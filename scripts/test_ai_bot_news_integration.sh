#!/bin/bash

# Test script for AI + Bot + News API Integration
# This script compiles and runs the integration test

echo "🚀 RicaXBulan AI + Bot + News API Integration Test"
echo "=================================================="

# Set up environment
export PROJECT_ROOT="/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0"
cd "$PROJECT_ROOT"

# Check if required dependencies are available
echo "📋 Checking dependencies..."

# Check for curl
if ! command -v curl &> /dev/null; then
    echo "❌ curl is required but not installed"
    exit 1
fi

# Check for nlohmann/json
if [ ! -f "/opt/homebrew/include/nlohmann/json.hpp" ] && [ ! -f "/usr/local/include/nlohmann/json.hpp" ]; then
    echo "⚠️  nlohmann/json not found, installing..."
    brew install nlohmann-json
fi

echo "✅ Dependencies checked"

# Create build directory
mkdir -p build/tests
cd build/tests

echo "🔨 Compiling integration test..."

# Compile the test
g++ -std=c++17 \
    -I"$PROJECT_ROOT" \
    -I"$PROJECT_ROOT/src" \
    -I/opt/homebrew/include \
    -I/usr/local/include \
    -L/opt/homebrew/lib \
    -L/usr/local/lib \
    "$PROJECT_ROOT/tests/ai_bot_news_integration_test.cpp" \
    -o ai_bot_news_integration_test \
    -lcurl \
    -pthread \
    -DTEST_MODE=1 \
    2>&1

if [ $? -eq 0 ]; then
    echo "✅ Compilation successful"
else
    echo "❌ Compilation failed"
    exit 1
fi

echo "🧪 Running integration test..."
echo ""

# Run the test
./ai_bot_news_integration_test

TEST_RESULT=$?

echo ""
if [ $TEST_RESULT -eq 0 ]; then
    echo "🎉 Integration test completed successfully!"
    echo ""
    echo "📊 Test Summary:"
    echo "✅ Bot News Initialization - PASSED"
    echo "✅ AI News Integration - PASSED"
    echo "✅ Multi-API Rotation - PASSED"
    echo "✅ Sentiment Analysis - PASSED"
    echo "✅ Real-Time Updates - PASSED"
    echo ""
    echo "🔗 AI and Bot are correctly connected to the new multi-API news system!"
else
    echo "❌ Integration test failed with exit code $TEST_RESULT"
    echo ""
    echo "🔍 Troubleshooting:"
    echo "1. Check internet connection for API access"
    echo "2. Verify API keys are valid"
    echo "3. Ensure all dependencies are installed"
    echo "4. Check logs for detailed error messages"
fi

# Cleanup
cd "$PROJECT_ROOT"

exit $TEST_RESULT

#!/bin/bash

# Test script for News Signal Strength Overlay
# This script verifies that the UI overlay is properly integrated

echo "🔍 Testing News Signal Strength Overlay Integration"
echo "================================================="

PROJECT_ROOT="/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0"
cd "$PROJECT_ROOT"

echo ""
echo "📋 Checking UI Integration Components..."

# 1. Check if NewsSignalOverlay component exists
echo ""
echo "1. Checking NewsSignalOverlay Component..."
if [ -f "qml/components/NewsSignalOverlay.qml" ]; then
    echo "   ✅ NewsSignalOverlay.qml exists"

    # Check if it has the required properties
    if grep -q "qmlBridge.hasActiveNews" qml/components/NewsSignalOverlay.qml && \
       grep -q "qmlBridge.topNewsHeadline" qml/components/NewsSignalOverlay.qml && \
       grep -q "qmlBridge.newsSentimentScore" qml/components/NewsSignalOverlay.qml; then
        echo "   ✅ Component binds to QML bridge properties"
    else
        echo "   ❌ Component missing QML bridge property bindings"
    fi

    # Check for glassmorphism styling
    if grep -q "FastBlur" qml/components/NewsSignalOverlay.qml && \
       grep -q "radius.*12" qml/components/NewsSignalOverlay.qml; then
        echo "   ✅ Glassmorphism styling implemented"
    else
        echo "   ⚠️  Glassmorphism styling may be incomplete"
    fi

    # Check for animations
    if grep -q "Behavior on" qml/components/NewsSignalOverlay.qml && \
       grep -q "NumberAnimation" qml/components/NewsSignalOverlay.qml; then
        echo "   ✅ Smooth animations implemented"
    else
        echo "   ⚠️  Animations may be missing"
    fi
else
    echo "   ❌ NewsSignalOverlay.qml missing"
    exit 1
fi

# 2. Check if MainPanel includes the overlay
echo ""
echo "2. Checking MainPanel Integration..."
if grep -q "NewsSignalOverlay" qml/components/MainPanel.qml; then
    echo "   ✅ NewsSignalOverlay included in MainPanel"

    if grep -q "z.*1000" qml/components/MainPanel.qml; then
        echo "   ✅ Overlay has proper z-index for visibility"
    else
        echo "   ⚠️  Z-index may not be set correctly"
    fi
else
    echo "   ❌ NewsSignalOverlay not included in MainPanel"
fi

# 3. Check QML Bridge properties
echo ""
echo "3. Checking QML Bridge Properties..."
if grep -q "Q_PROPERTY.*topNewsHeadline" src/terminal/qml_bridge.h && \
   grep -q "Q_PROPERTY.*newsSentimentScore" src/terminal/qml_bridge.h && \
   grep -q "Q_PROPERTY.*newsConfidenceLevel" src/terminal/qml_bridge.h; then
    echo "   ✅ QML Bridge has news signal properties"
else
    echo "   ❌ QML Bridge missing news signal properties"
fi

# Check if getter methods are implemented
if grep -q "QString.*topNewsHeadline.*const" src/terminal/qml_bridge.h && \
   grep -q "double.*newsSentimentScore.*const" src/terminal/qml_bridge.h; then
    echo "   ✅ QML Bridge getter methods declared"
else
    echo "   ❌ QML Bridge getter methods missing"
fi

# Check if implementation exists
if grep -q "QString QmlBridge::topNewsHeadline" src/terminal/qml_bridge.cpp && \
   grep -q "double QmlBridge::newsSentimentScore" src/terminal/qml_bridge.cpp; then
    echo "   ✅ QML Bridge getter methods implemented"
else
    echo "   ❌ QML Bridge getter methods not implemented"
fi

# 4. Check update mechanism
echo ""
echo "4. Checking News Update Mechanism..."
if grep -q "updateNewsSignalStrength" src/terminal/qml_bridge.h && \
   grep -q "updateNewsSignalStrength" src/terminal/qml_bridge.cpp; then
    echo "   ✅ News signal update method exists"
else
    echo "   ❌ News signal update method missing"
fi

# Check if timer is set up
if grep -q "newsSignalTimer" src/terminal/qml_bridge.cpp && \
   grep -q "start(30000)" src/terminal/qml_bridge.cpp; then
    echo "   ✅ Automatic news signal updates configured"
else
    echo "   ❌ Automatic news signal updates not configured"
fi

# 5. Check signal emissions
echo ""
echo "5. Checking Signal Emissions..."
if grep -q "topNewsHeadlineChanged" src/terminal/qml_bridge.h && \
   grep -q "newsSentimentScoreChanged" src/terminal/qml_bridge.h && \
   grep -q "newsConfidenceLevelChanged" src/terminal/qml_bridge.h; then
    echo "   ✅ News signal change signals declared"
else
    echo "   ❌ News signal change signals missing"
fi

# Check if signals are emitted in implementation
if grep -q "emit.*topNewsHeadlineChanged" src/terminal/qml_bridge.cpp && \
   grep -q "emit.*newsSentimentScoreChanged" src/terminal/qml_bridge.cpp; then
    echo "   ✅ News signal change signals emitted"
else
    echo "   ❌ News signal change signals not emitted"
fi

# 6. Check news data integration
echo ""
echo "6. Checking News Data Integration..."
if grep -q "getRecentNews" src/terminal/qml_bridge.cpp && \
   grep -q "latestNews" src/terminal/qml_bridge.cpp; then
    echo "   ✅ News data integration implemented"
else
    echo "   ❌ News data integration missing"
fi

# Check sentiment analysis
if grep -q "sentimentScore" src/terminal/qml_bridge.cpp && \
   grep -q "positiveWords" src/terminal/qml_bridge.cpp && \
   grep -q "negativeWords" src/terminal/qml_bridge.cpp; then
    echo "   ✅ Sentiment analysis logic implemented"
else
    echo "   ❌ Sentiment analysis logic missing"
fi

# 7. Check UI responsiveness features
echo ""
echo "7. Checking UI Responsiveness Features..."

# Check for confidence indicator
if grep -q "confidenceLevel.*width" qml/components/NewsSignalOverlay.qml; then
    echo "   ✅ Confidence level progress bar implemented"
else
    echo "   ⚠️  Confidence level indicator may be missing"
fi

# Check for sentiment color coding
if grep -q "sentimentColor" qml/components/NewsSignalOverlay.qml && \
   grep -q "#00cc66\|#cc3333\|#ffcc00" qml/components/NewsSignalOverlay.qml; then
    echo "   ✅ Sentiment color coding implemented"
else
    echo "   ⚠️  Sentiment color coding may be incomplete"
fi

# Check for pulse animation
if grep -q "pulseAnimation" qml/components/NewsSignalOverlay.qml && \
   grep -q "sentimentScore.*0.5" qml/components/NewsSignalOverlay.qml; then
    echo "   ✅ High-impact news pulse animation implemented"
else
    echo "   ⚠️  Pulse animation for high-impact news may be missing"
fi

# Summary
echo ""
echo "📊 News Signal Overlay Integration Summary"
echo "========================================="

# Count checks (simplified)
if [ -f "qml/components/NewsSignalOverlay.qml" ] && \
   grep -q "NewsSignalOverlay" qml/components/MainPanel.qml && \
   grep -q "Q_PROPERTY.*topNewsHeadline" src/terminal/qml_bridge.h && \
   grep -q "updateNewsSignalStrength" src/terminal/qml_bridge.cpp; then
    echo "✅ Core Integration: PASSED"
    echo "✅ UI Component: CREATED"
    echo "✅ Data Binding: CONNECTED"
    echo "✅ Real-Time Updates: CONFIGURED"
    echo ""
    echo "🎉 News Signal Strength Overlay successfully integrated!"
    echo ""
    echo "📈 Features Available:"
    echo "   • Real-time news headline display"
    echo "   • Sentiment analysis with color coding"
    echo "   • Confidence level indicator"
    echo "   • Glassmorphism UI design"
    echo "   • Smooth animations and transitions"
    echo "   • Automatic 30-second updates"
    echo "   • High-impact news pulse effects"
    echo ""
    echo "🚀 UI Enhancement Complete!"
    echo ""
    echo "💡 Next Steps:"
    echo "   1. Launch the application to see the overlay"
    echo "   2. Connect to PyQuotex to get real news data"
    echo "   3. Monitor the overlay for live news updates"
    echo "   4. Test sentiment analysis accuracy"
else
    echo "❌ Some integration components are missing or misconfigured"
    echo ""
    echo "🔧 Please check the issues listed above and run this script again"
    exit 1
fi

echo ""

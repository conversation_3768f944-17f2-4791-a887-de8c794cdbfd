# Multi-API News Integration System

## Overview

The RicaXBulan trading bot now features a sophisticated multi-API news integration system that provides real-time financial news, sentiment analysis, and economic calendar data from multiple sources with intelligent failover and caching.

## Supported APIs

### 1. Marketaux (Primary)
- **URL**: https://api.marketaux.com/v1
- **Rate Limit**: 1000 calls/hour
- **Features**: 
  - Comprehensive financial news with entity recognition
  - Symbol-specific filtering
  - Relevance scoring
  - Multiple language support
- **Best For**: Primary news source with high-quality entity extraction

### 2. Finnhub (Secondary)
- **URL**: https://finnhub.io/api/v1
- **Rate Limit**: 60 calls/minute (free tier)
- **Features**:
  - Company-specific news
  - Sentiment analysis API
  - Real-time market data
- **Best For**: Sentiment analysis and company-specific news

### 3. NewsData.io (Backup)
- **URL**: https://newsdata.io/api/1
- **Rate Limit**: 200 calls/day (free tier)
- **Features**:
  - General business news
  - Multiple categories
  - Archive access
- **Best For**: Backup news source when primary APIs are unavailable

## Key Features

### 🔄 Intelligent API Rotation
```cpp
// Automatic failover based on:
// 1. Remaining API quota
// 2. Response time performance
// 3. Error count history
// 4. Provider health status

if (remainingUsage["marketaux"] > 0) {
    useMarketaux();
} else if (remainingUsage["finnhub"] > 0) {
    useFinnhub();
} else if (remainingUsage["newsdata"] > 0) {
    useNewsdata();
} else {
    fallbackToCachedNews();
}
```

### 📊 Multi-Level Caching
- **Redis Cache**: Hot data with configurable TTL
- **Memory Cache**: Frequently accessed data
- **Fallback Cache**: Expired data for emergency use
- **Cache Keys**: Structured for efficient retrieval

### 🎯 Smart Filtering
- **Symbol-based**: Filter news by trading pairs/stocks
- **Impact-based**: Focus on high/medium/low impact news
- **Keyword-based**: Custom keyword filtering
- **Time-based**: Recent news prioritization

### 📈 Sentiment Analysis
- **Real-time**: Live sentiment scoring (-1.0 to 1.0)
- **Multi-source**: Combine multiple sentiment signals
- **Trading Integration**: Direct impact on trading decisions
- **Keyword Analysis**: Positive/negative keyword detection

## Configuration

### API Keys Setup
Edit `config/news_api_config.json`:

```json
{
  "news_apis": {
    "marketaux": {
      "api_key": "your_marketaux_key",
      "enabled": true,
      "priority": 1
    },
    "finnhub": {
      "api_key": "your_finnhub_key", 
      "enabled": true,
      "priority": 2
    },
    "newsdata": {
      "api_key": "your_newsdata_key",
      "enabled": true,
      "priority": 3
    }
  }
}
```

### Cache Settings
```json
{
  "cache_settings": {
    "news_ttl_seconds": 300,
    "sentiment_ttl_seconds": 600,
    "economic_calendar_ttl_seconds": 3600,
    "enable_redis_cache": true
  }
}
```

## Usage Examples

### Basic News Fetching
```cpp
#include "src/api/news/news_api_client.h"

NewsAPIClient client;
client.initialize(marketauxKey, finnhubKey, newsdataKey);

// Fetch latest financial news
auto news = client.fetchLatestNews();

for (const auto& item : news) {
    std::cout << item.title << std::endl;
    std::cout << "Impact: " << static_cast<int>(item.impact) << std::endl;
    std::cout << "Source: " << item.source << std::endl;
}
```

### Symbol-Specific News
```cpp
// Get news for specific trading pairs
std::vector<std::string> symbols = {"EUR/USD", "AAPL", "BTC/USD"};
auto symbolNews = client.fetchNewsBySymbols(symbols);
```

### Sentiment Analysis
```cpp
// Analyze sentiment for a specific symbol
auto sentiment = client.fetchSentimentAnalysis("AAPL");

std::cout << "Sentiment Score: " << sentiment.sentimentScore << std::endl;
std::cout << "Market Impact: " << sentiment.marketImpact << std::endl;
std::cout << "Trading Recommended: " << sentiment.isTradingRecommended << std::endl;
```

### Economic Calendar
```cpp
// Fetch upcoming economic events
auto events = client.fetchEconomicCalendar();

for (const auto& event : events) {
    std::cout << event.title << std::endl;
    std::cout << "Forecast: " << event.forecast << std::endl;
    std::cout << "Previous: " << event.previous << std::endl;
}
```

### Advanced Filtering
```cpp
NewsFilter filter;
filter.symbols = {"EUR/USD", "GBP/USD"};
filter.minImpact = Models::NewsImpact::MEDIUM;
filter.keywords = {"fed", "interest rate", "inflation"};

auto filteredNews = client.fetchLatestNews(filter);
```

## Trading Integration

### Sentiment-Based Signals
```cpp
// Use news sentiment in trading decisions
auto sentiment = client.fetchSentimentAnalysis("EUR/USD");

if (sentiment.sentimentScore > 0.3) {
    // Bullish sentiment - consider CALL option
    tradingEngine.suggestTrade("EUR/USD", TradeDirection::CALL, sentiment.marketImpact);
} else if (sentiment.sentimentScore < -0.3) {
    // Bearish sentiment - consider PUT option
    tradingEngine.suggestTrade("EUR/USD", TradeDirection::PUT, sentiment.marketImpact);
}
```

### High-Impact News Alerts
```cpp
// Monitor for high-impact news
auto news = client.fetchLatestNews();

for (const auto& item : news) {
    if (item.impact == Models::NewsImpact::HIGH) {
        // Pause trading or adjust risk parameters
        tradingEngine.adjustVolatilityMultiplier(1.5);
        
        // Send alert
        alertSystem.sendHighImpactNewsAlert(item);
    }
}
```

## Monitoring & Metrics

### API Usage Statistics
```cpp
auto stats = client.getAPIUsageStats();

for (const auto& [provider, creds] : stats) {
    std::cout << "Provider: " << providerToString(provider) << std::endl;
    std::cout << "Remaining Calls: " << creds.remainingCalls << std::endl;
    std::cout << "Error Count: " << creds.errorCount << std::endl;
    std::cout << "Response Time: " << creds.responseTimeMs << "ms" << std::endl;
}
```

### Health Monitoring
```cpp
// Log API metrics every 15 minutes
client.logAPIMetrics();

// Enable/disable providers based on performance
if (errorCount > 10) {
    client.setProviderEnabled(NewsAPIProvider::FINNHUB, false);
}
```

## Testing

### Run News API Tests
```bash
# Compile and run the test
cd tests
g++ -std=c++17 news_api_test.cpp -o news_api_test -lcurl -lnlohmann_json
./news_api_test
```

### Expected Output
```
=== Testing Latest Financial News ===
Fetched 25 news items:

1. Fed Signals Potential Rate Cut in December
   Source: Marketaux
   Impact: HIGH
   Symbol: USD
   Description: Federal Reserve officials hint at possible interest rate reduction...

=== Testing Sentiment Analysis ===
Analyzing sentiment for AAPL:
Sentiment Score: 0.65
Market Impact: 0.65
Volatility Expectation: 0.52
Trading Recommended: Yes
Keywords: bullish, growth
```

## Error Handling

### Automatic Failover
- **API Quota Exhausted**: Automatically switch to next available provider
- **Network Errors**: Retry with exponential backoff
- **Rate Limiting**: Intelligent request spacing
- **Cache Fallback**: Use cached data when all APIs fail

### Error Recovery
```cpp
try {
    auto news = client.fetchLatestNews();
} catch (const std::exception& e) {
    // Automatic fallback to cached data
    Logger::error("News API error: " + std::string(e.what()));
    // System continues with cached data
}
```

## Performance Optimization

### Caching Strategy
- **News**: 5-minute TTL for real-time updates
- **Sentiment**: 10-minute TTL for stability
- **Economic Calendar**: 1-hour TTL for events

### Rate Limiting
- **Marketaux**: 100ms between requests
- **Finnhub**: 1000ms between requests  
- **NewsData**: 500ms between requests

### Memory Management
- Automatic cleanup of expired cache entries
- Configurable memory limits
- Efficient JSON parsing and serialization

## Troubleshooting

### Common Issues

1. **API Key Invalid**
   ```
   Error: API Error [Marketaux]: Unauthorized
   Solution: Check API key in config/news_api_config.json
   ```

2. **Rate Limit Exceeded**
   ```
   Warning: Rate limit exceeded for Finnhub, switching to NewsData
   Solution: Normal behavior - automatic failover active
   ```

3. **No News Data**
   ```
   Info: All APIs unavailable, using fallback cache
   Solution: Check internet connection and API status
   ```

### Debug Mode
```cpp
// Enable detailed logging
Logger::getInstance().setLevel(LogLevel::DEBUG);
client.logAPIMetrics();
```

## Future Enhancements

- [ ] Machine learning-based sentiment analysis
- [ ] Real-time WebSocket news feeds
- [ ] Custom news source integration
- [ ] Advanced natural language processing
- [ ] Multi-language news support
- [ ] News impact prediction models

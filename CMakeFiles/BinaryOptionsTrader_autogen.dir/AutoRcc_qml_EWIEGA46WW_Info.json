{"BUILD_DIR": "/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen", "CMAKE_BINARY_DIR": "/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0", "CMAKE_CURRENT_BINARY_DIR": "/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0", "CMAKE_CURRENT_SOURCE_DIR": "/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0", "CMAKE_SOURCE_DIR": "/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0", "CROSS_CONFIG": false, "GENERATOR": "Unix Makefiles", "INCLUDE_DIR": "/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/include", "INPUTS": ["/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/qml/main.qml", "/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/qml/popups/SettingsPopup.qml", "/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/qml/popups/TradePopup.qml", "/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/qml/popups/IndicatorPopup.qml", "/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/qml/components/SideBar.qml", "/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/qml/components/SentimentPanel.qml", "/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/qml/components/PriceChart.qml", "/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/qml/components/PinCodeDialog.qml", "/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/qml/components/MainPanel.qml", "/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/qml/components/StrategyTestingPanel.qml", "/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/qml/components/IndicatorsPanel.qml", "/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/qml/components/OrderPanel.qml", "/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/qml/components/TradeControls.qml", "/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/qml/components/NewsSignalOverlay.qml", "/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/qml/components/TopNavBar.qml", "/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/qml/components/PatternFeedPanel.qml", "/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/qml/components/SettingsPanel.qml", "/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/qml/components/Candle.qml", "/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/qml/styles/AppStyle.qml", "/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/qml/styles/AppTheme.qml"], "LOCK_FILE": "/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles/BinaryOptionsTrader_autogen.dir/AutoRcc_qml_EWIEGA46WW_Lock.lock", "MULTI_CONFIG": false, "OPTIONS": ["-name", "qml"], "OUTPUT_CHECKSUM": "EWIEGA46WW", "OUTPUT_NAME": "qrc_qml.cpp", "RCC_EXECUTABLE": "/opt/homebrew/share/qt/libexec/rcc", "RCC_LIST_OPTIONS": ["--list"], "SETTINGS_FILE": "/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles/BinaryOptionsTrader_autogen.dir/AutoRcc_qml_EWIEGA46WW_Used.txt", "SOURCE": "/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/qml.qrc", "USE_BETTER_GRAPH": true, "VERBOSITY": 0}
CMakeFiles/BinaryOptionsTrader.dir/BinaryOptionsTrader_autogen/mocs_compilation.cpp.o: \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/mocs_compilation.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3KI4N4FQU5/moc_advanced_ai_model.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3KI4N4FQU5/../../src/ai/ml/advanced_ai_model.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/memory \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__config \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__config_site \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__configuration/abi.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__configuration/compiler.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__configuration/platform.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__configuration/availability.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__configuration/language.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__memory/addressof.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__memory/align.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__cstddef/size_t.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__memory/allocator.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__cstddef/ptrdiff_t.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__memory/allocate_at_least.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__memory/allocator_traits.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__fwd/memory.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__memory/construct_at.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__assert \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__assertion_handler \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__verbose_abort \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__iterator/access.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__new/placement_new_delete.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/enable_if.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_array.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/integral_constant.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__utility/declval.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__utility/forward.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_reference.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/remove_reference.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__utility/move.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/conditional.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_constructible.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/add_lvalue_reference.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_referenceable.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_same.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/add_rvalue_reference.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_nothrow_constructible.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__undef_macros \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__memory/pointer_traits.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/conjunction.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/decay.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/add_pointer.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_void.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_function.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/remove_cv.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/remove_extent.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_class.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/void_t.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/detected_or.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_empty.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/make_unsigned.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/copy_cv.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_enum.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_integral.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_unsigned.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_arithmetic.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_floating_point.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/type_list.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/limits \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_signed.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/version \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/type_traits \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/add_cv_quals.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/aligned_storage.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/aligned_union.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/alignment_of.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/common_type.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/remove_cvref.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/type_identity.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__utility/empty.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/extent.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/has_virtual_destructor.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_abstract.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_assignable.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_base_of.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_compound.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_fundamental.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_null_pointer.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__cstddef/nullptr_t.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_const.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_convertible.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_destructible.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/remove_all_extents.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_literal_type.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_member_pointer.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_nothrow_assignable.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_nothrow_destructible.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_object.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_pod.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_pointer.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_polymorphic.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_scalar.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_standard_layout.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_trivial.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_trivially_assignable.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_trivially_constructible.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_trivially_copyable.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/cstdint \
  /opt/homebrew/Cellar/llvm/20.1.4_1/lib/clang/20/include/stdint.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/stdint.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_int8_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_int16_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_int32_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_int64_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/_types/_uint8_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/_types/_uint16_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/_types/_uint32_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/_types/_uint64_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/cdefs.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_symbol_aliasing.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_posix_availability.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/machine/_types.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/arm/_types.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_pthread/_pthread_types.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_intptr_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_uintptr_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/_types/_intmax_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/_types/_uintmax_t.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_trivially_destructible.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_union.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_volatile.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/make_signed.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/rank.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/remove_const.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/remove_pointer.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/remove_volatile.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/result_of.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/invoke.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_core_convertible.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_reference_wrapper.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__fwd/functional.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/nat.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/underlying_type.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_final.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/disjunction.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/has_unique_object_representation.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_aggregate.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_swappable.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/negation.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__new/allocate.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__cstddef/max_align_t.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/stddef.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/lib/clang/20/include/stddef.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/stddef.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/lib/clang/20/include/__stddef_header_macro.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/lib/clang/20/include/__stddef_ptrdiff_t.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/lib/clang/20/include/__stddef_size_t.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/lib/clang/20/include/__stddef_wchar_t.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/lib/clang/20/include/__stddef_null.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/lib/clang/20/include/__stddef_nullptr_t.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/lib/clang/20/include/__stddef_max_align_t.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/lib/clang/20/include/__stddef_offsetof.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__new/align_val_t.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__new/global_new_delete.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__new/exceptions.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__exception/exception.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__new/nothrow_t.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__utility/element_count.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_constant_evaluated.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__memory/allocator_arg_t.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__memory/uses_allocator.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__memory/auto_ptr.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__memory/inout_ptr.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__memory/shared_ptr.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__compare/compare_three_way.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__compare/three_way_comparable.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__compare/common_comparison_category.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__compare/ordering.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__concepts/common_reference_with.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__concepts/convertible_to.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__concepts/same_as.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/common_reference.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/copy_cvref.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__concepts/equality_comparable.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__concepts/boolean_testable.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/make_const_lvalue_ref.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__concepts/totally_ordered.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__functional/binary_function.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__functional/operations.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__functional/unary_function.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/desugars_to.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__functional/reference_wrapper.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__compare/synth_three_way.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__functional/weak_result_type.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__fwd/ostream.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__fwd/string.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__fwd/memory_resource.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__memory/allocation_guard.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__memory/allocator_destructor.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__memory/compressed_pair.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/datasizeof.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__memory/shared_count.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/typeinfo \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/cstddef \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__cstddef/byte.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__fwd/byte.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/cstdlib \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/stdlib.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/stdlib.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/_stdlib.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/Availability.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/AvailabilityVersions.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/AvailabilityInternal.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/AvailabilityInternalLegacy.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/_bounds.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/_types.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/wait.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_pid_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_id_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/signal.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/appleapiopts.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/machine/signal.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/arm/signal.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/machine/_mcontext.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/arm/_mcontext.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/machine/_structs.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/arm/_structs.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/machine/types.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/arm/types.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_u_int8_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_u_int16_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_u_int32_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_u_int64_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_pthread/_pthread_attr_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_sigaltstack.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_ucontext.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_sigset_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_size_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_uid_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/resource.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_timeval.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/machine/endian.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/arm/endian.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_endian.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/machine/_endian.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/arm/_endian.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/__endian.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/libkern/_OSByteOrder.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/libkern/arm/_OSByteOrder.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/alloca.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_ct_rune_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_rune_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_wchar_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_null.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/malloc/_malloc.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/malloc/_malloc_type.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/malloc/_ptrcheck.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/_abort.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_dev_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_mode_t.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__memory/uninitialized_algorithms.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/copy.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/copy_move_common.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/unwrap_iter.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__iterator/iterator_traits.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__concepts/arithmetic.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_signed_integer.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_unsigned_integer.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__concepts/constructible.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__concepts/destructible.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__concepts/copyable.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__concepts/assignable.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__concepts/movable.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__concepts/swappable.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__concepts/class_or_enum.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__utility/exchange.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__utility/swap.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__fwd/pair.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__fwd/tuple.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__iterator/incrementable_traits.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_primary_template.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_valid_expansion.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__iterator/readable_traits.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/unwrap_range.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__iterator/concepts.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__concepts/derived_from.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__concepts/invocable.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__functional/invoke.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__concepts/predicate.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__concepts/regular.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__concepts/semiregular.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__concepts/relation.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__iterator/iter_move.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__iterator/next.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__iterator/advance.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__utility/convert_to_integral.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__utility/unreachable.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__utility/pair.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__concepts/different_from.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__fwd/array.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__tuple/tuple_indices.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__utility/integer_sequence.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__tuple/tuple_like_no_subrange.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__fwd/complex.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__tuple/tuple_size.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__tuple/tuple_types.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_implicitly_default_constructible.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_trivially_relocatable.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/unwrap_ref.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__utility/piecewise_construct.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__string/constexpr_c_functions.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_always_bitcastable.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_equality_comparable.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_trivially_lexicographically_comparable.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__utility/is_pointer_in_range.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/comp.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__utility/is_valid_range.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/for_each_segment.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__iterator/segmented_iterator.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/min.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/comp_ref_type.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/min_element.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__functional/identity.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_callable.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/initializer_list \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/move.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/iterator_operations.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/iter_swap.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/ranges_iterator_concept.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__iterator/distance.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__ranges/access.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__ranges/enable_borrowed_range.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__utility/auto_cast.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__ranges/concepts.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__ranges/data.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__ranges/enable_view.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__ranges/size.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__iterator/iter_swap.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__iterator/prev.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__iterator/reverse_iterator.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__compare/compare_three_way_result.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__iterator/iterator.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__ranges/subrange.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__fwd/subrange.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__ranges/dangling.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__ranges/view_interface.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__ranges/empty.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__tuple/tuple_element.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_unbounded_array.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__utility/exception_guard.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__memory/unique_ptr.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__functional/hash.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/cstring \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/string.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/string.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/_string.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_ssize_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/_strings.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__memory/array_cookie.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/dependent_type.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_bounded_array.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__utility/private_constructor_tag.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__atomic/memory_order.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_specialization.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/tuple \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__tuple/find_index.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__tuple/ignore.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__tuple/make_tuple_types.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__tuple/sfinae_helpers.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__tuple/tuple_like_ext.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/lazy.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/maybe_const.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/compare \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/cmath \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__math/hypot.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__math/abs.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__math/exponential_functions.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/promote.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__math/min_max.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__math/roots.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__math/special_functions.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__math/copysign.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__math/traits.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/math.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/math.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__math/error_functions.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__math/fdim.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__math/fma.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__math/gamma.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__math/hyperbolic_functions.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__math/inverse_hyperbolic_functions.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__math/inverse_trigonometric_functions.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__math/logarithms.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__math/modulo.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__math/remainder.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__math/rounding_functions.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__math/trigonometric_functions.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/exception \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__exception/exception_ptr.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__exception/operations.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__exception/nested_exception.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__exception/terminate.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/new \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__new/new_handler.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__new/interference_size.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__new/launder.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/iosfwd \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__fwd/fstream.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__fwd/ios.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__fwd/istream.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__fwd/sstream.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__fwd/streambuf.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__std_mbstate_t.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__mbstate_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_mbstate_t.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/utility \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__utility/rel_ops.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__utility/as_const.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__utility/in_place.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__memory/out_ptr.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__memory/raw_storage_iterator.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__memory/temporary_buffer.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__memory/unique_temporary_buffer.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/atomic \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__atomic/aliases.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__atomic/atomic.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__atomic/atomic_sync.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__atomic/contention_t.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__atomic/support.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__atomic/support/c11.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__atomic/to_gcc_order.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__chrono/duration.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/ratio \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/climits \
  /opt/homebrew/Cellar/llvm/20.1.4_1/lib/clang/20/include/limits.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/limits.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/machine/limits.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/arm/limits.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/arm/_limits.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/syslimits.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__thread/poll_with_backoff.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__chrono/high_resolution_clock.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__chrono/steady_clock.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__chrono/time_point.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__chrono/system_clock.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/ctime \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/time.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/_time.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_clock_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_time_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_timespec.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__atomic/check_memory_order.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__atomic/is_always_lock_free.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__atomic/atomic_lock_free.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__atomic/atomic_flag.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__thread/support.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__thread/support/pthread.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__chrono/convert_to_timespec.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/errno.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/errno.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/errno.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/pthread.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/pthread/sched.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/pthread/pthread_impl.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_pthread/_pthread_cond_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_pthread/_pthread_condattr_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_pthread/_pthread_key_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_pthread/_pthread_mutex_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_pthread/_pthread_mutexattr_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_pthread/_pthread_once_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_pthread/_pthread_rwlock_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_pthread/_pthread_rwlockattr_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_pthread/_pthread_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/pthread/qos.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/qos.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_mach_port_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sched.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__atomic/atomic_init.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__atomic/fence.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__atomic/kill_dependency.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/concepts \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/iterator \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__iterator/back_insert_iterator.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__iterator/front_insert_iterator.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__iterator/insert_iterator.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__iterator/istream_iterator.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__iterator/default_sentinel.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__iterator/istreambuf_iterator.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__string/char_traits.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/fill_n.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__fwd/bit_reference.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/find.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/find_segment_if.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__bit/countr.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__bit/rotate.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__bit/invert_if.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/cwchar \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/cwctype \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/cctype \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/ctype.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/ctype.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/_ctype.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/runetype.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_wint_t.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/wctype.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/wctype.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/_wctype.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/_types/_wctrans_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/__wctype.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/___wctype.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/_types/_wctype_t.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/wchar.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/wchar.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/_wchar.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/lib/clang/20/include/stdarg.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/lib/clang/20/include/__stdarg_header_macro.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/lib/clang/20/include/__stdarg___gnuc_va_list.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/lib/clang/20/include/__stdarg_va_list.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/lib/clang/20/include/__stdarg_va_arg.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/lib/clang/20/include/__stdarg___va_copy.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/lib/clang/20/include/__stdarg_va_copy.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/stdio.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/stdio.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/_stdio.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_va_list.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/stdio.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/_printf.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_seek_set.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/_ctermid.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_off_t.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/find_end.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/find_first_of.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/cstdio \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__iterator/move_iterator.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__iterator/move_sentinel.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__iterator/ostream_iterator.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__iterator/ostreambuf_iterator.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__iterator/wrap_iter.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__iterator/reverse_access.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__iterator/data.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__iterator/empty.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__iterator/size.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/variant \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__fwd/variant.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__utility/forward_like.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__variant/monostate.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/stdexcept \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/vector \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__vector/comparison.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/equal.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/lexicographical_compare.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/mismatch.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/simd_utils.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__bit/bit_cast.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__bit/countl.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__iterator/aliasing_iterator.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/lexicographical_compare_three_way.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/three_way_comp_ref_type.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__fwd/vector.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__vector/swap.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__vector/vector.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/copy_n.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/max.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/max_element.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/move_backward.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/ranges_copy_n.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/in_out_result.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/ranges_copy.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__iterator/unreachable_sentinel.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/rotate.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/swap_ranges.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__debug_utils/sanitizers.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__format/enable_insertable.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__iterator/bounded_iter.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__memory/noexcept_move_assign_container.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__memory/swap_allocator.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__memory/temp_value.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__ranges/container_compatible_range.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__ranges/from_range.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__split_buffer \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_allocator.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__vector/container_traits.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/container_traits.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__vector/vector_bool.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__bit_reference \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__vector/pmr.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__memory_resource/polymorphic_allocator.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__memory_resource/memory_resource.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/algorithm \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/adjacent_find.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/all_of.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/any_of.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/binary_search.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/lower_bound.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/half_positive.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/copy_backward.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/copy_if.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/count.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__bit/popcount.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/count_if.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/equal_range.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/upper_bound.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/fill.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/find_if.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/find_if_not.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/for_each.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__ranges/movable_box.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/optional \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/generate.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/generate_n.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/includes.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/inplace_merge.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__memory/destruct_n.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/is_heap.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/is_heap_until.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/is_partitioned.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/is_permutation.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/is_sorted.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/is_sorted_until.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/make_heap.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/sift_down.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/merge.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/minmax.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/minmax_element.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/next_permutation.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/reverse.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/none_of.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/nth_element.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/sort.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/partial_sort.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/sort_heap.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/pop_heap.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/push_heap.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__debug_utils/strict_weak_ordering_check.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__debug_utils/randomize_range.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__bit/blsr.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__functional/ranges_operations.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/partial_sort_copy.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/make_projected.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/partition.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/partition_copy.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/partition_point.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/prev_permutation.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/remove.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/remove_copy.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/remove_copy_if.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/remove_if.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/replace.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/replace_copy.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/replace_copy_if.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/replace_if.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/reverse_copy.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/rotate_copy.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/search.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/search_n.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/set_difference.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/set_intersection.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/set_symmetric_difference.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/set_union.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/shuffle.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__random/uniform_int_distribution.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__random/is_valid.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__random/log2.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/stable_partition.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/stable_sort.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/radix_sort.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__bit/bit_log2.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__numeric/partial_sum.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/transform.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/unique.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/unique_copy.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/clamp.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/for_each_n.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/pstl.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__algorithm/sample.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/bit \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/array \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__iterator/static_bounded_iter.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/cerrno \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/clocale \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/locale.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/_locale.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/_types/_locale_t.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/locale \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__locale \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__locale_dir/locale_base_api.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__locale_dir/support/apple.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__locale_dir/support/bsd_like.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/xlocale.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/_xlocale.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/__xlocale.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/_mb_cur_max.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/xlocale/_ctype.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/xlocale/___wctype.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/xlocale/_stdio.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/xlocale/_stdlib.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/xlocale/_string.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/xlocale/_time.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/xlocale/_wchar.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/xlocale/_wctype.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__mutex/once_flag.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__utility/no_destroy.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/string \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__ios/fpos.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__string/extern_template_lists.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__utility/scope_guard.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/string_view \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__fwd/string_view.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__locale_dir/pad_and_output.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/ios \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__system_error/error_category.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__system_error/error_code.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__system_error/errc.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__system_error/error_condition.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__system_error/system_error.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/mutex \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__condition_variable/condition_variable.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__mutex/mutex.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__mutex/unique_lock.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__mutex/tag_types.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__system_error/throw_system_error.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__mutex/lock_guard.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__thread/id.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/system_error \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/streambuf \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/nl_types.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/types.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_u_char.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_u_short.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_u_int.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_caddr_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_blkcnt_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_blksize_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_gid_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_in_addr_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_in_port_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_ino_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_ino64_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_key_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_nlink_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_useconds_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_suseconds_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_rsize_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_errno_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_fd_def.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_fd_setsize.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_fd_set.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_fd_clr.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_fd_zero.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_fd_isset.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_fd_copy.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_fsblkcnt_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_fsfilcnt_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/_types/_nl_item.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/cstdarg \
  /opt/homebrew/lib/QtCore.framework/Headers/QObject \
  /opt/homebrew/lib/QtCore.framework/Headers/qobject.h \
  /opt/homebrew/include/QtCore/qobjectdefs.h \
  /opt/homebrew/include/QtCore/qnamespace.h \
  /opt/homebrew/include/QtCore/qglobal.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/assert.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/_assert.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/_static_assert.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/stdbool.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/lib/clang/20/include/stdbool.h \
  /opt/homebrew/include/QtCore/qtcoreglobal.h \
  /opt/homebrew/include/QtCore/qtversionchecks.h \
  /opt/homebrew/include/QtCore/qtconfiginclude.h \
  /opt/homebrew/include/QtCore/qconfig.h \
  /opt/homebrew/include/QtCore/qtcore-config.h \
  /opt/homebrew/include/QtCore/qtconfigmacros.h \
  /opt/homebrew/include/QtCore/qtdeprecationdefinitions.h \
  /opt/homebrew/include/QtCore/qcompilerdetection.h \
  /opt/homebrew/include/QtCore/qprocessordetection.h \
  /opt/homebrew/include/QtCore/qsystemdetection.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/TargetConditionals.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/AvailabilityMacros.h \
  /opt/homebrew/include/QtCore/qtcoreexports.h \
  /opt/homebrew/include/QtCore/qtdeprecationmarkers.h \
  /opt/homebrew/include/QtCore/qtclasshelpermacros.h \
  /opt/homebrew/include/QtCore/qtpreprocessorsupport.h \
  /opt/homebrew/include/QtCore/qassert.h \
  /opt/homebrew/include/QtCore/qtnoop.h \
  /opt/homebrew/include/QtCore/qtypes.h \
  /opt/homebrew/include/QtCore/qtversion.h \
  /opt/homebrew/include/QtCore/qtypeinfo.h \
  /opt/homebrew/include/QtCore/qcontainerfwd.h \
  /opt/homebrew/include/QtCore/qsysinfo.h \
  /opt/homebrew/include/QtCore/qlogging.h \
  /opt/homebrew/include/QtCore/qflags.h \
  /opt/homebrew/include/QtCore/qcompare_impl.h \
  /opt/homebrew/include/QtCore/qatomic.h \
  /opt/homebrew/include/QtCore/qbasicatomic.h \
  /opt/homebrew/include/QtCore/qatomic_cxx11.h \
  /opt/homebrew/include/QtCore/qgenericatomic.h \
  /opt/homebrew/include/QtCore/qyieldcpu.h \
  /opt/homebrew/include/QtCore/qconstructormacros.h \
  /opt/homebrew/include/QtCore/qdarwinhelpers.h \
  /opt/homebrew/include/QtCore/qexceptionhandling.h \
  /opt/homebrew/include/QtCore/qforeach.h \
  /opt/homebrew/include/QtCore/qttypetraits.h \
  /opt/homebrew/include/QtCore/qfunctionpointer.h \
  /opt/homebrew/include/QtCore/qglobalstatic.h \
  /opt/homebrew/include/QtCore/qmalloc.h \
  /opt/homebrew/include/QtCore/qminmax.h \
  /opt/homebrew/include/QtCore/qnumeric.h \
  /opt/homebrew/include/QtCore/qoverload.h \
  /opt/homebrew/include/QtCore/qswap.h \
  /opt/homebrew/include/QtCore/qtenvironmentvariables.h \
  /opt/homebrew/include/QtCore/qtresource.h \
  /opt/homebrew/include/QtCore/qttranslation.h \
  /opt/homebrew/include/QtCore/qversiontagging.h \
  /opt/homebrew/include/QtCore/qcompare.h \
  /opt/homebrew/include/QtCore/qstdlibdetection.h \
  /opt/homebrew/include/QtCore/qcomparehelpers.h \
  /opt/homebrew/include/QtCore/q20type_traits.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/functional \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__functional/binary_negate.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__functional/bind.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__functional/binder1st.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__functional/binder2nd.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__functional/mem_fn.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__functional/mem_fun_ref.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__functional/pointer_to_binary_function.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__functional/pointer_to_unary_function.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__functional/unary_negate.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__functional/function.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/strip_signature.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__functional/boyer_moore_searcher.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/unordered_map \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__functional/is_transparent.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__hash_table \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/can_extract_key.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/remove_const_ref.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__iterator/erase_if_container.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__iterator/ranges_iterator_traits.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__node_handle \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__functional/default_searcher.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__functional/not_fn.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__functional/perfect_forward.h \
  /opt/homebrew/include/QtCore/qtmetamacros.h \
  /opt/homebrew/include/QtCore/qobjectdefs_impl.h \
  /opt/homebrew/include/QtCore/qfunctionaltools_impl.h \
  /opt/homebrew/include/QtCore/qstring.h \
  /opt/homebrew/include/QtCore/qchar.h \
  /opt/homebrew/include/QtCore/qstringview.h \
  /opt/homebrew/include/QtCore/qbytearray.h \
  /opt/homebrew/include/QtCore/qrefcount.h \
  /opt/homebrew/include/QtCore/qarraydata.h \
  /opt/homebrew/include/QtCore/qpair.h \
  /opt/homebrew/include/QtCore/qarraydatapointer.h \
  /opt/homebrew/include/QtCore/qarraydataops.h \
  /opt/homebrew/include/QtCore/qcontainertools_impl.h \
  /opt/homebrew/include/QtCore/qxptype_traits.h \
  /opt/homebrew/include/QtCore/q20functional.h \
  /opt/homebrew/include/QtCore/q20memory.h \
  /opt/homebrew/include/QtCore/q17memory.h \
  /opt/homebrew/include/QtCore/qbytearrayalgorithms.h \
  /opt/homebrew/include/QtCore/qbytearrayview.h \
  /opt/homebrew/include/QtCore/qstringfwd.h \
  /opt/homebrew/include/QtCore/qstringliteral.h \
  /opt/homebrew/include/QtCore/qstringalgorithms.h \
  /opt/homebrew/include/QtCore/qlatin1stringview.h \
  /opt/homebrew/include/QtCore/qanystringview.h \
  /opt/homebrew/include/QtCore/qutf8stringview.h \
  /opt/homebrew/include/QtCore/qstringtokenizer.h \
  /opt/homebrew/include/QtCore/qstringbuilder.h \
  /opt/homebrew/include/QtCore/qstringconverter.h \
  /opt/homebrew/include/QtCore/qstringconverter_base.h \
  /opt/homebrew/include/QtCore/qlist.h \
  /opt/homebrew/include/QtCore/qhashfunctions.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/numeric \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__numeric/accumulate.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__numeric/adjacent_difference.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__numeric/inner_product.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__numeric/iota.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__numeric/exclusive_scan.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__numeric/gcd_lcm.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__numeric/inclusive_scan.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__numeric/pstl.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__numeric/reduce.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__numeric/transform_exclusive_scan.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__numeric/transform_inclusive_scan.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__numeric/transform_reduce.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/execution \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_execution_policy.h \
  /opt/homebrew/include/QtCore/qiterator.h \
  /opt/homebrew/include/QtCore/qbytearraylist.h \
  /opt/homebrew/include/QtCore/qstringlist.h \
  /opt/homebrew/include/QtCore/qalgorithms.h \
  /opt/homebrew/include/QtCore/qstringmatcher.h \
  /opt/homebrew/include/QtCore/qscopedpointer.h \
  /opt/homebrew/include/QtCore/qmetatype.h \
  /opt/homebrew/include/QtCore/qdatastream.h \
  /opt/homebrew/include/QtCore/qiodevicebase.h \
  /opt/homebrew/include/QtCore/qfloat16.h \
  /opt/homebrew/include/QtCore/qmath.h \
  /opt/homebrew/include/QtCore/qtformat_impl.h \
  /opt/homebrew/include/QtCore/qiterable.h \
  /opt/homebrew/include/QtCore/qmetacontainer.h \
  /opt/homebrew/include/QtCore/qcontainerinfo.h \
  /opt/homebrew/include/QtCore/qtaggedpointer.h \
  /opt/homebrew/include/QtCore/qscopeguard.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/list \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/map \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__tree \
  /opt/homebrew/include/QtCore/qobject_impl.h \
  /opt/homebrew/include/QtCore/qbindingstorage.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/chrono \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__chrono/file_clock.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/forward_list \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3KI4N4FQU5/../../src/ai/ml/../../models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3KI4N4FQU5/../../src/ai/ml/../../models/order_flow_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3KI4N4FQU5/../../src/ai/ml/../../models/qdatetime_compat.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/sstream \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__ostream/basic_ostream.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__ostream/put_character_sequence.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/bitset \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__type_traits/is_char_like_type.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/istream \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/ostream \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/format \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/queue \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__fwd/deque.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__fwd/queue.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/deque \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/stack \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__fwd/stack.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/print \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/unistd.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/iomanip \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3KI4N4FQU5/../../src/ai/ml/../../models/market_depth_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3KI4N4FQU5/../../src/ai/ml/../../models/volume_profile_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3KI4N4FQU5/../../src/ai/ml/../../models/prediction_result.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3KI4N4FQU5/../../src/ai/ml/../../models/news_event.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3KI4N4FQU5/../../src/ai/ml/../../models/news_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3KI4N4FQU5/../../src/ai/ml/../../models/qt_compat.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3KI4N4FQU5/../../src/ai/ml/model_optimizer.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/random \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__random/bernoulli_distribution.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__random/uniform_real_distribution.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__random/generate_canonical.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__random/binomial_distribution.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__random/cauchy_distribution.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__random/chi_squared_distribution.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__random/gamma_distribution.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__random/exponential_distribution.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__random/default_random_engine.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__random/linear_congruential_engine.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__random/is_seed_sequence.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__random/discard_block_engine.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__random/discrete_distribution.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__random/extreme_value_distribution.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__random/fisher_f_distribution.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__random/geometric_distribution.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__random/negative_binomial_distribution.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__random/poisson_distribution.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__random/clamp_to_integral.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__random/normal_distribution.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__random/independent_bits_engine.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__random/knuth_b.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__random/shuffle_order_engine.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__random/lognormal_distribution.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__random/mersenne_twister_engine.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__random/piecewise_constant_distribution.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__random/piecewise_linear_distribution.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__random/random_device.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__random/ranlux.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__random/subtract_with_carry_engine.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__random/seed_seq.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__random/student_t_distribution.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__random/uniform_random_bit_generator.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__random/weibull_distribution.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3KI4N4FQU5/../../src/ai/ml/model_evaluation.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3KI4N4FQU5/../../src/ai/ml/../../models/feature_set.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3KI4N4FQU5/../../src/ai/ml/../../models/trade_result.h \
  /opt/homebrew/include/QtCore/qdatetime.h \
  /opt/homebrew/include/QtCore/qcalendar.h \
  /opt/homebrew/include/QtCore/qlocale.h \
  /opt/homebrew/include/QtCore/qvariant.h \
  /opt/homebrew/include/QtCore/qdebug.h \
  /opt/homebrew/include/QtCore/qtextstream.h \
  /opt/homebrew/include/QtCore/qcontiguouscache.h \
  /opt/homebrew/include/QtCore/qsharedpointer.h \
  /opt/homebrew/include/QtCore/qshareddata.h \
  /opt/homebrew/include/QtCore/qsharedpointer_impl.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/set \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/unordered_set \
  /opt/homebrew/include/QtCore/qmap.h \
  /opt/homebrew/include/QtCore/qshareddata_impl.h \
  /opt/homebrew/include/QtCore/qset.h \
  /opt/homebrew/include/QtCore/qhash.h \
  /opt/homebrew/include/QtCore/qvarlengtharray.h \
  /opt/homebrew/include/QtCore/q23utility.h \
  /opt/homebrew/include/QtCore/q20utility.h \
  /opt/homebrew/include/QtCore/qobject.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3KI4N4FQU5/../../src/ai/ml/../../models/qmetatype_compat.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3KI4N4FQU5/../../src/ai/ml/../../models/../trading/trading_data.h \
  /opt/homebrew/lib/QtCore.framework/Headers/QDateTime \
  /opt/homebrew/lib/QtCore.framework/Headers/qdatetime.h \
  /opt/homebrew/lib/QtCore.framework/Headers/QString \
  /opt/homebrew/lib/QtCore.framework/Headers/qstring.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3KI4N4FQU5/../../src/ai/ml/../../models/../trading/../models/trade.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3KI4N4FQU5/../../src/ai/ml/../../models/../trading/../models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3KI4N4FQU5/../../src/ai/ml/../../utils/logger.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/fstream \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__filesystem/path.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/filesystem \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__filesystem/copy_options.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__filesystem/directory_entry.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__filesystem/file_status.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__filesystem/file_type.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__filesystem/perms.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__filesystem/file_time_type.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__filesystem/filesystem_error.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__filesystem/operations.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__filesystem/perm_options.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__filesystem/space_info.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__filesystem/directory_iterator.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__filesystem/directory_options.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__filesystem/path_iterator.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__filesystem/recursive_directory_iterator.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__filesystem/u8path.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/iostream \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3KI4N4FQU5/../../src/ai/ml/model_monitor.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/binaryoptionsbot/../models/prediction_result.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/binaryoptionsbot/../models/trade_result.h \
  /opt/homebrew/include/QtCore/qtmochelpers.h \
  /opt/homebrew/include/QtCore/qtmocconstants.h \
  /opt/homebrew/include/QtCore/q20algorithm.h \
  /opt/homebrew/include/QtCore/q23type_traits.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3KI4N4FQU5/moc_concrete_ai_model.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3KI4N4FQU5/../../src/ai/ml/concrete_ai_model.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3KI4N4FQU5/../../src/ai/ml/../../models/training_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3KI4N4FQU5/../../src/ai/ml/../torch/torch.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/S5WMBGBJYU/moc_market_processor.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/S5WMBGBJYU/../../src/ai/realtime/market_processor.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/S5WMBGBJYU/../../src/ai/realtime/../interfaces/market_processor.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/S5WMBGBJYU/../../src/ai/realtime/../interfaces/../../models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/S5WMBGBJYU/../../src/ai/realtime/../interfaces/../../models/news_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/S5WMBGBJYU/../../src/ai/realtime/../../models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/S5WMBGBJYU/../../src/ai/realtime/../../models/market_conditions.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/S5WMBGBJYU/../../src/ai/realtime/../../models/news_event.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/condition_variable \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__stop_token/stop_callback.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__stop_token/intrusive_shared_ptr.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__stop_token/stop_state.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__stop_token/atomic_unique_lock.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__stop_token/intrusive_list_view.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__stop_token/stop_token.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/thread \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__thread/this_thread.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/__thread/thread.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/moc_market_analyzer.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/market_analyzer.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/market_analyzer.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/order_book.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/trade.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../models/trade.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/market_context.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../models/liquidity_analysis.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../models/liquidity_types.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/timeframe/timeframe_selection.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/indicators/indicator.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/indicators/../../models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/market_structure.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/volume_profile.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/market_metrics.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/liquidity_analyzer.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/market_efficiency.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../utils/logger.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/market_analysis_integrator.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/market_regime_detector.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/../models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/../models/../../models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/binaryoptionsbot/../models/market_regime.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/../ml/feature_engineering.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/../ml/../../models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/../ml/../../models/feature_set.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/../ml/../../utils/logger.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/../risk/risk_metrics.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/pattern_optimizer.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/future \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/../patterns/pattern.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/../patterns/candlestick_patterns.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/../patterns/../../models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/../../models/pattern_analysis.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/../../models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/../../models/../trading/timeframe.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/../../models/volume_profile_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/../../models/../validation/multi_timeframe_validation.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/../../models/../validation/../models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/../../models/../validation/../trading/trading_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/../../models/../validation/../models/market_regime.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/../../models/../ai/patterns/pattern_recognition_types.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/../../models/pattern_direction.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/../../models/trade_decision.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/../../models/../trading/trading_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/../../models/../ai/indicators/indicator.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/../../models/../ai/indicators/indicator_selector.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/../../models/../ai/indicators/../../models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/../../models/../ai/indicators/../../trading/trading_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/../../models/../ai/patterns/pattern.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/../../trading/market_regime.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/../../trading/../models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/../../trading/trading_strategy.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/../../trading/../models/portfolio.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/../../trading/../models/../trading/trading_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/../../trading/../models/position.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/../../trading/../models/trade.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/../../ai/trading_brain_types.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/../../ai/../models/trade.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/realtime/../../ai/risk/risk_metrics.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/patterns/pattern_analyzer.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/patterns/../patterns/pattern.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/patterns/../patterns/candlestick_patterns.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/patterns/../../models/pattern_analysis.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../ai/patterns/../../models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../market/../models/pattern_analysis.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../models/volume_profile_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../models/market_regime.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../models/pattern_analysis.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../ai/ml/ensemble_strategy.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../ai/ml/advanced_ai_model.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../ai/ml/model_monitor.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../models/news_event.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../models/prediction_result.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../ai/ml/ai_models.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../ai/ml/../../models/training_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../ai/ml/model_optimizer.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../ai/ml/concrete_ai_model.h \
  /opt/homebrew/lib/QtCore.framework/Headers/QMetaObject \
  /opt/homebrew/lib/QtCore.framework/Headers/qobjectdefs.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../ai/patterns/pattern_recognizer.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../ai/patterns/../../models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../ai/patterns/../../models/pattern_analysis.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../ai/patterns/../../models/ai_prediction.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../ai/patterns/../ml/feature_engineering.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../ai/patterns/../ml/feature_extractor.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../ai/patterns/../ml/../../models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../ai/patterns/../ml/../../models/feature_set.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/JESWEPGH3D/../../src/binaryoptionsbot/analysis/../../ai/patterns/../ml/model_evaluation.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/moc_binary_options_bot.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/binary_options_bot.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../models/qobject_compat.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../models/trading_config.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../models/qdatetime_compat.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/json.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/adl_serializer.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/detail/abi_macros.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/detail/conversions/from_json.hpp \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/valarray \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/detail/exceptions.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/detail/value_t.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/detail/macro_scope.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/detail/meta/detected.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/detail/meta/void_t.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/thirdparty/hedley/hedley.hpp \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/cassert \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/detail/string_escape.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/detail/input/position_t.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/detail/meta/cpp_future.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/detail/meta/type_traits.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/detail/iterators/iterator_traits.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/detail/meta/call_std/begin.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/detail/meta/call_std/end.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/json_fwd.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/detail/string_concat.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/detail/meta/identity_tag.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/detail/meta/std_fs.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/detail/conversions/to_json.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/detail/iterators/iteration_proxy.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/detail/string_utils.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/byte_container_with_subtype.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/detail/hash.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/detail/input/binary_reader.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/detail/input/input_adapters.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/detail/input/json_sax.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/detail/input/lexer.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/detail/meta/is_sax.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/detail/input/parser.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/detail/iterators/internal_iterator.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/detail/iterators/primitive_iterator.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/detail/iterators/iter_impl.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/detail/iterators/json_reverse_iterator.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/detail/json_custom_base_class.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/detail/json_pointer.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/detail/json_ref.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/detail/output/binary_writer.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/detail/output/output_adapters.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/detail/output/serializer.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/detail/conversions/to_chars.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/ordered_map.hpp \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/any \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/detail/macro_unscope.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/deps/json/include/nlohmann/thirdparty/hedley/hedley_undef.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/trading_brain.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../models/news_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../models/trade_decision.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../models/trading_opportunity.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../models/../ai/trading_brain_types.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../models/harmonic_patterns.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/realtime/market_processor.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/patterns/pattern_analyzer.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/patterns/candlestick_analyzer.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/patterns/../../models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/patterns/pattern.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/ml/advanced_ai_model.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/ml/pattern_recognition_model.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/ml/../ai_model.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/ml/../models/neural_network.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/ml/../models/prediction_model.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/binaryoptionsbot/../models/feature_set.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/ml/../models/../utils/matrix.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/models/feature_set.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/ml/../models/news_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/ml/../../models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/ml/../../models/feature_set.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/ml/model_optimizer.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/ml/model_evaluation.h \
  /opt/homebrew/include/xgboost/c_api.h \
  /opt/homebrew/include/LightGBM/c_api.h \
  /opt/homebrew/include/LightGBM/arrow.h \
  /opt/homebrew/include/LightGBM/arrow.tpp \
  /opt/homebrew/include/LightGBM/export.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/ml/feature_extractor.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/ml/feature_engineering.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/ml/model_monitor.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/timeframe/timeframe_learner.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/timeframe/../../models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/timeframe/../../models/trading_config.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/timeframe/timeframe_selection.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../models/performance_metrics.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../models/qt_compat.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../models/../models/trade.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../models/../models/news_data.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/sysctl.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/time.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_timeval64.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_select.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/ucred.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/param.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/machine/param.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/arm/param.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/arm/_param.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/bsm/audit.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/port.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/boolean.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/machine/boolean.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/arm/boolean.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/machine/vm_types.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/arm/vm_types.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/proc.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/select.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/queue.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/lock.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/event.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/vm.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/mach.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/std_types.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/kern_return.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/machine/kern_return.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/arm/kern_return.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/vm_types.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_uuid_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/mach_types.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/host_info.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/message.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/vm_statistics.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/machine.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/time_value.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/host_notify.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/host_special_ports.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/memory_object_types.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/vm_prot.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/vm_sync.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/exception_types.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/machine/exception.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/arm/exception.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/thread_status.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/machine/thread_status.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/arm/thread_status.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/machine/thread_state.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/arm/thread_state.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach_debug/ipc_info.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/mach_voucher_types.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/processor_info.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/machine/processor_info.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/arm/processor_info.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/task_info.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/policy.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/task_inspect.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/task_policy.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/task_special_ports.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/thread_info.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/thread_policy.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/thread_special_ports.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/clock_types.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/vm_attributes.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/vm_inherit.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/vm_purgable.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/vm_behavior.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/vm_region.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/machine/vm_param.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/arm/vm_param.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/vm_page_size.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/kmod.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/dyld_kernel.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_fsid_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_fsobj_id_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/mach_interface.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/clock_priv.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/ndr.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/libkern/OSByteOrder.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/libkern/arm/OSByteOrder.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_os_inline.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/arm/arch.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/notify.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/mig_errors.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/mig.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/mig_strncpy_zerofill_support.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/host_priv.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach_debug/mach_debug_types.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach_debug/vm_info.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach_debug/zone_info.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach_debug/page_info.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach_debug/hash_info.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach_debug/lockgroup_info.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/host_security.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/processor.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/processor_set.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/semaphore.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/sync_policy.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/task.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/thread_act.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/vm_map.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/mach_port.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/mach_init.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/mach_traps.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/mach_host.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/thread_switch.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/rpc.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/machine/rpc.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/arm/rpc.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/mach_error.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/mach/error.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../models/trade_result.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/indicators/indicator_selector.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../api/trading_api.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../api/ws_channel.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../api/market_data_handler.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../api/../models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../api/order_manager.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../api/account_manager.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../api/auth/token_manager.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../api/auth/token.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../api/websocket_channel.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../api/websocket_config.hpp \
  /opt/homebrew/include/websocketpp/config/asio_client.hpp \
  /opt/homebrew/include/websocketpp/config/core_client.hpp \
  /opt/homebrew/include/websocketpp/common/platforms.hpp \
  /opt/homebrew/include/websocketpp/common/cpp11.hpp \
  /opt/homebrew/include/websocketpp/common/stdint.hpp \
  /opt/homebrew/include/websocketpp/concurrency/basic.hpp \
  /opt/homebrew/include/websocketpp/common/thread.hpp \
  /opt/homebrew/include/websocketpp/transport/iostream/endpoint.hpp \
  /opt/homebrew/include/websocketpp/transport/base/endpoint.hpp \
  /opt/homebrew/include/websocketpp/common/functional.hpp \
  /opt/homebrew/include/websocketpp/common/system_error.hpp \
  /opt/homebrew/include/websocketpp/transport/iostream/connection.hpp \
  /opt/homebrew/include/websocketpp/transport/iostream/base.hpp \
  /opt/homebrew/include/websocketpp/common/connection_hdl.hpp \
  /opt/homebrew/include/websocketpp/common/memory.hpp \
  /opt/homebrew/include/websocketpp/transport/base/connection.hpp \
  /opt/homebrew/include/websocketpp/uri.hpp \
  /opt/homebrew/include/websocketpp/error.hpp \
  /opt/homebrew/include/websocketpp/logger/levels.hpp \
  /opt/homebrew/include/websocketpp/http/request.hpp \
  /opt/homebrew/include/websocketpp/http/parser.hpp \
  /opt/homebrew/include/websocketpp/utilities.hpp \
  /opt/homebrew/include/websocketpp/impl/utilities_impl.hpp \
  /opt/homebrew/include/websocketpp/http/constants.hpp \
  /opt/homebrew/include/websocketpp/http/impl/parser.hpp \
  /opt/homebrew/include/websocketpp/http/impl/request.hpp \
  /opt/homebrew/include/websocketpp/http/response.hpp \
  /opt/homebrew/include/websocketpp/http/impl/response.hpp \
  /opt/homebrew/include/websocketpp/message_buffer/message.hpp \
  /opt/homebrew/include/websocketpp/frame.hpp \
  /opt/homebrew/include/websocketpp/common/network.hpp \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/netinet/in.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/socket.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/constrained_ctypes.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/machine/_param.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/net/net_kev.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_sa_family_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_socklen_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_iovec_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/netinet6/in6.h \
  /opt/homebrew/include/websocketpp/message_buffer/alloc.hpp \
  /opt/homebrew/include/websocketpp/logger/basic.hpp \
  /opt/homebrew/include/websocketpp/common/time.hpp \
  /opt/homebrew/include/websocketpp/random/random_device.hpp \
  /opt/homebrew/include/websocketpp/common/random.hpp \
  /opt/homebrew/include/websocketpp/endpoint_base.hpp \
  /opt/homebrew/include/websocketpp/connection_base.hpp \
  /opt/homebrew/include/websocketpp/extensions/permessage_deflate/disabled.hpp \
  /opt/homebrew/include/websocketpp/extensions/extension.hpp \
  /opt/homebrew/include/websocketpp/transport/asio/endpoint.hpp \
  /opt/homebrew/include/websocketpp/transport/asio/connection.hpp \
  /opt/homebrew/include/websocketpp/transport/asio/base.hpp \
  /opt/homebrew/include/websocketpp/common/asio.hpp \
  /opt/homebrew/include/asio/version.hpp /opt/homebrew/include/asio.hpp \
  /opt/homebrew/include/asio/any_completion_executor.hpp \
  /opt/homebrew/include/asio/detail/config.hpp \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/unistd.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_posix_vdisable.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/gethostuuid.h \
  /opt/homebrew/include/asio/execution.hpp \
  /opt/homebrew/include/asio/execution/allocator.hpp \
  /opt/homebrew/include/asio/detail/type_traits.hpp \
  /opt/homebrew/include/asio/execution/executor.hpp \
  /opt/homebrew/include/asio/execution/invocable_archetype.hpp \
  /opt/homebrew/include/asio/detail/push_options.hpp \
  /opt/homebrew/include/asio/detail/pop_options.hpp \
  /opt/homebrew/include/asio/traits/equality_comparable.hpp \
  /opt/homebrew/include/asio/traits/execute_member.hpp \
  /opt/homebrew/include/asio/is_applicable_property.hpp \
  /opt/homebrew/include/asio/traits/query_static_constexpr_member.hpp \
  /opt/homebrew/include/asio/traits/static_query.hpp \
  /opt/homebrew/include/asio/execution/any_executor.hpp \
  /opt/homebrew/include/asio/detail/assert.hpp \
  /opt/homebrew/include/asio/detail/atomic_count.hpp \
  /opt/homebrew/include/asio/detail/cstddef.hpp \
  /opt/homebrew/include/asio/detail/executor_function.hpp \
  /opt/homebrew/include/asio/detail/handler_alloc_helpers.hpp \
  /opt/homebrew/include/asio/detail/memory.hpp \
  /opt/homebrew/include/asio/detail/cstdint.hpp \
  /opt/homebrew/include/asio/detail/throw_exception.hpp \
  /opt/homebrew/include/asio/detail/noncopyable.hpp \
  /opt/homebrew/include/asio/detail/recycling_allocator.hpp \
  /opt/homebrew/include/asio/detail/thread_context.hpp \
  /opt/homebrew/include/asio/detail/call_stack.hpp \
  /opt/homebrew/include/asio/detail/tss_ptr.hpp \
  /opt/homebrew/include/asio/detail/posix_tss_ptr.hpp \
  /opt/homebrew/include/asio/detail/impl/posix_tss_ptr.ipp \
  /opt/homebrew/include/asio/detail/throw_error.hpp \
  /opt/homebrew/include/asio/error_code.hpp \
  /opt/homebrew/include/asio/impl/error_code.ipp \
  /opt/homebrew/include/asio/detail/local_free_on_block_exit.hpp \
  /opt/homebrew/include/asio/detail/socket_types.hpp \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/ioctl.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/ttycom.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/ioccom.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/filio.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/sockio.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/net/if.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/net/if_var.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_timeval32.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/poll.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/poll.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/stat.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_s_ifmt.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_filesec_t.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/fcntl.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/fcntl.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_o_sync.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/_types/_o_dsync.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/uio.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/un.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/netinet/tcp.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/arpa/inet.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/netdb.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/signal.h \
  /opt/homebrew/include/asio/detail/impl/throw_error.ipp \
  /opt/homebrew/include/asio/system_error.hpp \
  /opt/homebrew/include/asio/error.hpp \
  /opt/homebrew/include/asio/impl/error.ipp \
  /opt/homebrew/include/asio/detail/impl/thread_context.ipp \
  /opt/homebrew/include/asio/detail/thread_info_base.hpp \
  /opt/homebrew/include/asio/multiple_exceptions.hpp \
  /opt/homebrew/include/asio/impl/multiple_exceptions.ipp \
  /opt/homebrew/include/asio/associated_allocator.hpp \
  /opt/homebrew/include/asio/associator.hpp \
  /opt/homebrew/include/asio/detail/functional.hpp \
  /opt/homebrew/include/asio/detail/non_const_lvalue.hpp \
  /opt/homebrew/include/asio/detail/scoped_ptr.hpp \
  /opt/homebrew/include/asio/execution/bad_executor.hpp \
  /opt/homebrew/include/asio/execution/impl/bad_executor.ipp \
  /opt/homebrew/include/asio/execution/blocking.hpp \
  /opt/homebrew/include/asio/prefer.hpp \
  /opt/homebrew/include/asio/traits/prefer_free.hpp \
  /opt/homebrew/include/asio/traits/prefer_member.hpp \
  /opt/homebrew/include/asio/traits/require_free.hpp \
  /opt/homebrew/include/asio/traits/require_member.hpp \
  /opt/homebrew/include/asio/traits/static_require.hpp \
  /opt/homebrew/include/asio/query.hpp \
  /opt/homebrew/include/asio/traits/query_member.hpp \
  /opt/homebrew/include/asio/traits/query_free.hpp \
  /opt/homebrew/include/asio/require.hpp \
  /opt/homebrew/include/asio/execution/blocking_adaptation.hpp \
  /opt/homebrew/include/asio/detail/event.hpp \
  /opt/homebrew/include/asio/detail/posix_event.hpp \
  /opt/homebrew/include/asio/detail/impl/posix_event.ipp \
  /opt/homebrew/include/asio/detail/mutex.hpp \
  /opt/homebrew/include/asio/detail/posix_mutex.hpp \
  /opt/homebrew/include/asio/detail/scoped_lock.hpp \
  /opt/homebrew/include/asio/detail/impl/posix_mutex.ipp \
  /opt/homebrew/include/asio/execution/context.hpp \
  /opt/homebrew/include/asio/execution/context_as.hpp \
  /opt/homebrew/include/asio/execution/mapping.hpp \
  /opt/homebrew/include/asio/execution/occupancy.hpp \
  /opt/homebrew/include/asio/execution/outstanding_work.hpp \
  /opt/homebrew/include/asio/execution/prefer_only.hpp \
  /opt/homebrew/include/asio/execution/relationship.hpp \
  /opt/homebrew/include/asio/impl/any_completion_executor.ipp \
  /opt/homebrew/include/asio/any_completion_handler.hpp \
  /opt/homebrew/include/asio/any_io_executor.hpp \
  /opt/homebrew/include/asio/execution_context.hpp \
  /opt/homebrew/include/asio/impl/execution_context.hpp \
  /opt/homebrew/include/asio/detail/handler_type_requirements.hpp \
  /opt/homebrew/include/asio/async_result.hpp \
  /opt/homebrew/include/asio/detail/service_registry.hpp \
  /opt/homebrew/include/asio/detail/impl/service_registry.hpp \
  /opt/homebrew/include/asio/detail/impl/service_registry.ipp \
  /opt/homebrew/include/asio/impl/execution_context.ipp \
  /opt/homebrew/include/asio/impl/any_io_executor.ipp \
  /opt/homebrew/include/asio/associated_cancellation_slot.hpp \
  /opt/homebrew/include/asio/cancellation_signal.hpp \
  /opt/homebrew/include/asio/cancellation_type.hpp \
  /opt/homebrew/include/asio/impl/cancellation_signal.ipp \
  /opt/homebrew/include/asio/associated_executor.hpp \
  /opt/homebrew/include/asio/is_executor.hpp \
  /opt/homebrew/include/asio/detail/is_executor.hpp \
  /opt/homebrew/include/asio/system_executor.hpp \
  /opt/homebrew/include/asio/impl/system_executor.hpp \
  /opt/homebrew/include/asio/detail/executor_op.hpp \
  /opt/homebrew/include/asio/detail/fenced_block.hpp \
  /opt/homebrew/include/asio/detail/std_fenced_block.hpp \
  /opt/homebrew/include/asio/detail/scheduler_operation.hpp \
  /opt/homebrew/include/asio/detail/handler_tracking.hpp \
  /opt/homebrew/include/asio/detail/impl/handler_tracking.ipp \
  /opt/homebrew/include/asio/detail/op_queue.hpp \
  /opt/homebrew/include/asio/detail/global.hpp \
  /opt/homebrew/include/asio/detail/posix_global.hpp \
  /opt/homebrew/include/asio/system_context.hpp \
  /opt/homebrew/include/asio/detail/scheduler.hpp \
  /opt/homebrew/include/asio/detail/conditionally_enabled_event.hpp \
  /opt/homebrew/include/asio/detail/conditionally_enabled_mutex.hpp \
  /opt/homebrew/include/asio/detail/null_event.hpp \
  /opt/homebrew/include/asio/detail/impl/null_event.ipp \
  /opt/homebrew/include/asio/detail/scheduler_task.hpp \
  /opt/homebrew/include/asio/detail/thread.hpp \
  /opt/homebrew/include/asio/detail/posix_thread.hpp \
  /opt/homebrew/include/asio/detail/impl/posix_thread.ipp \
  /opt/homebrew/include/asio/detail/impl/scheduler.ipp \
  /opt/homebrew/include/asio/detail/concurrency_hint.hpp \
  /opt/homebrew/include/asio/detail/limits.hpp \
  /opt/homebrew/include/asio/detail/scheduler_thread_info.hpp \
  /opt/homebrew/include/asio/detail/signal_blocker.hpp \
  /opt/homebrew/include/asio/detail/posix_signal_blocker.hpp \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/csignal \
  /opt/homebrew/include/asio/detail/reactor.hpp \
  /opt/homebrew/include/asio/detail/kqueue_reactor.hpp \
  /opt/homebrew/include/asio/detail/object_pool.hpp \
  /opt/homebrew/include/asio/detail/reactor_op.hpp \
  /opt/homebrew/include/asio/detail/operation.hpp \
  /opt/homebrew/include/asio/detail/select_interrupter.hpp \
  /opt/homebrew/include/asio/detail/pipe_select_interrupter.hpp \
  /opt/homebrew/include/asio/detail/impl/pipe_select_interrupter.ipp \
  /opt/homebrew/include/asio/detail/timer_queue_base.hpp \
  /opt/homebrew/include/asio/detail/timer_queue_set.hpp \
  /opt/homebrew/include/asio/detail/impl/timer_queue_set.ipp \
  /opt/homebrew/include/asio/detail/wait_op.hpp \
  /opt/homebrew/include/asio/detail/impl/kqueue_reactor.hpp \
  /opt/homebrew/include/asio/detail/impl/kqueue_reactor.ipp \
  /opt/homebrew/include/asio/detail/thread_group.hpp \
  /opt/homebrew/include/asio/impl/system_context.hpp \
  /opt/homebrew/include/asio/impl/system_context.ipp \
  /opt/homebrew/include/asio/associated_immediate_executor.hpp \
  /opt/homebrew/include/asio/cancellation_state.hpp \
  /opt/homebrew/include/asio/recycling_allocator.hpp \
  /opt/homebrew/include/asio/append.hpp \
  /opt/homebrew/include/asio/impl/append.hpp \
  /opt/homebrew/include/asio/detail/handler_cont_helpers.hpp \
  /opt/homebrew/include/asio/handler_continuation_hook.hpp \
  /opt/homebrew/include/asio/detail/utility.hpp \
  /opt/homebrew/include/asio/as_tuple.hpp \
  /opt/homebrew/include/asio/impl/as_tuple.hpp \
  /opt/homebrew/include/asio/awaitable.hpp \
  /opt/homebrew/include/asio/basic_datagram_socket.hpp \
  /opt/homebrew/include/asio/basic_socket.hpp \
  /opt/homebrew/include/asio/detail/io_object_impl.hpp \
  /opt/homebrew/include/asio/io_context.hpp \
  /opt/homebrew/include/asio/detail/chrono.hpp \
  /opt/homebrew/include/asio/detail/wrapped_handler.hpp \
  /opt/homebrew/include/asio/detail/bind_handler.hpp \
  /opt/homebrew/include/asio/impl/io_context.hpp \
  /opt/homebrew/include/asio/detail/completion_handler.hpp \
  /opt/homebrew/include/asio/detail/handler_work.hpp \
  /opt/homebrew/include/asio/detail/initiate_dispatch.hpp \
  /opt/homebrew/include/asio/detail/work_dispatcher.hpp \
  /opt/homebrew/include/asio/executor_work_guard.hpp \
  /opt/homebrew/include/asio/impl/io_context.ipp \
  /opt/homebrew/include/asio/post.hpp \
  /opt/homebrew/include/asio/detail/initiate_post.hpp \
  /opt/homebrew/include/asio/socket_base.hpp \
  /opt/homebrew/include/asio/detail/io_control.hpp \
  /opt/homebrew/include/asio/detail/socket_option.hpp \
  /opt/homebrew/include/asio/detail/reactive_socket_service.hpp \
  /opt/homebrew/include/asio/buffer.hpp \
  /opt/homebrew/include/asio/detail/array_fwd.hpp \
  /opt/homebrew/include/asio/detail/string_view.hpp \
  /opt/homebrew/include/asio/is_contiguous_iterator.hpp \
  /opt/homebrew/include/asio/detail/is_buffer_sequence.hpp \
  /opt/homebrew/include/asio/detail/buffer_sequence_adapter.hpp \
  /opt/homebrew/include/asio/registered_buffer.hpp \
  /opt/homebrew/include/asio/detail/impl/buffer_sequence_adapter.ipp \
  /opt/homebrew/include/asio/detail/reactive_null_buffers_op.hpp \
  /opt/homebrew/include/asio/detail/reactive_socket_accept_op.hpp \
  /opt/homebrew/include/asio/detail/socket_holder.hpp \
  /opt/homebrew/include/asio/detail/socket_ops.hpp \
  /opt/homebrew/include/asio/detail/impl/socket_ops.ipp \
  /opt/homebrew/include/asio/detail/reactive_socket_connect_op.hpp \
  /opt/homebrew/include/asio/detail/reactive_socket_recvfrom_op.hpp \
  /opt/homebrew/include/asio/detail/reactive_socket_sendto_op.hpp \
  /opt/homebrew/include/asio/detail/reactive_socket_service_base.hpp \
  /opt/homebrew/include/asio/detail/reactive_socket_recv_op.hpp \
  /opt/homebrew/include/asio/detail/reactive_socket_recvmsg_op.hpp \
  /opt/homebrew/include/asio/detail/reactive_socket_send_op.hpp \
  /opt/homebrew/include/asio/detail/reactive_wait_op.hpp \
  /opt/homebrew/include/asio/detail/impl/reactive_socket_service_base.ipp \
  /opt/homebrew/include/asio/basic_deadline_timer.hpp \
  /opt/homebrew/include/asio/basic_file.hpp \
  /opt/homebrew/include/asio/basic_io_object.hpp \
  /opt/homebrew/include/asio/basic_random_access_file.hpp \
  /opt/homebrew/include/asio/basic_raw_socket.hpp \
  /opt/homebrew/include/asio/basic_readable_pipe.hpp \
  /opt/homebrew/include/asio/detail/reactive_descriptor_service.hpp \
  /opt/homebrew/include/asio/detail/descriptor_ops.hpp \
  /opt/homebrew/include/asio/detail/impl/descriptor_ops.ipp \
  /opt/homebrew/include/asio/detail/descriptor_read_op.hpp \
  /opt/homebrew/include/asio/dispatch.hpp \
  /opt/homebrew/include/asio/detail/descriptor_write_op.hpp \
  /opt/homebrew/include/asio/posix/descriptor_base.hpp \
  /opt/homebrew/include/asio/detail/impl/reactive_descriptor_service.ipp \
  /opt/homebrew/include/asio/basic_seq_packet_socket.hpp \
  /opt/homebrew/include/asio/basic_serial_port.hpp \
  /opt/homebrew/include/asio/serial_port_base.hpp \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/termios.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/termios.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/sys/ttydefaults.h \
  /opt/homebrew/include/asio/impl/serial_port_base.hpp \
  /opt/homebrew/include/asio/impl/serial_port_base.ipp \
  /opt/homebrew/include/asio/detail/posix_serial_port_service.hpp \
  /opt/homebrew/include/asio/detail/impl/posix_serial_port_service.ipp \
  /opt/homebrew/include/asio/basic_signal_set.hpp \
  /opt/homebrew/include/asio/detail/signal_set_service.hpp \
  /opt/homebrew/include/asio/signal_set_base.hpp \
  /opt/homebrew/include/asio/detail/signal_handler.hpp \
  /opt/homebrew/include/asio/detail/signal_op.hpp \
  /opt/homebrew/include/asio/detail/impl/signal_set_service.ipp \
  /opt/homebrew/include/asio/detail/static_mutex.hpp \
  /opt/homebrew/include/asio/detail/posix_static_mutex.hpp \
  /opt/homebrew/include/asio/basic_socket_acceptor.hpp \
  /opt/homebrew/include/asio/basic_socket_iostream.hpp \
  /opt/homebrew/include/asio/basic_socket_streambuf.hpp \
  /opt/homebrew/include/asio/basic_stream_socket.hpp \
  /opt/homebrew/include/asio/steady_timer.hpp \
  /opt/homebrew/include/asio/basic_waitable_timer.hpp \
  /opt/homebrew/include/asio/detail/chrono_time_traits.hpp \
  /opt/homebrew/include/asio/detail/deadline_timer_service.hpp \
  /opt/homebrew/include/asio/detail/timer_queue.hpp \
  /opt/homebrew/include/asio/detail/date_time_fwd.hpp \
  /opt/homebrew/include/asio/detail/timer_queue_ptime.hpp \
  /opt/homebrew/include/asio/detail/timer_scheduler.hpp \
  /opt/homebrew/include/asio/detail/timer_scheduler_fwd.hpp \
  /opt/homebrew/include/asio/detail/wait_handler.hpp \
  /opt/homebrew/include/asio/wait_traits.hpp \
  /opt/homebrew/include/asio/basic_stream_file.hpp \
  /opt/homebrew/include/asio/basic_streambuf.hpp \
  /opt/homebrew/include/asio/basic_streambuf_fwd.hpp \
  /opt/homebrew/include/asio/basic_writable_pipe.hpp \
  /opt/homebrew/include/asio/bind_allocator.hpp \
  /opt/homebrew/include/asio/bind_cancellation_slot.hpp \
  /opt/homebrew/include/asio/bind_executor.hpp \
  /opt/homebrew/include/asio/uses_executor.hpp \
  /opt/homebrew/include/asio/bind_immediate_executor.hpp \
  /opt/homebrew/include/asio/buffer_registration.hpp \
  /opt/homebrew/include/asio/buffered_read_stream_fwd.hpp \
  /opt/homebrew/include/asio/buffered_read_stream.hpp \
  /opt/homebrew/include/asio/detail/buffer_resize_guard.hpp \
  /opt/homebrew/include/asio/detail/buffered_stream_storage.hpp \
  /opt/homebrew/include/asio/impl/buffered_read_stream.hpp \
  /opt/homebrew/include/asio/buffered_stream_fwd.hpp \
  /opt/homebrew/include/asio/buffered_stream.hpp \
  /opt/homebrew/include/asio/buffered_write_stream.hpp \
  /opt/homebrew/include/asio/buffered_write_stream_fwd.hpp \
  /opt/homebrew/include/asio/completion_condition.hpp \
  /opt/homebrew/include/asio/write.hpp \
  /opt/homebrew/include/asio/impl/write.hpp \
  /opt/homebrew/include/asio/detail/base_from_cancellation_state.hpp \
  /opt/homebrew/include/asio/detail/base_from_completion_cond.hpp \
  /opt/homebrew/include/asio/detail/consuming_buffers.hpp \
  /opt/homebrew/include/asio/detail/dependent_type.hpp \
  /opt/homebrew/include/asio/impl/buffered_write_stream.hpp \
  /opt/homebrew/include/asio/buffers_iterator.hpp \
  /opt/homebrew/include/asio/co_spawn.hpp \
  /opt/homebrew/include/asio/compose.hpp \
  /opt/homebrew/include/asio/detail/composed_work.hpp \
  /opt/homebrew/include/asio/connect.hpp \
  /opt/homebrew/include/asio/impl/connect.hpp \
  /opt/homebrew/include/asio/connect_pipe.hpp \
  /opt/homebrew/include/asio/impl/connect_pipe.hpp \
  /opt/homebrew/include/asio/impl/connect_pipe.ipp \
  /opt/homebrew/include/asio/consign.hpp \
  /opt/homebrew/include/asio/impl/consign.hpp \
  /opt/homebrew/include/asio/coroutine.hpp \
  /opt/homebrew/include/asio/deadline_timer.hpp \
  /opt/homebrew/include/asio/defer.hpp \
  /opt/homebrew/include/asio/detail/initiate_defer.hpp \
  /opt/homebrew/include/asio/deferred.hpp \
  /opt/homebrew/include/asio/impl/deferred.hpp \
  /opt/homebrew/include/asio/detached.hpp \
  /opt/homebrew/include/asio/impl/detached.hpp \
  /opt/homebrew/include/asio/executor.hpp \
  /opt/homebrew/include/asio/impl/executor.hpp \
  /opt/homebrew/include/asio/impl/executor.ipp \
  /opt/homebrew/include/asio/file_base.hpp \
  /opt/homebrew/include/asio/generic/basic_endpoint.hpp \
  /opt/homebrew/include/asio/generic/detail/endpoint.hpp \
  /opt/homebrew/include/asio/generic/detail/impl/endpoint.ipp \
  /opt/homebrew/include/asio/generic/datagram_protocol.hpp \
  /opt/homebrew/include/asio/generic/raw_protocol.hpp \
  /opt/homebrew/include/asio/generic/seq_packet_protocol.hpp \
  /opt/homebrew/include/asio/generic/stream_protocol.hpp \
  /opt/homebrew/include/asio/high_resolution_timer.hpp \
  /opt/homebrew/include/asio/io_context_strand.hpp \
  /opt/homebrew/include/asio/detail/strand_service.hpp \
  /opt/homebrew/include/asio/detail/impl/strand_service.hpp \
  /opt/homebrew/include/asio/detail/impl/strand_service.ipp \
  /opt/homebrew/include/asio/io_service.hpp \
  /opt/homebrew/include/asio/io_service_strand.hpp \
  /opt/homebrew/include/asio/ip/address.hpp \
  /opt/homebrew/include/asio/ip/address_v4.hpp \
  /opt/homebrew/include/asio/detail/array.hpp \
  /opt/homebrew/include/asio/detail/winsock_init.hpp \
  /opt/homebrew/include/asio/ip/impl/address_v4.hpp \
  /opt/homebrew/include/asio/ip/impl/address_v4.ipp \
  /opt/homebrew/include/asio/ip/address_v6.hpp \
  /opt/homebrew/include/asio/ip/impl/address_v6.hpp \
  /opt/homebrew/include/asio/ip/impl/address_v6.ipp \
  /opt/homebrew/include/asio/ip/bad_address_cast.hpp \
  /opt/homebrew/include/asio/ip/impl/address.hpp \
  /opt/homebrew/include/asio/ip/impl/address.ipp \
  /opt/homebrew/include/asio/ip/address_v4_iterator.hpp \
  /opt/homebrew/include/asio/ip/address_v4_range.hpp \
  /opt/homebrew/include/asio/ip/address_v6_iterator.hpp \
  /opt/homebrew/include/asio/ip/address_v6_range.hpp \
  /opt/homebrew/include/asio/ip/network_v4.hpp \
  /opt/homebrew/include/asio/ip/impl/network_v4.hpp \
  /opt/homebrew/include/asio/ip/impl/network_v4.ipp \
  /opt/homebrew/include/asio/ip/network_v6.hpp \
  /opt/homebrew/include/asio/ip/impl/network_v6.hpp \
  /opt/homebrew/include/asio/ip/impl/network_v6.ipp \
  /opt/homebrew/include/asio/ip/basic_endpoint.hpp \
  /opt/homebrew/include/asio/ip/detail/endpoint.hpp \
  /opt/homebrew/include/asio/ip/detail/impl/endpoint.ipp \
  /opt/homebrew/include/asio/ip/impl/basic_endpoint.hpp \
  /opt/homebrew/include/asio/ip/basic_resolver.hpp \
  /opt/homebrew/include/asio/ip/basic_resolver_iterator.hpp \
  /opt/homebrew/include/asio/ip/basic_resolver_entry.hpp \
  /opt/homebrew/include/asio/ip/basic_resolver_query.hpp \
  /opt/homebrew/include/asio/ip/resolver_query_base.hpp \
  /opt/homebrew/include/asio/ip/resolver_base.hpp \
  /opt/homebrew/include/asio/ip/basic_resolver_results.hpp \
  /opt/homebrew/include/asio/detail/resolver_service.hpp \
  /opt/homebrew/include/asio/detail/resolve_endpoint_op.hpp \
  /opt/homebrew/include/asio/detail/resolve_op.hpp \
  /opt/homebrew/include/asio/detail/resolve_query_op.hpp \
  /opt/homebrew/include/asio/detail/resolver_service_base.hpp \
  /opt/homebrew/include/asio/detail/impl/resolver_service_base.ipp \
  /opt/homebrew/include/asio/ip/host_name.hpp \
  /opt/homebrew/include/asio/ip/impl/host_name.ipp \
  /opt/homebrew/include/asio/ip/icmp.hpp \
  /opt/homebrew/include/asio/ip/multicast.hpp \
  /opt/homebrew/include/asio/ip/detail/socket_option.hpp \
  /opt/homebrew/include/asio/ip/tcp.hpp \
  /opt/homebrew/include/asio/ip/udp.hpp \
  /opt/homebrew/include/asio/ip/unicast.hpp \
  /opt/homebrew/include/asio/ip/v6_only.hpp \
  /opt/homebrew/include/asio/is_read_buffered.hpp \
  /opt/homebrew/include/asio/is_write_buffered.hpp \
  /opt/homebrew/include/asio/local/basic_endpoint.hpp \
  /opt/homebrew/include/asio/local/detail/endpoint.hpp \
  /opt/homebrew/include/asio/local/detail/impl/endpoint.ipp \
  /opt/homebrew/include/asio/local/connect_pair.hpp \
  /opt/homebrew/include/asio/local/datagram_protocol.hpp \
  /opt/homebrew/include/asio/local/seq_packet_protocol.hpp \
  /opt/homebrew/include/asio/local/stream_protocol.hpp \
  /opt/homebrew/include/asio/packaged_task.hpp \
  /opt/homebrew/include/asio/detail/future.hpp \
  /opt/homebrew/include/asio/placeholders.hpp \
  /opt/homebrew/include/asio/posix/basic_descriptor.hpp \
  /opt/homebrew/include/asio/posix/basic_stream_descriptor.hpp \
  /opt/homebrew/include/asio/posix/descriptor.hpp \
  /opt/homebrew/include/asio/posix/stream_descriptor.hpp \
  /opt/homebrew/include/asio/prepend.hpp \
  /opt/homebrew/include/asio/impl/prepend.hpp \
  /opt/homebrew/include/asio/random_access_file.hpp \
  /opt/homebrew/include/asio/read.hpp \
  /opt/homebrew/include/asio/impl/read.hpp \
  /opt/homebrew/include/asio/read_at.hpp \
  /opt/homebrew/include/asio/impl/read_at.hpp \
  /opt/homebrew/include/asio/read_until.hpp \
  /opt/homebrew/include/asio/detail/regex_fwd.hpp \
  /opt/homebrew/include/asio/impl/read_until.hpp \
  /opt/homebrew/include/asio/readable_pipe.hpp \
  /opt/homebrew/include/asio/redirect_error.hpp \
  /opt/homebrew/include/asio/impl/redirect_error.hpp \
  /opt/homebrew/include/asio/require_concept.hpp \
  /opt/homebrew/include/asio/traits/require_concept_member.hpp \
  /opt/homebrew/include/asio/traits/require_concept_free.hpp \
  /opt/homebrew/include/asio/traits/static_require_concept.hpp \
  /opt/homebrew/include/asio/serial_port.hpp \
  /opt/homebrew/include/asio/signal_set.hpp \
  /opt/homebrew/include/asio/static_thread_pool.hpp \
  /opt/homebrew/include/asio/thread_pool.hpp \
  /opt/homebrew/include/asio/impl/thread_pool.hpp \
  /opt/homebrew/include/asio/detail/blocking_executor_op.hpp \
  /opt/homebrew/include/asio/impl/thread_pool.ipp \
  /opt/homebrew/include/asio/strand.hpp \
  /opt/homebrew/include/asio/detail/strand_executor_service.hpp \
  /opt/homebrew/include/asio/detail/impl/strand_executor_service.hpp \
  /opt/homebrew/include/asio/detail/impl/strand_executor_service.ipp \
  /opt/homebrew/include/asio/stream_file.hpp \
  /opt/homebrew/include/asio/streambuf.hpp \
  /opt/homebrew/include/asio/system_timer.hpp \
  /opt/homebrew/include/asio/this_coro.hpp \
  /opt/homebrew/include/asio/thread.hpp \
  /opt/homebrew/include/asio/time_traits.hpp \
  /opt/homebrew/include/asio/use_awaitable.hpp \
  /opt/homebrew/include/asio/use_future.hpp \
  /opt/homebrew/include/asio/impl/use_future.hpp \
  /opt/homebrew/include/asio/windows/basic_object_handle.hpp \
  /opt/homebrew/include/asio/windows/basic_overlapped_handle.hpp \
  /opt/homebrew/include/asio/windows/basic_random_access_handle.hpp \
  /opt/homebrew/include/asio/windows/basic_stream_handle.hpp \
  /opt/homebrew/include/asio/windows/object_handle.hpp \
  /opt/homebrew/include/asio/windows/overlapped_handle.hpp \
  /opt/homebrew/include/asio/windows/overlapped_ptr.hpp \
  /opt/homebrew/include/asio/windows/random_access_handle.hpp \
  /opt/homebrew/include/asio/windows/stream_handle.hpp \
  /opt/homebrew/include/asio/writable_pipe.hpp \
  /opt/homebrew/include/asio/write_at.hpp \
  /opt/homebrew/include/asio/impl/write_at.hpp \
  /opt/homebrew/include/websocketpp/common/chrono.hpp \
  /opt/homebrew/include/websocketpp/common/type_traits.hpp \
  /opt/homebrew/include/websocketpp/base64/base64.hpp \
  /opt/homebrew/include/websocketpp/transport/asio/security/none.hpp \
  /opt/homebrew/include/websocketpp/transport/asio/security/base.hpp \
  /opt/homebrew/include/websocketpp/transport/asio/security/tls.hpp \
  /opt/homebrew/include/websocketpp/common/asio_ssl.hpp \
  /opt/homebrew/include/asio/ssl.hpp \
  /opt/homebrew/include/asio/ssl/context.hpp \
  /opt/homebrew/include/asio/ssl/context_base.hpp \
  /opt/homebrew/include/asio/ssl/detail/openssl_types.hpp \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/conf.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/macros.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/opensslconf.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/configuration.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/opensslv.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/bio.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/e_os2.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/crypto.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/safestack.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/stack.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/types.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/cryptoerr.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/symhacks.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/cryptoerr_legacy.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/core.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/bioerr.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/lhash.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/conferr.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/conftypes.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/ssl.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/e_ostime.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/comp.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/comperr.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/x509.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/buffer.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/buffererr.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/evp.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/core_dispatch.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/indicator.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/params.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/bn.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/bnerr.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/evperr.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/objects.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/obj_mac.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/asn1.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/asn1err.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/objectserr.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/ec.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/ecerr.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/rsa.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/rsaerr.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/dsa.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/dh.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/dherr.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/dsaerr.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/sha.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/x509err.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/x509_vfy.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/pkcs7.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/pkcs7err.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/http.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/pem.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/pemerr.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/hmac.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/async.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/asyncerr.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/ct.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/cterr.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/sslerr.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/sslerr_legacy.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/prov_ssl.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/ssl2.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/ssl3.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/tls1.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/dtls1.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/srtp.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/quic.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/engine.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/rand.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/randerr.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/ui.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/uierr.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/err.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/engineerr.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/x509v3.h \
  /opt/homebrew/Cellar/openssl@3/3.5.0/include/openssl/x509v3err.h \
  /opt/homebrew/include/asio/ssl/detail/openssl_init.hpp \
  /opt/homebrew/include/asio/ssl/detail/impl/openssl_init.ipp \
  /opt/homebrew/include/asio/ssl/detail/password_callback.hpp \
  /opt/homebrew/include/asio/ssl/detail/verify_callback.hpp \
  /opt/homebrew/include/asio/ssl/verify_context.hpp \
  /opt/homebrew/include/asio/ssl/verify_mode.hpp \
  /opt/homebrew/include/asio/ssl/impl/context.hpp \
  /opt/homebrew/include/asio/ssl/impl/context.ipp \
  /opt/homebrew/include/asio/ssl/error.hpp \
  /opt/homebrew/include/asio/ssl/impl/error.ipp \
  /opt/homebrew/include/asio/ssl/rfc2818_verification.hpp \
  /opt/homebrew/include/asio/ssl/impl/rfc2818_verification.ipp \
  /opt/homebrew/include/asio/ssl/host_name_verification.hpp \
  /opt/homebrew/include/asio/ssl/impl/host_name_verification.ipp \
  /opt/homebrew/include/asio/ssl/stream.hpp \
  /opt/homebrew/include/asio/ssl/detail/buffered_handshake_op.hpp \
  /opt/homebrew/include/asio/ssl/detail/engine.hpp \
  /opt/homebrew/include/asio/ssl/stream_base.hpp \
  /opt/homebrew/include/asio/ssl/detail/impl/engine.ipp \
  /opt/homebrew/include/asio/ssl/detail/handshake_op.hpp \
  /opt/homebrew/include/asio/ssl/detail/io.hpp \
  /opt/homebrew/include/asio/ssl/detail/stream_core.hpp \
  /opt/homebrew/include/asio/ssl/detail/read_op.hpp \
  /opt/homebrew/include/asio/ssl/detail/shutdown_op.hpp \
  /opt/homebrew/include/asio/ssl/detail/write_op.hpp \
  /opt/homebrew/include/websocketpp/config/asio_no_tls_client.hpp \
  /opt/homebrew/include/websocketpp/client.hpp \
  /opt/homebrew/include/websocketpp/roles/client_endpoint.hpp \
  /opt/homebrew/include/websocketpp/endpoint.hpp \
  /opt/homebrew/include/websocketpp/connection.hpp \
  /opt/homebrew/include/websocketpp/close.hpp \
  /opt/homebrew/include/websocketpp/utf8_validator.hpp \
  /opt/homebrew/include/websocketpp/processors/processor.hpp \
  /opt/homebrew/include/websocketpp/processors/base.hpp \
  /opt/homebrew/include/websocketpp/impl/connection_impl.hpp \
  /opt/homebrew/include/websocketpp/processors/hybi00.hpp \
  /opt/homebrew/include/websocketpp/common/md5.hpp \
  /opt/homebrew/include/websocketpp/processors/hybi07.hpp \
  /opt/homebrew/include/websocketpp/processors/hybi08.hpp \
  /opt/homebrew/include/websocketpp/processors/hybi13.hpp \
  /opt/homebrew/include/websocketpp/sha1/sha1.hpp \
  /opt/homebrew/include/websocketpp/version.hpp \
  /opt/homebrew/include/websocketpp/impl/endpoint_impl.hpp \
  /opt/homebrew/include/boost/asio/strand.hpp \
  /opt/homebrew/include/boost/asio/detail/config.hpp \
  /opt/homebrew/include/boost/config.hpp \
  /opt/homebrew/include/boost/config/user.hpp \
  /opt/homebrew/include/boost/config/detail/select_compiler_config.hpp \
  /opt/homebrew/include/boost/config/compiler/clang.hpp \
  /opt/homebrew/include/boost/config/compiler/clang_version.hpp \
  /opt/homebrew/include/boost/config/detail/select_stdlib_config.hpp \
  /opt/homebrew/include/boost/config/stdlib/libcpp.hpp \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/shared_mutex \
  /opt/homebrew/include/boost/config/detail/select_platform_config.hpp \
  /opt/homebrew/include/boost/config/platform/macos.hpp \
  /opt/homebrew/include/boost/config/detail/posix_features.hpp \
  /opt/homebrew/include/boost/config/detail/suffix.hpp \
  /opt/homebrew/include/boost/config/helper_macros.hpp \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/memory_resource \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/charconv \
  /opt/homebrew/include/boost/config/detail/cxx_composite.hpp \
  /opt/homebrew/include/boost/version.hpp \
  /opt/homebrew/include/boost/asio/detail/strand_executor_service.hpp \
  /opt/homebrew/include/boost/asio/detail/atomic_count.hpp \
  /opt/homebrew/include/boost/asio/detail/executor_op.hpp \
  /opt/homebrew/include/boost/asio/detail/fenced_block.hpp \
  /opt/homebrew/include/boost/asio/detail/std_fenced_block.hpp \
  /opt/homebrew/include/boost/asio/detail/noncopyable.hpp \
  /opt/homebrew/include/boost/asio/detail/push_options.hpp \
  /opt/homebrew/include/boost/asio/detail/pop_options.hpp \
  /opt/homebrew/include/boost/asio/detail/handler_alloc_helpers.hpp \
  /opt/homebrew/include/boost/asio/detail/memory.hpp \
  /opt/homebrew/include/boost/asio/detail/cstdint.hpp \
  /opt/homebrew/include/boost/asio/detail/throw_exception.hpp \
  /opt/homebrew/include/boost/throw_exception.hpp \
  /opt/homebrew/include/boost/exception/exception.hpp \
  /opt/homebrew/include/boost/assert/source_location.hpp \
  /opt/homebrew/include/boost/cstdint.hpp \
  /opt/homebrew/include/boost/config/workaround.hpp \
  /opt/homebrew/include/boost/align/aligned_alloc.hpp \
  /opt/homebrew/include/boost/align/detail/aligned_alloc_posix.hpp \
  /opt/homebrew/include/boost/align/detail/is_alignment.hpp \
  /opt/homebrew/include/boost/assert.hpp \
  /opt/homebrew/include/boost/asio/detail/recycling_allocator.hpp \
  /opt/homebrew/include/boost/asio/detail/thread_context.hpp \
  /opt/homebrew/include/boost/asio/detail/call_stack.hpp \
  /opt/homebrew/include/boost/asio/detail/tss_ptr.hpp \
  /opt/homebrew/include/boost/asio/detail/keyword_tss_ptr.hpp \
  /opt/homebrew/include/boost/asio/detail/impl/thread_context.ipp \
  /opt/homebrew/include/boost/asio/detail/thread_info_base.hpp \
  /opt/homebrew/include/boost/asio/multiple_exceptions.hpp \
  /opt/homebrew/include/boost/asio/impl/multiple_exceptions.ipp \
  /opt/homebrew/include/boost/asio/associated_allocator.hpp \
  /opt/homebrew/include/boost/asio/associator.hpp \
  /opt/homebrew/include/boost/asio/detail/functional.hpp \
  /opt/homebrew/include/boost/asio/detail/type_traits.hpp \
  /opt/homebrew/include/boost/asio/detail/scheduler_operation.hpp \
  /opt/homebrew/include/boost/system/error_code.hpp \
  /opt/homebrew/include/boost/system/detail/error_code.hpp \
  /opt/homebrew/include/boost/system/is_error_code_enum.hpp \
  /opt/homebrew/include/boost/system/detail/error_category.hpp \
  /opt/homebrew/include/boost/system/detail/config.hpp \
  /opt/homebrew/include/boost/system/detail/error_condition.hpp \
  /opt/homebrew/include/boost/system/detail/generic_category.hpp \
  /opt/homebrew/include/boost/system/detail/generic_category_message.hpp \
  /opt/homebrew/include/boost/system/detail/enable_if.hpp \
  /opt/homebrew/include/boost/system/detail/is_same.hpp \
  /opt/homebrew/include/boost/system/detail/errc.hpp \
  /opt/homebrew/include/boost/system/is_error_condition_enum.hpp \
  /opt/homebrew/include/boost/system/detail/cerrno.hpp \
  /opt/homebrew/include/boost/system/detail/append_int.hpp \
  /opt/homebrew/include/boost/system/detail/snprintf.hpp \
  /opt/homebrew/include/boost/system/detail/system_category.hpp \
  /opt/homebrew/include/boost/system/detail/system_category_impl.hpp \
  /opt/homebrew/include/boost/system/detail/system_category_message.hpp \
  /opt/homebrew/include/boost/system/api_config.hpp \
  /opt/homebrew/include/boost/system/detail/interop_category.hpp \
  /opt/homebrew/include/boost/system/detail/std_category.hpp \
  /opt/homebrew/include/boost/system/error_category.hpp \
  /opt/homebrew/include/boost/system/detail/error_category_impl.hpp \
  /opt/homebrew/include/boost/system/detail/std_category_impl.hpp \
  /opt/homebrew/include/boost/system/detail/mutex.hpp \
  /opt/homebrew/include/boost/system/error_condition.hpp \
  /opt/homebrew/include/boost/system/errc.hpp \
  /opt/homebrew/include/boost/system/generic_category.hpp \
  /opt/homebrew/include/boost/system/system_category.hpp \
  /opt/homebrew/include/boost/system/detail/throws.hpp \
  /opt/homebrew/include/boost/asio/detail/handler_tracking.hpp \
  /opt/homebrew/include/boost/asio/detail/impl/handler_tracking.ipp \
  /opt/homebrew/include/boost/asio/detail/op_queue.hpp \
  /opt/homebrew/include/boost/asio/detail/mutex.hpp \
  /opt/homebrew/include/boost/asio/detail/posix_mutex.hpp \
  /opt/homebrew/include/boost/asio/detail/scoped_lock.hpp \
  /opt/homebrew/include/boost/asio/detail/impl/posix_mutex.ipp \
  /opt/homebrew/include/boost/asio/detail/throw_error.hpp \
  /opt/homebrew/include/boost/asio/detail/impl/throw_error.ipp \
  /opt/homebrew/include/boost/system/system_error.hpp \
  /opt/homebrew/include/boost/asio/error.hpp \
  /opt/homebrew/include/boost/cerrno.hpp \
  /opt/homebrew/include/boost/asio/impl/error.ipp \
  /opt/homebrew/include/boost/asio/detail/scoped_ptr.hpp \
  /opt/homebrew/include/boost/asio/execution.hpp \
  /opt/homebrew/include/boost/asio/execution/allocator.hpp \
  /opt/homebrew/include/boost/asio/execution/executor.hpp \
  /opt/homebrew/include/boost/asio/execution/invocable_archetype.hpp \
  /opt/homebrew/include/boost/asio/traits/equality_comparable.hpp \
  /opt/homebrew/include/boost/asio/traits/execute_member.hpp \
  /opt/homebrew/include/boost/asio/is_applicable_property.hpp \
  /opt/homebrew/include/boost/asio/traits/query_static_constexpr_member.hpp \
  /opt/homebrew/include/boost/asio/traits/static_query.hpp \
  /opt/homebrew/include/boost/asio/execution/any_executor.hpp \
  /opt/homebrew/include/boost/asio/detail/assert.hpp \
  /opt/homebrew/include/boost/asio/detail/cstddef.hpp \
  /opt/homebrew/include/boost/asio/detail/executor_function.hpp \
  /opt/homebrew/include/boost/asio/detail/non_const_lvalue.hpp \
  /opt/homebrew/include/boost/asio/execution/bad_executor.hpp \
  /opt/homebrew/include/boost/asio/execution/impl/bad_executor.ipp \
  /opt/homebrew/include/boost/asio/execution/blocking.hpp \
  /opt/homebrew/include/boost/asio/prefer.hpp \
  /opt/homebrew/include/boost/asio/traits/prefer_free.hpp \
  /opt/homebrew/include/boost/asio/traits/prefer_member.hpp \
  /opt/homebrew/include/boost/asio/traits/require_free.hpp \
  /opt/homebrew/include/boost/asio/traits/require_member.hpp \
  /opt/homebrew/include/boost/asio/traits/static_require.hpp \
  /opt/homebrew/include/boost/asio/query.hpp \
  /opt/homebrew/include/boost/asio/traits/query_member.hpp \
  /opt/homebrew/include/boost/asio/traits/query_free.hpp \
  /opt/homebrew/include/boost/asio/require.hpp \
  /opt/homebrew/include/boost/asio/execution/blocking_adaptation.hpp \
  /opt/homebrew/include/boost/asio/detail/event.hpp \
  /opt/homebrew/include/boost/asio/detail/posix_event.hpp \
  /opt/homebrew/include/boost/asio/detail/impl/posix_event.ipp \
  /opt/homebrew/include/boost/asio/execution/context.hpp \
  /opt/homebrew/include/boost/asio/execution/context_as.hpp \
  /opt/homebrew/include/boost/asio/execution/mapping.hpp \
  /opt/homebrew/include/boost/asio/execution/occupancy.hpp \
  /opt/homebrew/include/boost/asio/execution/outstanding_work.hpp \
  /opt/homebrew/include/boost/asio/execution/prefer_only.hpp \
  /opt/homebrew/include/boost/asio/execution/relationship.hpp \
  /opt/homebrew/include/boost/asio/execution_context.hpp \
  /opt/homebrew/include/boost/asio/impl/execution_context.hpp \
  /opt/homebrew/include/boost/asio/detail/handler_type_requirements.hpp \
  /opt/homebrew/include/boost/asio/async_result.hpp \
  /opt/homebrew/include/boost/asio/default_completion_token.hpp \
  /opt/homebrew/include/boost/asio/deferred.hpp \
  /opt/homebrew/include/boost/asio/detail/utility.hpp \
  /opt/homebrew/include/boost/asio/impl/deferred.hpp \
  /opt/homebrew/include/boost/asio/detail/service_registry.hpp \
  /opt/homebrew/include/boost/asio/detail/impl/service_registry.hpp \
  /opt/homebrew/include/boost/asio/detail/impl/service_registry.ipp \
  /opt/homebrew/include/boost/asio/impl/execution_context.ipp \
  /opt/homebrew/include/boost/asio/detail/impl/strand_executor_service.hpp \
  /opt/homebrew/include/boost/asio/executor_work_guard.hpp \
  /opt/homebrew/include/boost/asio/associated_executor.hpp \
  /opt/homebrew/include/boost/asio/is_executor.hpp \
  /opt/homebrew/include/boost/asio/detail/is_executor.hpp \
  /opt/homebrew/include/boost/asio/system_executor.hpp \
  /opt/homebrew/include/boost/asio/impl/system_executor.hpp \
  /opt/homebrew/include/boost/asio/detail/global.hpp \
  /opt/homebrew/include/boost/asio/detail/posix_global.hpp \
  /opt/homebrew/include/boost/asio/system_context.hpp \
  /opt/homebrew/include/boost/asio/detail/scheduler.hpp \
  /opt/homebrew/include/boost/asio/detail/conditionally_enabled_event.hpp \
  /opt/homebrew/include/boost/asio/detail/conditionally_enabled_mutex.hpp \
  /opt/homebrew/include/boost/asio/detail/null_event.hpp \
  /opt/homebrew/include/boost/asio/detail/impl/null_event.ipp \
  /opt/homebrew/include/boost/asio/detail/scheduler_task.hpp \
  /opt/homebrew/include/boost/asio/detail/thread.hpp \
  /opt/homebrew/include/boost/asio/detail/posix_thread.hpp \
  /opt/homebrew/include/boost/asio/detail/impl/posix_thread.ipp \
  /opt/homebrew/include/boost/asio/detail/impl/scheduler.ipp \
  /opt/homebrew/include/boost/asio/config.hpp \
  /opt/homebrew/include/boost/asio/impl/config.hpp \
  /opt/homebrew/include/boost/asio/impl/config.ipp \
  /opt/homebrew/include/boost/asio/detail/concurrency_hint.hpp \
  /opt/homebrew/include/boost/asio/detail/limits.hpp \
  /opt/homebrew/include/boost/asio/detail/scheduler_thread_info.hpp \
  /opt/homebrew/include/boost/asio/detail/signal_blocker.hpp \
  /opt/homebrew/include/boost/asio/detail/posix_signal_blocker.hpp \
  /opt/homebrew/include/boost/asio/detail/reactor.hpp \
  /opt/homebrew/include/boost/asio/detail/kqueue_reactor.hpp \
  /opt/homebrew/include/boost/asio/detail/object_pool.hpp \
  /opt/homebrew/include/boost/asio/detail/reactor_op.hpp \
  /opt/homebrew/include/boost/asio/detail/operation.hpp \
  /opt/homebrew/include/boost/asio/detail/select_interrupter.hpp \
  /opt/homebrew/include/boost/asio/detail/pipe_select_interrupter.hpp \
  /opt/homebrew/include/boost/asio/detail/impl/pipe_select_interrupter.ipp \
  /opt/homebrew/include/boost/asio/detail/socket_types.hpp \
  /opt/homebrew/include/boost/asio/detail/timer_queue_base.hpp \
  /opt/homebrew/include/boost/asio/detail/timer_queue_set.hpp \
  /opt/homebrew/include/boost/asio/detail/impl/timer_queue_set.ipp \
  /opt/homebrew/include/boost/asio/detail/wait_op.hpp \
  /opt/homebrew/include/boost/asio/detail/impl/kqueue_reactor.hpp \
  /opt/homebrew/include/boost/asio/detail/impl/kqueue_reactor.ipp \
  /opt/homebrew/include/boost/asio/detail/thread_group.hpp \
  /opt/homebrew/include/boost/asio/impl/system_context.hpp \
  /opt/homebrew/include/boost/asio/impl/system_context.ipp \
  /opt/homebrew/include/boost/asio/defer.hpp \
  /opt/homebrew/include/boost/asio/detail/initiate_defer.hpp \
  /opt/homebrew/include/boost/asio/detail/work_dispatcher.hpp \
  /opt/homebrew/include/boost/asio/detail/bind_handler.hpp \
  /opt/homebrew/include/boost/asio/detail/handler_cont_helpers.hpp \
  /opt/homebrew/include/boost/asio/handler_continuation_hook.hpp \
  /opt/homebrew/include/boost/asio/dispatch.hpp \
  /opt/homebrew/include/boost/asio/detail/initiate_dispatch.hpp \
  /opt/homebrew/include/boost/asio/post.hpp \
  /opt/homebrew/include/boost/asio/detail/initiate_post.hpp \
  /opt/homebrew/include/boost/asio/detail/impl/strand_executor_service.ipp \
  /opt/homebrew/include/boost/asio/steady_timer.hpp \
  /opt/homebrew/include/boost/asio/basic_waitable_timer.hpp \
  /opt/homebrew/include/boost/asio/any_io_executor.hpp \
  /opt/homebrew/include/boost/asio/impl/any_io_executor.ipp \
  /opt/homebrew/include/boost/asio/detail/chrono_time_traits.hpp \
  /opt/homebrew/include/boost/asio/detail/deadline_timer_service.hpp \
  /opt/homebrew/include/boost/asio/associated_cancellation_slot.hpp \
  /opt/homebrew/include/boost/asio/cancellation_signal.hpp \
  /opt/homebrew/include/boost/asio/cancellation_type.hpp \
  /opt/homebrew/include/boost/asio/impl/cancellation_signal.ipp \
  /opt/homebrew/include/boost/asio/detail/socket_ops.hpp \
  /opt/homebrew/include/boost/asio/detail/impl/socket_ops.ipp \
  /opt/homebrew/include/boost/asio/detail/timer_queue.hpp \
  /opt/homebrew/include/boost/asio/detail/date_time_fwd.hpp \
  /opt/homebrew/include/boost/asio/detail/timer_queue_ptime.hpp \
  /opt/homebrew/include/boost/asio/time_traits.hpp \
  /opt/homebrew/include/boost/date_time/posix_time/posix_time_types.hpp \
  /opt/homebrew/include/boost/date_time/time_clock.hpp \
  /opt/homebrew/include/boost/date_time/c_time.hpp \
  /opt/homebrew/include/boost/date_time/compiler_config.hpp \
  /opt/homebrew/include/boost/detail/workaround.hpp \
  /opt/homebrew/include/boost/date_time/locale_config.hpp \
  /opt/homebrew/include/boost/shared_ptr.hpp \
  /opt/homebrew/include/boost/smart_ptr/shared_ptr.hpp \
  /opt/homebrew/include/boost/smart_ptr/detail/shared_count.hpp \
  /opt/homebrew/include/boost/smart_ptr/bad_weak_ptr.hpp \
  /opt/homebrew/include/boost/smart_ptr/detail/sp_counted_base.hpp \
  /opt/homebrew/include/boost/smart_ptr/detail/sp_has_gcc_intrinsics.hpp \
  /opt/homebrew/include/boost/smart_ptr/detail/sp_has_sync_intrinsics.hpp \
  /opt/homebrew/include/boost/smart_ptr/detail/deprecated_macros.hpp \
  /opt/homebrew/include/boost/config/pragma_message.hpp \
  /opt/homebrew/include/boost/smart_ptr/detail/sp_counted_base_gcc_atomic.hpp \
  /opt/homebrew/include/boost/smart_ptr/detail/sp_typeinfo_.hpp \
  /opt/homebrew/include/boost/smart_ptr/detail/sp_counted_impl.hpp \
  /opt/homebrew/include/boost/core/checked_delete.hpp \
  /opt/homebrew/include/boost/core/addressof.hpp \
  /opt/homebrew/include/boost/smart_ptr/detail/sp_disable_deprecated.hpp \
  /opt/homebrew/include/boost/smart_ptr/detail/sp_convertible.hpp \
  /opt/homebrew/include/boost/smart_ptr/detail/sp_noexcept.hpp \
  /opt/homebrew/include/boost/smart_ptr/detail/spinlock_pool.hpp \
  /opt/homebrew/include/boost/smart_ptr/detail/spinlock.hpp \
  /opt/homebrew/include/boost/smart_ptr/detail/spinlock_gcc_atomic.hpp \
  /opt/homebrew/include/boost/smart_ptr/detail/yield_k.hpp \
  /opt/homebrew/include/boost/core/yield_primitives.hpp \
  /opt/homebrew/include/boost/core/detail/sp_thread_pause.hpp \
  /opt/homebrew/include/boost/core/detail/sp_thread_yield.hpp \
  /opt/homebrew/include/boost/core/detail/sp_thread_sleep.hpp \
  /opt/homebrew/include/boost/smart_ptr/detail/local_sp_deleter.hpp \
  /opt/homebrew/include/boost/smart_ptr/detail/local_counted_base.hpp \
  /opt/homebrew/include/boost/date_time/microsec_time_clock.hpp \
  /opt/homebrew/include/boost/date_time/posix_time/ptime.hpp \
  /opt/homebrew/include/boost/date_time/posix_time/posix_time_system.hpp \
  /opt/homebrew/include/boost/date_time/posix_time/posix_time_config.hpp \
  /opt/homebrew/include/boost/limits.hpp \
  /opt/homebrew/include/boost/config/no_tr1/cmath.hpp \
  /opt/homebrew/include/boost/date_time/time_duration.hpp \
  /opt/homebrew/include/boost/core/enable_if.hpp \
  /opt/homebrew/include/boost/date_time/special_defs.hpp \
  /opt/homebrew/include/boost/date_time/time_defs.hpp \
  /opt/homebrew/include/boost/operators.hpp \
  /opt/homebrew/include/boost/static_assert.hpp \
  /opt/homebrew/include/boost/type_traits/is_integral.hpp \
  /opt/homebrew/include/boost/type_traits/integral_constant.hpp \
  /opt/homebrew/include/boost/date_time/time_resolution_traits.hpp \
  /opt/homebrew/include/boost/date_time/int_adapter.hpp \
  /opt/homebrew/include/boost/date_time/gregorian/gregorian_types.hpp \
  /opt/homebrew/include/boost/date_time/date.hpp \
  /opt/homebrew/include/boost/date_time/year_month_day.hpp \
  /opt/homebrew/include/boost/date_time/period.hpp \
  /opt/homebrew/include/boost/date_time/gregorian/greg_calendar.hpp \
  /opt/homebrew/include/boost/date_time/gregorian/greg_weekday.hpp \
  /opt/homebrew/include/boost/date_time/constrained_value.hpp \
  /opt/homebrew/include/boost/type_traits/conditional.hpp \
  /opt/homebrew/include/boost/type_traits/is_base_of.hpp \
  /opt/homebrew/include/boost/type_traits/is_base_and_derived.hpp \
  /opt/homebrew/include/boost/type_traits/intrinsics.hpp \
  /opt/homebrew/include/boost/type_traits/detail/config.hpp \
  /opt/homebrew/include/boost/type_traits/remove_cv.hpp \
  /opt/homebrew/include/boost/type_traits/is_same.hpp \
  /opt/homebrew/include/boost/type_traits/is_class.hpp \
  /opt/homebrew/include/boost/date_time/date_defs.hpp \
  /opt/homebrew/include/boost/date_time/gregorian/greg_day_of_year.hpp \
  /opt/homebrew/include/boost/date_time/gregorian_calendar.hpp \
  /opt/homebrew/include/boost/date_time/gregorian_calendar.ipp \
  /opt/homebrew/include/boost/date_time/gregorian/greg_ymd.hpp \
  /opt/homebrew/include/boost/date_time/gregorian/greg_day.hpp \
  /opt/homebrew/include/boost/date_time/gregorian/greg_year.hpp \
  /opt/homebrew/include/boost/date_time/gregorian/greg_month.hpp \
  /opt/homebrew/include/boost/date_time/gregorian/greg_duration.hpp \
  /opt/homebrew/include/boost/date_time/date_duration.hpp \
  /opt/homebrew/include/boost/date_time/date_duration_types.hpp \
  /opt/homebrew/include/boost/date_time/gregorian/greg_duration_types.hpp \
  /opt/homebrew/include/boost/date_time/gregorian/greg_date.hpp \
  /opt/homebrew/include/boost/date_time/adjust_functors.hpp \
  /opt/homebrew/include/boost/date_time/wrapping_int.hpp \
  /opt/homebrew/include/boost/date_time/date_generators.hpp \
  /opt/homebrew/include/boost/date_time/date_clock_device.hpp \
  /opt/homebrew/include/boost/date_time/date_iterator.hpp \
  /opt/homebrew/include/boost/date_time/time_system_split.hpp \
  /opt/homebrew/include/boost/date_time/time_system_counted.hpp \
  /opt/homebrew/include/boost/date_time/time.hpp \
  /opt/homebrew/include/boost/date_time/posix_time/date_duration_operators.hpp \
  /opt/homebrew/include/boost/date_time/posix_time/posix_time_duration.hpp \
  /opt/homebrew/include/boost/numeric/conversion/cast.hpp \
  /opt/homebrew/include/boost/type.hpp \
  /opt/homebrew/include/boost/numeric/conversion/converter.hpp \
  /opt/homebrew/include/boost/numeric/conversion/conversion_traits.hpp \
  /opt/homebrew/include/boost/numeric/conversion/detail/conversion_traits.hpp \
  /opt/homebrew/include/boost/type_traits/is_arithmetic.hpp \
  /opt/homebrew/include/boost/type_traits/is_floating_point.hpp \
  /opt/homebrew/include/boost/numeric/conversion/detail/meta.hpp \
  /opt/homebrew/include/boost/mpl/if.hpp \
  /opt/homebrew/include/boost/mpl/aux_/value_wknd.hpp \
  /opt/homebrew/include/boost/mpl/aux_/static_cast.hpp \
  /opt/homebrew/include/boost/mpl/aux_/config/workaround.hpp \
  /opt/homebrew/include/boost/mpl/aux_/config/integral.hpp \
  /opt/homebrew/include/boost/mpl/aux_/config/msvc.hpp \
  /opt/homebrew/include/boost/mpl/aux_/config/eti.hpp \
  /opt/homebrew/include/boost/mpl/aux_/na_spec.hpp \
  /opt/homebrew/include/boost/mpl/lambda_fwd.hpp \
  /opt/homebrew/include/boost/mpl/void_fwd.hpp \
  /opt/homebrew/include/boost/mpl/aux_/adl_barrier.hpp \
  /opt/homebrew/include/boost/mpl/aux_/config/adl.hpp \
  /opt/homebrew/include/boost/mpl/aux_/config/intel.hpp \
  /opt/homebrew/include/boost/mpl/aux_/config/gcc.hpp \
  /opt/homebrew/include/boost/mpl/aux_/na.hpp \
  /opt/homebrew/include/boost/mpl/bool.hpp \
  /opt/homebrew/include/boost/mpl/bool_fwd.hpp \
  /opt/homebrew/include/boost/mpl/integral_c_tag.hpp \
  /opt/homebrew/include/boost/mpl/aux_/config/static_constant.hpp \
  /opt/homebrew/include/boost/mpl/aux_/na_fwd.hpp \
  /opt/homebrew/include/boost/mpl/aux_/config/ctps.hpp \
  /opt/homebrew/include/boost/mpl/aux_/config/lambda.hpp \
  /opt/homebrew/include/boost/mpl/aux_/config/ttp.hpp \
  /opt/homebrew/include/boost/mpl/int.hpp \
  /opt/homebrew/include/boost/mpl/int_fwd.hpp \
  /opt/homebrew/include/boost/mpl/aux_/nttp_decl.hpp \
  /opt/homebrew/include/boost/mpl/aux_/config/nttp.hpp \
  /opt/homebrew/include/boost/mpl/aux_/integral_wrapper.hpp \
  /opt/homebrew/include/boost/preprocessor/cat.hpp \
  /opt/homebrew/include/boost/preprocessor/config/config.hpp \
  /opt/homebrew/include/boost/mpl/aux_/lambda_arity_param.hpp \
  /opt/homebrew/include/boost/mpl/aux_/template_arity_fwd.hpp \
  /opt/homebrew/include/boost/mpl/aux_/arity.hpp \
  /opt/homebrew/include/boost/mpl/aux_/config/dtp.hpp \
  /opt/homebrew/include/boost/mpl/aux_/preprocessor/params.hpp \
  /opt/homebrew/include/boost/mpl/aux_/config/preprocessor.hpp \
  /opt/homebrew/include/boost/preprocessor/comma_if.hpp \
  /opt/homebrew/include/boost/preprocessor/punctuation/comma_if.hpp \
  /opt/homebrew/include/boost/preprocessor/control/if.hpp \
  /opt/homebrew/include/boost/preprocessor/control/iif.hpp \
  /opt/homebrew/include/boost/preprocessor/logical/bool.hpp \
  /opt/homebrew/include/boost/preprocessor/config/limits.hpp \
  /opt/homebrew/include/boost/preprocessor/logical/limits/bool_256.hpp \
  /opt/homebrew/include/boost/preprocessor/facilities/empty.hpp \
  /opt/homebrew/include/boost/preprocessor/punctuation/comma.hpp \
  /opt/homebrew/include/boost/preprocessor/repeat.hpp \
  /opt/homebrew/include/boost/preprocessor/repetition/repeat.hpp \
  /opt/homebrew/include/boost/preprocessor/debug/error.hpp \
  /opt/homebrew/include/boost/preprocessor/detail/auto_rec.hpp \
  /opt/homebrew/include/boost/preprocessor/detail/limits/auto_rec_256.hpp \
  /opt/homebrew/include/boost/preprocessor/tuple/eat.hpp \
  /opt/homebrew/include/boost/preprocessor/repetition/limits/repeat_256.hpp \
  /opt/homebrew/include/boost/preprocessor/inc.hpp \
  /opt/homebrew/include/boost/preprocessor/arithmetic/inc.hpp \
  /opt/homebrew/include/boost/preprocessor/arithmetic/limits/inc_256.hpp \
  /opt/homebrew/include/boost/mpl/aux_/preprocessor/enum.hpp \
  /opt/homebrew/include/boost/mpl/aux_/preprocessor/def_params_tail.hpp \
  /opt/homebrew/include/boost/mpl/limits/arity.hpp \
  /opt/homebrew/include/boost/preprocessor/logical/and.hpp \
  /opt/homebrew/include/boost/preprocessor/logical/bitand.hpp \
  /opt/homebrew/include/boost/preprocessor/identity.hpp \
  /opt/homebrew/include/boost/preprocessor/facilities/identity.hpp \
  /opt/homebrew/include/boost/preprocessor/empty.hpp \
  /opt/homebrew/include/boost/preprocessor/arithmetic/add.hpp \
  /opt/homebrew/include/boost/preprocessor/arithmetic/dec.hpp \
  /opt/homebrew/include/boost/preprocessor/arithmetic/limits/dec_256.hpp \
  /opt/homebrew/include/boost/preprocessor/control/while.hpp \
  /opt/homebrew/include/boost/preprocessor/list/fold_left.hpp \
  /opt/homebrew/include/boost/preprocessor/list/detail/fold_left.hpp \
  /opt/homebrew/include/boost/preprocessor/control/expr_iif.hpp \
  /opt/homebrew/include/boost/preprocessor/list/adt.hpp \
  /opt/homebrew/include/boost/preprocessor/detail/is_binary.hpp \
  /opt/homebrew/include/boost/preprocessor/detail/check.hpp \
  /opt/homebrew/include/boost/preprocessor/logical/compl.hpp \
  /opt/homebrew/include/boost/preprocessor/list/detail/limits/fold_left_256.hpp \
  /opt/homebrew/include/boost/preprocessor/list/limits/fold_left_256.hpp \
  /opt/homebrew/include/boost/preprocessor/list/fold_right.hpp \
  /opt/homebrew/include/boost/preprocessor/list/detail/fold_right.hpp \
  /opt/homebrew/include/boost/preprocessor/list/reverse.hpp \
  /opt/homebrew/include/boost/preprocessor/list/detail/limits/fold_right_256.hpp \
  /opt/homebrew/include/boost/preprocessor/control/detail/while.hpp \
  /opt/homebrew/include/boost/preprocessor/control/detail/limits/while_256.hpp \
  /opt/homebrew/include/boost/preprocessor/control/limits/while_256.hpp \
  /opt/homebrew/include/boost/preprocessor/logical/bitor.hpp \
  /opt/homebrew/include/boost/preprocessor/tuple/elem.hpp \
  /opt/homebrew/include/boost/preprocessor/facilities/expand.hpp \
  /opt/homebrew/include/boost/preprocessor/facilities/overload.hpp \
  /opt/homebrew/include/boost/preprocessor/variadic/size.hpp \
  /opt/homebrew/include/boost/preprocessor/facilities/check_empty.hpp \
  /opt/homebrew/include/boost/preprocessor/variadic/has_opt.hpp \
  /opt/homebrew/include/boost/preprocessor/variadic/limits/size_64.hpp \
  /opt/homebrew/include/boost/preprocessor/tuple/rem.hpp \
  /opt/homebrew/include/boost/preprocessor/tuple/detail/is_single_return.hpp \
  /opt/homebrew/include/boost/preprocessor/variadic/elem.hpp \
  /opt/homebrew/include/boost/preprocessor/variadic/limits/elem_64.hpp \
  /opt/homebrew/include/boost/preprocessor/arithmetic/detail/is_maximum_number.hpp \
  /opt/homebrew/include/boost/preprocessor/comparison/equal.hpp \
  /opt/homebrew/include/boost/preprocessor/comparison/not_equal.hpp \
  /opt/homebrew/include/boost/preprocessor/comparison/limits/not_equal_256.hpp \
  /opt/homebrew/include/boost/preprocessor/arithmetic/detail/maximum_number.hpp \
  /opt/homebrew/include/boost/preprocessor/arithmetic/detail/is_minimum_number.hpp \
  /opt/homebrew/include/boost/preprocessor/logical/not.hpp \
  /opt/homebrew/include/boost/preprocessor/arithmetic/sub.hpp \
  /opt/homebrew/include/boost/mpl/aux_/config/overload_resolution.hpp \
  /opt/homebrew/include/boost/mpl/aux_/lambda_support.hpp \
  /opt/homebrew/include/boost/mpl/eval_if.hpp \
  /opt/homebrew/include/boost/mpl/equal_to.hpp \
  /opt/homebrew/include/boost/mpl/aux_/comparison_op.hpp \
  /opt/homebrew/include/boost/mpl/aux_/numeric_op.hpp \
  /opt/homebrew/include/boost/mpl/numeric_cast.hpp \
  /opt/homebrew/include/boost/mpl/apply_wrap.hpp \
  /opt/homebrew/include/boost/mpl/aux_/has_apply.hpp \
  /opt/homebrew/include/boost/mpl/has_xxx.hpp \
  /opt/homebrew/include/boost/mpl/aux_/type_wrapper.hpp \
  /opt/homebrew/include/boost/mpl/aux_/yes_no.hpp \
  /opt/homebrew/include/boost/mpl/aux_/config/arrays.hpp \
  /opt/homebrew/include/boost/mpl/aux_/config/has_xxx.hpp \
  /opt/homebrew/include/boost/mpl/aux_/config/msvc_typename.hpp \
  /opt/homebrew/include/boost/preprocessor/array/elem.hpp \
  /opt/homebrew/include/boost/preprocessor/array/data.hpp \
  /opt/homebrew/include/boost/preprocessor/array/size.hpp \
  /opt/homebrew/include/boost/preprocessor/repetition/enum_params.hpp \
  /opt/homebrew/include/boost/preprocessor/repetition/enum_trailing_params.hpp \
  /opt/homebrew/include/boost/mpl/aux_/config/has_apply.hpp \
  /opt/homebrew/include/boost/mpl/aux_/msvc_never_true.hpp \
  /opt/homebrew/include/boost/mpl/aux_/config/use_preprocessed.hpp \
  /opt/homebrew/include/boost/mpl/aux_/include_preprocessed.hpp \
  /opt/homebrew/include/boost/mpl/aux_/config/compiler.hpp \
  /opt/homebrew/include/boost/preprocessor/stringize.hpp \
  /opt/homebrew/include/boost/mpl/aux_/preprocessed/gcc/apply_wrap.hpp \
  /opt/homebrew/include/boost/mpl/tag.hpp \
  /opt/homebrew/include/boost/mpl/void.hpp \
  /opt/homebrew/include/boost/mpl/aux_/has_tag.hpp \
  /opt/homebrew/include/boost/mpl/aux_/numeric_cast_utils.hpp \
  /opt/homebrew/include/boost/mpl/aux_/config/forwarding.hpp \
  /opt/homebrew/include/boost/mpl/aux_/msvc_eti_base.hpp \
  /opt/homebrew/include/boost/mpl/aux_/is_msvc_eti_arg.hpp \
  /opt/homebrew/include/boost/mpl/aux_/preprocessed/gcc/equal_to.hpp \
  /opt/homebrew/include/boost/mpl/not.hpp \
  /opt/homebrew/include/boost/mpl/aux_/nested_type_wknd.hpp \
  /opt/homebrew/include/boost/mpl/and.hpp \
  /opt/homebrew/include/boost/mpl/aux_/preprocessed/gcc/and.hpp \
  /opt/homebrew/include/boost/mpl/identity.hpp \
  /opt/homebrew/include/boost/numeric/conversion/detail/int_float_mixture.hpp \
  /opt/homebrew/include/boost/numeric/conversion/int_float_mixture_enum.hpp \
  /opt/homebrew/include/boost/numeric/conversion/detail/sign_mixture.hpp \
  /opt/homebrew/include/boost/numeric/conversion/sign_mixture_enum.hpp \
  /opt/homebrew/include/boost/numeric/conversion/detail/udt_builtin_mixture.hpp \
  /opt/homebrew/include/boost/numeric/conversion/udt_builtin_mixture_enum.hpp \
  /opt/homebrew/include/boost/numeric/conversion/detail/is_subranged.hpp \
  /opt/homebrew/include/boost/mpl/multiplies.hpp \
  /opt/homebrew/include/boost/mpl/times.hpp \
  /opt/homebrew/include/boost/mpl/aux_/arithmetic_op.hpp \
  /opt/homebrew/include/boost/mpl/integral_c.hpp \
  /opt/homebrew/include/boost/mpl/integral_c_fwd.hpp \
  /opt/homebrew/include/boost/mpl/aux_/largest_int.hpp \
  /opt/homebrew/include/boost/mpl/aux_/preprocessed/gcc/times.hpp \
  /opt/homebrew/include/boost/mpl/aux_/preprocessor/default_params.hpp \
  /opt/homebrew/include/boost/mpl/less.hpp \
  /opt/homebrew/include/boost/mpl/aux_/preprocessed/gcc/less.hpp \
  /opt/homebrew/include/boost/numeric/conversion/converter_policies.hpp \
  /opt/homebrew/include/boost/numeric/conversion/detail/converter.hpp \
  /opt/homebrew/include/boost/numeric/conversion/bounds.hpp \
  /opt/homebrew/include/boost/numeric/conversion/detail/bounds.hpp \
  /opt/homebrew/include/boost/numeric/conversion/numeric_cast_traits.hpp \
  /opt/homebrew/include/boost/numeric/conversion/detail/numeric_cast_traits.hpp \
  /opt/homebrew/include/boost/numeric/conversion/detail/preprocessed/numeric_cast_traits_common.hpp \
  /opt/homebrew/include/boost/numeric/conversion/detail/preprocessed/numeric_cast_traits_long_long.hpp \
  /opt/homebrew/include/boost/date_time/posix_time/time_period.hpp \
  /opt/homebrew/include/boost/date_time/time_iterator.hpp \
  /opt/homebrew/include/boost/date_time/dst_rules.hpp \
  /opt/homebrew/include/boost/asio/detail/impl/timer_queue_ptime.ipp \
  /opt/homebrew/include/boost/asio/detail/timer_scheduler.hpp \
  /opt/homebrew/include/boost/asio/detail/timer_scheduler_fwd.hpp \
  /opt/homebrew/include/boost/asio/detail/wait_handler.hpp \
  /opt/homebrew/include/boost/asio/detail/handler_work.hpp \
  /opt/homebrew/include/boost/asio/associated_immediate_executor.hpp \
  /opt/homebrew/include/boost/asio/detail/io_object_impl.hpp \
  /opt/homebrew/include/boost/asio/io_context.hpp \
  /opt/homebrew/include/boost/asio/detail/chrono.hpp \
  /opt/homebrew/include/boost/asio/detail/wrapped_handler.hpp \
  /opt/homebrew/include/boost/asio/impl/io_context.hpp \
  /opt/homebrew/include/boost/asio/detail/completion_handler.hpp \
  /opt/homebrew/include/boost/asio/impl/io_context.ipp \
  /opt/homebrew/include/boost/asio/io_context_strand.hpp \
  /opt/homebrew/include/boost/asio/detail/strand_service.hpp \
  /opt/homebrew/include/boost/asio/detail/impl/strand_service.hpp \
  /opt/homebrew/include/boost/asio/detail/impl/strand_service.ipp \
  /opt/homebrew/include/boost/asio/wait_traits.hpp \
  /opt/homebrew/include/boost/asio/bind_executor.hpp \
  /opt/homebrew/include/boost/asio/detail/initiation_base.hpp \
  /opt/homebrew/include/boost/asio/uses_executor.hpp \
  /opt/homebrew/include/boost/asio/executor.hpp \
  /opt/homebrew/include/boost/asio/impl/executor.hpp \
  /opt/homebrew/include/boost/asio/impl/executor.ipp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../models/trade.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/risk/risk_metrics.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../hardware/hardware_optimization_manager.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../hardware/apple_silicon/m_series_optimizer.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/IOKit.framework/Headers/IOKitLib.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/CoreFoundation.framework/Headers/CFBase.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/CoreFoundation.framework/Headers/CFAvailability.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/os/availability.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/Block.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/MacTypes.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/ConditionalMacros.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/lib/clang/20/include/ptrauth.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/CoreFoundation.framework/Headers/CFDictionary.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/CoreFoundation.framework/Headers/CFRunLoop.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/CoreFoundation.framework/Headers/CFArray.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/CoreFoundation.framework/Headers/CFDate.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/CoreFoundation.framework/Headers/CFString.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/CoreFoundation.framework/Headers/CFData.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/CoreFoundation.framework/Headers/CFCharacterSet.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/CoreFoundation.framework/Headers/CFLocale.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/CoreFoundation.framework/Headers/CFNotificationCenter.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/CoreFoundation.framework/Headers/CFError.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/IOKit.framework/Headers/IOTypes.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/IOKit.framework/Headers/IOReturn.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/libkern/OSTypes.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/device/device_types.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/IOKit.framework/Headers/IOMapTypes.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/IOKit.framework/Headers/IOKitKeys.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/System/Library/Frameworks/IOKit.framework/Headers/OSMessageNotification.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/dispatch/dispatch.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/os/base.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/os/object.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/os/workgroup.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/os/workgroup_base.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/os/clock.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/os/workgroup_object.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/os/workgroup_interval.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/os/workgroup_parallel.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/dispatch/base.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/dispatch/time.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/dispatch/object.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/dispatch/queue.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/dispatch/block.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/dispatch/source.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/dispatch/group.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/dispatch/semaphore.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/dispatch/once.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/dispatch/data.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/dispatch/io.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/dispatch/workloop.h \
  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/dispatch/dispatch_swift_shims.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../hardware/apple_silicon/../../utils/logger.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../models/liquidity_types.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/analysis_types.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../models/market_regime.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/trading_brain_types.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/wave_types.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/trading_brain_methods/wave_analysis.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/trading_brain_methods/../wave_types.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/trading_brain_methods/../../models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/trading_brain_methods/wave_analysis_types.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/trading_brain_methods/../../market/market_context.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/trading_brain_methods/timeframe_types.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/trading_brain_methods/../timeframe/timeframe_selection.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/trading_brain_methods/../../validation/market_condition.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../validation/ml_validation_framework.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../validation/../models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../validation/../models/trade_result.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../validation/../trading/trade_types.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../validation/validation_types.h \
  /opt/homebrew/lib/QtWidgets.framework/Headers/QWidget \
  /opt/homebrew/lib/QtWidgets.framework/Headers/qwidget.h \
  /opt/homebrew/include/QtWidgets/qtwidgetsglobal.h \
  /opt/homebrew/include/QtGui/qtguiglobal.h \
  /opt/homebrew/include/QtGui/qtgui-config.h \
  /opt/homebrew/include/QtGui/qtguiexports.h \
  /opt/homebrew/include/QtWidgets/qtwidgets-config.h \
  /opt/homebrew/include/QtWidgets/qtwidgetsexports.h \
  /opt/homebrew/include/QtGui/qwindowdefs.h \
  /opt/homebrew/include/QtCore/qmargins.h \
  /opt/homebrew/include/QtGui/qaction.h \
  /opt/homebrew/include/QtGui/qkeysequence.h \
  /opt/homebrew/include/QtGui/qicon.h \
  /opt/homebrew/include/QtCore/qsize.h \
  /opt/homebrew/include/QtGui/qpixmap.h \
  /opt/homebrew/include/QtGui/qpaintdevice.h \
  /opt/homebrew/include/QtCore/qrect.h \
  /opt/homebrew/include/QtCore/qpoint.h \
  /opt/homebrew/include/QtGui/qcolor.h \
  /opt/homebrew/include/QtGui/qrgb.h \
  /opt/homebrew/include/QtGui/qrgba64.h \
  /opt/homebrew/include/QtGui/qimage.h \
  /opt/homebrew/include/QtGui/qpixelformat.h \
  /opt/homebrew/include/QtGui/qtransform.h \
  /opt/homebrew/include/QtGui/qpolygon.h \
  /opt/homebrew/include/QtGui/qregion.h \
  /opt/homebrew/include/QtCore/qspan.h \
  /opt/homebrew/include/QtCore/q20iterator.h \
  /opt/homebrew/include/QtCore/qline.h \
  /opt/homebrew/include/QtGui/qpalette.h \
  /opt/homebrew/include/QtGui/qbrush.h \
  /opt/homebrew/include/QtGui/qfont.h \
  /opt/homebrew/include/QtCore/qendian.h \
  /opt/homebrew/include/QtGui/qfontmetrics.h \
  /opt/homebrew/include/QtGui/qfontinfo.h \
  /opt/homebrew/include/QtGui/qfontvariableaxis.h \
  /opt/homebrew/include/QtWidgets/qsizepolicy.h \
  /opt/homebrew/include/QtGui/qcursor.h \
  /opt/homebrew/include/QtGui/qbitmap.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../validation/system_component.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../validation/emergency_protocols.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../validation/risk_event.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../validation/system_stressor.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../validation/validation_metrics.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../validation/../models/performance_metrics.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../validation/market_condition.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../validation/risk_parameters.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../validation/historical_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../validation/../testing/test_metrics.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../validation/../testing/test_environment.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../validation/../testing/test_results.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../validation/../ai/patterns/scenario_result.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../validation/../ai/patterns/../../models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../validation/../ai/patterns/../../models/trade_result.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../validation/../ai/risk/risk_metrics.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../validation/performance_metrics.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../validation/../models/qdatetime_compat.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../validation/../models/qt_compat.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/realtime/market_analysis_integrator.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../market/market_context.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../models/order_flow_analysis.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../models/../market/volume_profile.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/../models/volume_price_analysis.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/trend_line.h \
  /opt/homebrew/lib/QtCore.framework/Headers/QPoint \
  /opt/homebrew/lib/QtCore.framework/Headers/qpoint.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/trading_engine.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../config/config.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../config/../models/trading_config.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../api/trading_api.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../database/redis_manager.h \
  /opt/homebrew/include/sw/redis++/redis++.h \
  /opt/homebrew/include/sw/redis++/redis.h \
  /opt/homebrew/include/sw/redis++/connection_pool.h \
  /opt/homebrew/include/sw/redis++/connection.h \
  /opt/homebrew/include/hiredis/hiredis.h \
  /opt/homebrew/include/hiredis/read.h \
  /opt/homebrew/include/hiredis/sds.h \
  /opt/homebrew/include/hiredis/alloc.h \
  /opt/homebrew/include/sw/redis++/errors.h \
  /opt/homebrew/include/sw/redis++/reply.h \
  /opt/homebrew/include/sw/redis++/utils.h \
  /opt/homebrew/include/sw/redis++/cxx_utils.h \
  /opt/homebrew/include/sw/redis++/tls.h \
  /opt/homebrew/include/sw/redis++/hiredis_features.h \
  /opt/homebrew/include/sw/redis++/sentinel.h \
  /opt/homebrew/include/sw/redis++/shards.h \
  /opt/homebrew/include/sw/redis++/command_options.h \
  /opt/homebrew/include/sw/redis++/subscriber.h \
  /opt/homebrew/include/sw/redis++/command.h \
  /opt/homebrew/include/sw/redis++/command_args.h \
  /opt/homebrew/include/sw/redis++/pipeline.h \
  /opt/homebrew/include/sw/redis++/transaction.h \
  /opt/homebrew/include/sw/redis++/redis_uri.h \
  /opt/homebrew/include/sw/redis++/redis.hpp \
  /opt/homebrew/include/sw/redis++/redis_cluster.h \
  /opt/homebrew/include/sw/redis++/shards_pool.h \
  /opt/homebrew/include/sw/redis++/redis_cluster.hpp \
  /opt/homebrew/include/sw/redis++/queued_redis.h \
  /opt/homebrew/include/sw/redis++/queued_redis.hpp \
  /opt/homebrew/include/sw/redis++/redis++.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../database/../trading/trading_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/models/cache_performance.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/utils/logger.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../database/mongodb_manager.h \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/client.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/client-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/config/prelude.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/config/compiler.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/config/config.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/config/export.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/config/version.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/config/postlude.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/client_session-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/collection-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/database-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/auto_encryption-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/client_encryption-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/pool-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/client_session.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/bulk_write-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/index_view-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/search_index_view-fwd.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/document/view.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/document/view-fwd.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/config/prelude.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/config/util.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/config/compiler.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/config/config.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/config/export.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/config/version.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/config/postlude.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/document/element.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/array/element-fwd.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/document/element-fwd.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/types-fwd.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/types/bson_value/value-fwd.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/types/bson_value/view-fwd.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/stdx/optional.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/stdx/operators.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/stdx/type_traits.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/stdx/string_view.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/client_session.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/client_session-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/transaction.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/transaction-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/read_concern-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/read_preference-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/write_concern-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/database.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/client_encryption-fwd.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/document/view_or_value.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/document/value.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/document/value-fwd.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/array/view.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/array/view-fwd.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/array/element.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/view_or_value.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/view_or_value-fwd.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/string/view_or_value.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/string/view_or_value-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/collection.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/builder/basic/array.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/builder/basic/array-fwd.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/array/value.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/array/value-fwd.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/builder/basic/impl.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/builder/basic/sub_array.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/builder/basic/sub_array-fwd.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/builder/basic/helpers.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/builder/concatenate.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/builder/concatenate-fwd.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/array/view_or_value.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/builder/core.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/builder/core-fwd.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/types.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/decimal128.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/decimal128-fwd.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/oid.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/oid-fwd.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/builder/basic/sub_document.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/builder/basic/sub_document-fwd.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/builder/basic/kvp.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/builder/basic/document.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/builder/basic/document-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/bulk_write.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/model/write.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/model/write-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/model/delete_many.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/model/delete_many-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/hint.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/hint-fwd.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/types/bson_value/view.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/model/delete_one.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/model/delete_one-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/model/insert_one.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/model/insert_one-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/model/replace_one.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/model/replace_one-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/model/update_many.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/model/update_many-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/pipeline.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/pipeline-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/model/update_one.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/model/update_one-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/write_type.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/write_type-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/bulk_write.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/bulk_write-fwd.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/types/bson_value/view_or_value.hpp \
  /opt/homebrew/include/bsoncxx/v_noabi/bsoncxx/types/bson_value/value.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/write_concern.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/uri-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/result/bulk_write.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/result/bulk_write-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/change_stream.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/change_stream-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/cursor.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/cursor-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/index_view.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/index_model.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/index_model-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/index.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/index-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/index_view.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/index_view-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/aggregate.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/aggregate-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/read_concern.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/read_preference.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/events/topology_description-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/change_stream.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/change_stream-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/count.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/count-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/delete.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/delete-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/distinct.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/distinct-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/estimated_document_count.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/estimated_document_count-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/find.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/find-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/find_one_and_delete.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/find_one_and_delete-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/find_one_and_replace.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/find_one_and_replace-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/find_one_common_options.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/find_one_common_options-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/find_one_and_update.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/find_one_and_update-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/insert.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/insert-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/replace.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/replace-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/update.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/update-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/result/delete.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/result/delete-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/result/insert_many.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/result/insert_many-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/result/insert_one.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/result/insert_one-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/result/replace_one.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/result/replace_one-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/result/update.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/result/update-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/search_index_view.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/search_index_model.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/search_index_model-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/gridfs/bucket.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/gridfs/bucket-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/gridfs/downloader.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/gridfs/downloader-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/gridfs/uploader.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/gridfs/uploader-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/result/gridfs/upload.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/result/gridfs/upload-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/gridfs/bucket.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/gridfs/bucket-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/gridfs/upload.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/gridfs/upload-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/client.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/client-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/apm.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/apm-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/events/command_failed_event.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/events/command_failed_event-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/events/command_started_event.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/events/command_started_event-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/events/command_succeeded_event.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/events/command_succeeded_event-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/events/heartbeat_failed_event.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/events/heartbeat_failed_event-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/events/heartbeat_started_event.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/events/heartbeat_started_event-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/events/heartbeat_succeeded_event.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/events/heartbeat_succeeded_event-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/events/server_changed_event.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/events/server_changed_event-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/events/server_description.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/events/server_description-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/events/server_closed_event.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/events/server_closed_event-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/events/server_opening_event.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/events/server_opening_event-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/events/topology_changed_event.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/events/topology_changed_event-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/events/topology_description.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/events/topology_closed_event.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/events/topology_closed_event-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/events/topology_opening_event.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/events/topology_opening_event-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/auto_encryption.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/server_api.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/server_api-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/tls.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/tls-fwd.hpp \
  /opt/homebrew/include/mongocxx/v_noabi/mongocxx/options/client_encryption.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/database/mongocxx/uri.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/database/mongocxx/instance.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/database/bsoncxx/json.hpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../database/../models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../database/../models/analytics_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../database/../models/performance_metrics.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../database/../config/config.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../database/../models/tournament_metrics.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../trading/trading_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../models/trade_result.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../models/trading_session.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../models/../config/config.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../models/trade.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../trading/trading_engine_components.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../events/event_manager.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../events/../../models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../events/../../models/market_regime.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../events/../../models/trade_result.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../recovery/recovery_manager.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../recovery/martingale_strategy.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../recovery/anti_martingale_strategy.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../recovery/../../recovery/fibonacci_recovery.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../recovery/../../recovery/../models/trade_result.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../recovery/percentage_recovery.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../recovery/dynamic_recovery.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../recovery/../../models/trading_config.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../recovery/fibonacci_recovery.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../analysis/market_analyzer.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../models/market_regime.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../models/market_state.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../models/../binaryoptionsbot/trading/market_microstructure.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../models/../binaryoptionsbot/trading/../../models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../models/../binaryoptionsbot/trading/../../models/market_types.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../models/../binaryoptionsbot/trading/../../models/order_flow_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../models/../binaryoptionsbot/trading/../../models/volume_profile_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../models/../binaryoptionsbot/trading/../../models/market_depth_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../models/tournament_config.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../models/tournament_metrics.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../models/trade_parameters.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../models/order_flow_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../models/market_depth_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../models/volume_profile_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../models/ai_prediction.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../models/position.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../utils/logger.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../terminal/utils/tts_manager.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/patterns/pattern_recognizer.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../validation/multi_timeframe_validation.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../hardware/apple_silicon/m_series_optimizer.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../hardware/hardware_optimization_manager.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/trade_executor.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/trade_anonymizer.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/trade_executor_singleton.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../core/trading_engine.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../strategies/strategy_manager.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../strategies/../../models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../strategies/../../models/market_regime.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../strategies/../../models/market_state.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../strategies/../../trading/trading_strategy.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../strategies/advanced_strategy.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../strategies/trading_strategy.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../strategies/../trading/trading_strategy.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../strategies/../trading/../../models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../strategies/../trading/../../models/performance_metrics.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../strategies/../trading/../../trading/trading_strategy.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../strategies/../../config/config.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../strategies/../../utils/logger.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../strategies/../../models/trading_config.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../strategies/../../models/trading_history.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../strategies/../../models/../trading/trading_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../strategies/../../models/trade.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../strategies/../core/trading_engine.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../risk/risk_manager.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../risk/../../models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../risk/../../models/trading_config.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../risk/../../models/market_regime.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../trading/market_microstructure.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../hardware/hardware_optimizer.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../hardware/../../hardware/apple_silicon/m_series_optimizer.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../hardware/../../hardware/hardware_optimization_manager.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../trading/trading_interface.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../trading/../../models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../trading/../../models/trade_result.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../trading/../../models/trade_parameters.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../trading/risk_metrics.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../trading/../trading/trading_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../trading/../models/trade_result.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../account/account_manager.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../data/data_manager.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../data/../../models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../trading/concrete_trading_interface.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../trading/../../models/trade.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../trading/../../models/qdatetime_compat.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../services/news_service.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../services/../models/news_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../services/../api/news/news_api_client.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../services/../api/news/../../models/news_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../services/../api/news/../../database/redis_manager.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../autopilot/auto_pilot_config.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../autopilot/auto_pilot_manager.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../autopilot/../ai/ml/advanced_ai_model.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../autopilot/../trading/risk_metrics.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/../../src/binaryoptionsbot/core/../../ai/ml/ensemble_strategy.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/moc_trade_executor.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/moc_trade_executor_singleton.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/NRE5J3DUVE/moc_trading_engine.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/F5AZ3Z3EBT/moc_event_handler.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/F5AZ3Z3EBT/../../src/binaryoptionsbot/events/event_handler.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/F5AZ3Z3EBT/../../src/binaryoptionsbot/events/../core/trade_executor.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/F5AZ3Z3EBT/../../src/binaryoptionsbot/events/../../models/trade_result.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/F5AZ3Z3EBT/moc_event_manager.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/F5AZ3Z3EBT/../../src/binaryoptionsbot/events/event_manager.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/OYJZ6EBVCR/moc_dynamic_recovery.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/OYJZ6EBVCR/../../src/binaryoptionsbot/recovery/dynamic_recovery.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/OYJZ6EBVCR/moc_recovery_manager.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/OYJZ6EBVCR/../../src/binaryoptionsbot/recovery/recovery_manager.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/WGSZSVNJLE/moc_risk_manager.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/WGSZSVNJLE/../../src/binaryoptionsbot/risk/risk_manager.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/MLPIKIXBDN/moc_strategy_manager.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/MLPIKIXBDN/../../src/binaryoptionsbot/strategies/strategy_manager.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/TJYP5TAQIJ/moc_concrete_trading_interface.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/TJYP5TAQIJ/../../src/binaryoptionsbot/trading/concrete_trading_interface.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/TJYP5TAQIJ/moc_market_microstructure.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/TJYP5TAQIJ/../../src/binaryoptionsbot/trading/market_microstructure.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/TJYP5TAQIJ/moc_trading_interface.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/TJYP5TAQIJ/../../src/binaryoptionsbot/trading/trading_interface.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/M7KQ2GL7XF/moc_database_connection.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/M7KQ2GL7XF/../../src/database/database_connection.h \
  /opt/homebrew/lib/QtSql.framework/Headers/QSqlDatabase \
  /opt/homebrew/lib/QtSql.framework/Headers/qsqldatabase.h \
  /opt/homebrew/include/QtSql/qtsqlglobal.h \
  /opt/homebrew/include/QtSql/qtsql-config.h \
  /opt/homebrew/include/QtSql/qtsqlexports.h \
  /opt/homebrew/include/QtCore/qmetaobject.h \
  /opt/homebrew/lib/QtSql.framework/Headers/QSqlError \
  /opt/homebrew/lib/QtSql.framework/Headers/qsqlerror.h \
  /opt/homebrew/lib/QtSql.framework/Headers/QSqlQuery \
  /opt/homebrew/lib/QtSql.framework/Headers/qsqlquery.h \
  /opt/homebrew/include/QtSql/qsqldatabase.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/36B24NPE3N/moc_exchange_adapter.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/36B24NPE3N/../../src/exchange/exchange_adapter.h \
  /opt/homebrew/lib/QtCore.framework/Headers/QList \
  /opt/homebrew/lib/QtCore.framework/Headers/qlist.h \
  /opt/homebrew/lib/QtCore.framework/Headers/QMap \
  /opt/homebrew/lib/QtCore.framework/Headers/qmap.h \
  /opt/homebrew/lib/QtNetwork.framework/Headers/QNetworkAccessManager \
  /opt/homebrew/lib/QtNetwork.framework/Headers/qnetworkaccessmanager.h \
  /opt/homebrew/include/QtNetwork/qtnetworkglobal.h \
  /opt/homebrew/include/QtNetwork/qtnetwork-config.h \
  /opt/homebrew/include/QtNetwork/qtnetworkexports.h \
  /opt/homebrew/include/QtNetwork/qnetworkrequest.h \
  /opt/homebrew/include/QtNetwork/qhttpheaders.h \
  /opt/homebrew/include/QtCore/QSharedDataPointer \
  /opt/homebrew/include/QtCore/QString /opt/homebrew/include/QtCore/QUrl \
  /opt/homebrew/include/QtCore/qurl.h \
  /opt/homebrew/include/QtCore/QVariant \
  /opt/homebrew/include/QtCore/q26numeric.h \
  /opt/homebrew/include/QtCore/QList \
  /opt/homebrew/include/QtCore/QObject \
  /opt/homebrew/include/QtNetwork/QSslConfiguration \
  /opt/homebrew/include/QtNetwork/qsslconfiguration.h \
  /opt/homebrew/include/QtNetwork/qsslsocket.h \
  /opt/homebrew/include/QtNetwork/qtcpsocket.h \
  /opt/homebrew/include/QtNetwork/qabstractsocket.h \
  /opt/homebrew/include/QtCore/qiodevice.h \
  /opt/homebrew/include/QtNetwork/qhostaddress.h \
  /opt/homebrew/include/QtNetwork/qsslerror.h \
  /opt/homebrew/include/QtNetwork/qsslcertificate.h \
  /opt/homebrew/include/QtCore/qcryptographichash.h \
  /opt/homebrew/include/QtNetwork/qssl.h \
  /opt/homebrew/include/QtCore/QFlags \
  /opt/homebrew/include/QtNetwork/QSslPreSharedKeyAuthenticator \
  /opt/homebrew/include/QtNetwork/qsslpresharedkeyauthenticator.h \
  /opt/homebrew/include/QtCore/QMetaType \
  /opt/homebrew/lib/QtNetwork.framework/Headers/QNetworkRequest \
  /opt/homebrew/lib/QtNetwork.framework/Headers/qnetworkrequest.h \
  /opt/homebrew/lib/QtNetwork.framework/Headers/QNetworkReply \
  /opt/homebrew/lib/QtNetwork.framework/Headers/qnetworkreply.h \
  /opt/homebrew/include/QtCore/QIODevice \
  /opt/homebrew/include/QtNetwork/QNetworkRequest \
  /opt/homebrew/include/QtNetwork/QNetworkAccessManager \
  /opt/homebrew/include/QtNetwork/QSslError \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/B3S2CYUO4J/moc_market_analyzer.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/B3S2CYUO4J/../../src/market/market_analyzer.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/M4YTXQ7V2H/moc_data_feed_manager.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/M4YTXQ7V2H/../../src/models/data_feed_manager.h \
  /opt/homebrew/lib/QtCore.framework/Headers/QTimer \
  /opt/homebrew/lib/QtCore.framework/Headers/qtimer.h \
  /opt/homebrew/include/QtCore/qbasictimer.h \
  /opt/homebrew/include/QtCore/qabstracteventdispatcher.h \
  /opt/homebrew/include/QtCore/qeventloop.h \
  /opt/homebrew/include/QtCore/qdeadlinetimer.h \
  /opt/homebrew/include/QtCore/qelapsedtimer.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/M4YTXQ7V2H/../../src/models/../trading/trading_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/M4YTXQ7V2H/../../src/models/../exchange/exchange_adapter.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/M4YTXQ7V2H/moc_indicator_calculator.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/M4YTXQ7V2H/../../src/models/indicator_calculator.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/GAJ3MXGHID/moc_ai_visualization.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/GAJ3MXGHID/../../src/terminal/components/ai_visualization.h \
  /opt/homebrew/lib/QtCharts.framework/Headers/QChart \
  /opt/homebrew/lib/QtCharts.framework/Headers/qchart.h \
  /opt/homebrew/include/QtCharts/QAbstractSeries \
  /opt/homebrew/include/QtCharts/qabstractseries.h \
  /opt/homebrew/include/QtCharts/QChartGlobal \
  /opt/homebrew/include/QtCharts/qchartglobal.h \
  /opt/homebrew/include/QtCharts/qtcharts-config.h \
  /opt/homebrew/include/QtCharts/qtchartsexports.h \
  /opt/homebrew/include/QtCharts/QAbstractAxis \
  /opt/homebrew/include/QtCharts/qabstractaxis.h \
  /opt/homebrew/include/QtGui/QPen /opt/homebrew/include/QtGui/qpen.h \
  /opt/homebrew/include/QtGui/QFont \
  /opt/homebrew/include/QtCharts/QLegend \
  /opt/homebrew/include/QtCharts/qlegend.h \
  /opt/homebrew/include/QtWidgets/QGraphicsWidget \
  /opt/homebrew/include/QtWidgets/qgraphicswidget.h \
  /opt/homebrew/include/QtWidgets/qgraphicslayoutitem.h \
  /opt/homebrew/include/QtGui/qevent.h \
  /opt/homebrew/include/QtCore/qcoreevent.h \
  /opt/homebrew/include/QtGui/qeventpoint.h \
  /opt/homebrew/include/QtGui/qvector2d.h \
  /opt/homebrew/include/QtGui/qvectornd.h \
  /opt/homebrew/include/QtGui/qpointingdevice.h \
  /opt/homebrew/include/QtGui/qinputdevice.h \
  /opt/homebrew/include/QtGui/qscreen.h \
  /opt/homebrew/include/QtCore/QRect /opt/homebrew/include/QtCore/QSize \
  /opt/homebrew/include/QtCore/QSizeF \
  /opt/homebrew/include/QtGui/QTransform \
  /opt/homebrew/include/QtCore/qnativeinterface.h \
  /opt/homebrew/include/QtGui/qscreen_platform.h \
  /opt/homebrew/include/QtGui/qguiapplication.h \
  /opt/homebrew/include/QtCore/qcoreapplication.h \
  /opt/homebrew/include/QtCore/qcoreapplication_platform.h \
  /opt/homebrew/include/QtGui/qinputmethod.h \
  /opt/homebrew/include/QtGui/qguiapplication_platform.h \
  /opt/homebrew/include/QtWidgets/qgraphicsitem.h \
  /opt/homebrew/include/QtGui/qpainterpath.h \
  /opt/homebrew/include/QtGui/QBrush \
  /opt/homebrew/include/QtCore/QMargins \
  /opt/homebrew/lib/QtCharts.framework/Headers/QChartView \
  /opt/homebrew/lib/QtCharts.framework/Headers/qchartview.h \
  /opt/homebrew/include/QtCharts/QChart \
  /opt/homebrew/include/QtWidgets/QGraphicsView \
  /opt/homebrew/include/QtWidgets/qgraphicsview.h \
  /opt/homebrew/include/QtGui/qpainter.h \
  /opt/homebrew/include/QtGui/qtextoption.h \
  /opt/homebrew/include/QtWidgets/qscrollarea.h \
  /opt/homebrew/include/QtWidgets/qabstractscrollarea.h \
  /opt/homebrew/include/QtWidgets/qframe.h \
  /opt/homebrew/include/QtWidgets/qwidget.h \
  /opt/homebrew/include/QtWidgets/qgraphicsscene.h \
  /opt/homebrew/lib/QtCharts.framework/Headers/QLineSeries \
  /opt/homebrew/lib/QtCharts.framework/Headers/qlineseries.h \
  /opt/homebrew/include/QtCharts/QXYSeries \
  /opt/homebrew/include/QtCharts/qxyseries.h \
  /opt/homebrew/include/QtGui/QImage \
  /opt/homebrew/lib/QtCharts.framework/Headers/QScatterSeries \
  /opt/homebrew/lib/QtCharts.framework/Headers/qscatterseries.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/GAJ3MXGHID/../../src/terminal/components/phase_selector.h \
  /opt/homebrew/lib/QtWidgets.framework/Headers/QComboBox \
  /opt/homebrew/lib/QtWidgets.framework/Headers/qcombobox.h \
  /opt/homebrew/include/QtWidgets/qabstractitemdelegate.h \
  /opt/homebrew/include/QtWidgets/qstyleoption.h \
  /opt/homebrew/include/QtWidgets/qabstractspinbox.h \
  /opt/homebrew/include/QtGui/qvalidator.h \
  /opt/homebrew/include/QtCore/qregularexpression.h \
  /opt/homebrew/include/QtWidgets/qslider.h \
  /opt/homebrew/include/QtWidgets/qabstractslider.h \
  /opt/homebrew/include/QtWidgets/qstyle.h \
  /opt/homebrew/include/QtWidgets/qtabbar.h \
  /opt/homebrew/include/QtWidgets/qtabwidget.h \
  /opt/homebrew/include/QtWidgets/qrubberband.h \
  /opt/homebrew/include/QtCore/qabstractitemmodel.h \
  /opt/homebrew/lib/QtWidgets.framework/Headers/QSpinBox \
  /opt/homebrew/lib/QtWidgets.framework/Headers/qspinbox.h \
  /opt/homebrew/lib/QtWidgets.framework/Headers/QDoubleSpinBox \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/GAJ3MXGHID/moc_chart_panel.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/GAJ3MXGHID/../../src/terminal/components/chart_panel.h \
  /opt/homebrew/include/QtCharts/QChartView \
  /opt/homebrew/include/QtCharts/QCandlestickSeries \
  /opt/homebrew/include/QtCharts/qcandlestickseries.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/GAJ3MXGHID/../../src/terminal/components/../../models/candle_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/GAJ3MXGHID/moc_documentation_display.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/GAJ3MXGHID/../../src/terminal/components/documentation_display.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/GAJ3MXGHID/../../src/terminal/components/../../documentation/user_documentation.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/GAJ3MXGHID/../../src/terminal/components/../../documentation/documentation_config.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/GAJ3MXGHID/../../src/terminal/components/../../documentation/../utils/logger.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/GAJ3MXGHID/../../src/terminal/components/../../documentation/version_control.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/GAJ3MXGHID/../../src/terminal/components/../../documentation/template_engine.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/GAJ3MXGHID/../../src/terminal/components/../../documentation/ci_integration.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/GAJ3MXGHID/../../src/terminal/components/../../documentation/documentation_types.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/GAJ3MXGHID/../../src/terminal/components/../../documentation/screenshot_manager.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/GAJ3MXGHID/../../src/terminal/components/../../documentation/video_recorder.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/GAJ3MXGHID/../../src/terminal/components/../../documentation/content_validator.h \
  /opt/homebrew/Cellar/llvm/20.1.4_1/bin/../include/c++/v1/regex \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/GAJ3MXGHID/moc_help_system.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/GAJ3MXGHID/../../src/terminal/components/help_system.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/GAJ3MXGHID/moc_order_panel.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/GAJ3MXGHID/../../src/terminal/components/order_panel.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/GAJ3MXGHID/moc_phase_selector.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/GAJ3MXGHID/moc_trade_controls.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/GAJ3MXGHID/../../src/terminal/components/trade_controls.h \
  /opt/homebrew/lib/QtWidgets.framework/Headers/QCheckBox \
  /opt/homebrew/lib/QtWidgets.framework/Headers/qcheckbox.h \
  /opt/homebrew/include/QtWidgets/qabstractbutton.h \
  /opt/homebrew/lib/QtWidgets.framework/Headers/QPushButton \
  /opt/homebrew/lib/QtWidgets.framework/Headers/qpushbutton.h \
  /opt/homebrew/lib/QtWidgets.framework/Headers/QLabel \
  /opt/homebrew/lib/QtWidgets.framework/Headers/qlabel.h \
  /opt/homebrew/include/QtGui/qpicture.h \
  /opt/homebrew/include/QtGui/qtextdocument.h \
  /opt/homebrew/lib/QtWidgets.framework/Headers/QVBoxLayout \
  /opt/homebrew/lib/QtWidgets.framework/Headers/qboxlayout.h \
  /opt/homebrew/include/QtWidgets/qlayout.h \
  /opt/homebrew/include/QtWidgets/qlayoutitem.h \
  /opt/homebrew/include/QtWidgets/qboxlayout.h \
  /opt/homebrew/include/QtWidgets/qgridlayout.h \
  /opt/homebrew/lib/QtWidgets.framework/Headers/QHBoxLayout \
  /opt/homebrew/lib/QtWidgets.framework/Headers/QGridLayout \
  /opt/homebrew/lib/QtWidgets.framework/Headers/qgridlayout.h \
  /opt/homebrew/lib/QtWidgets.framework/Headers/QGroupBox \
  /opt/homebrew/lib/QtWidgets.framework/Headers/qgroupbox.h \
  /opt/homebrew/include/QtGui/qtextcursor.h \
  /opt/homebrew/include/QtGui/qtextformat.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/GAJ3MXGHID/moc_trading_interface.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/GAJ3MXGHID/../../src/terminal/components/trading_interface.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/GAJ3MXGHID/../../src/terminal/components/../../models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/GAJ3MXGHID/../../src/terminal/components/../../models/trade_result.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/XK67ICHAIF/moc_glassmorphism_dock_widget.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/XK67ICHAIF/../../src/terminal/glassmorphism_dock_widget.h \
  /opt/homebrew/lib/QtWidgets.framework/Headers/QDockWidget \
  /opt/homebrew/lib/QtWidgets.framework/Headers/qdockwidget.h \
  /opt/homebrew/lib/QtGui.framework/Headers/QPainter \
  /opt/homebrew/lib/QtGui.framework/Headers/qpainter.h \
  /opt/homebrew/lib/QtGui.framework/Headers/QPainterPath \
  /opt/homebrew/lib/QtGui.framework/Headers/qpainterpath.h \
  /opt/homebrew/lib/QtWidgets.framework/Headers/QStyleOption \
  /opt/homebrew/lib/QtWidgets.framework/Headers/qstyleoption.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/XK67ICHAIF/moc_glassmorphism_window.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/XK67ICHAIF/../../src/terminal/glassmorphism_window.h \
  /opt/homebrew/lib/QtWidgets.framework/Headers/QMainWindow \
  /opt/homebrew/lib/QtWidgets.framework/Headers/qmainwindow.h \
  /opt/homebrew/lib/QtWidgets.framework/Headers/QGraphicsBlurEffect \
  /opt/homebrew/lib/QtWidgets.framework/Headers/qgraphicseffect.h \
  /opt/homebrew/lib/QtWidgets.framework/Headers/QGraphicsDropShadowEffect \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/XK67ICHAIF/moc_main_window.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/XK67ICHAIF/../../src/terminal/main_window.h \
  /opt/homebrew/lib/QtWidgets.framework/Headers/QSplitter \
  /opt/homebrew/lib/QtWidgets.framework/Headers/qsplitter.h \
  /opt/homebrew/lib/QtWidgets.framework/Headers/QToolBar \
  /opt/homebrew/lib/QtWidgets.framework/Headers/qtoolbar.h \
  /opt/homebrew/lib/QtGui.framework/Headers/QAction \
  /opt/homebrew/lib/QtGui.framework/Headers/qaction.h \
  /opt/homebrew/lib/QtGui.framework/Headers/QIcon \
  /opt/homebrew/lib/QtGui.framework/Headers/qicon.h \
  /opt/homebrew/lib/QtWidgets.framework/Headers/QDialog \
  /opt/homebrew/lib/QtWidgets.framework/Headers/qdialog.h \
  /opt/homebrew/lib/QtWidgets.framework/Headers/QFormLayout \
  /opt/homebrew/lib/QtWidgets.framework/Headers/qformlayout.h \
  /opt/homebrew/include/QtWidgets/QLayout \
  /opt/homebrew/lib/QtWidgets.framework/Headers/QDialogButtonBox \
  /opt/homebrew/lib/QtWidgets.framework/Headers/qdialogbuttonbox.h \
  /opt/homebrew/lib/QtCore.framework/Headers/QTime \
  /opt/homebrew/lib/QtCore.framework/Headers/QRandomGenerator \
  /opt/homebrew/lib/QtCore.framework/Headers/qrandom.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/XK67ICHAIF/../../src/terminal/../models/trade_result.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/XK67ICHAIF/../../src/terminal/../models/performance_metrics.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/XK67ICHAIF/../../src/terminal/../validation/performance_analyzer.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/XK67ICHAIF/../../src/terminal/../validation/historical_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/XK67ICHAIF/../../src/terminal/../validation/backtest_result.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/XK67ICHAIF/../../src/terminal/../validation/../trading/trade_types.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/XK67ICHAIF/../../src/terminal/../validation/../models/trade.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/XK67ICHAIF/../../src/terminal/../testing/strategy_tester.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/XK67ICHAIF/../../src/terminal/../testing/test_metrics.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/XK67ICHAIF/../../src/terminal/../testing/test_config.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/XK67ICHAIF/../../src/terminal/../testing/../market/market.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/XK67ICHAIF/../../src/terminal/../testing/../trading/trade_types.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/XK67ICHAIF/../../src/terminal/../testing/../models/trade_result.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/XK67ICHAIF/../../src/terminal/../testing/test_results.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/XK67ICHAIF/../../src/terminal/../utils/style_manager.h \
  /opt/homebrew/lib/QtGui.framework/Headers/QColor \
  /opt/homebrew/lib/QtGui.framework/Headers/qcolor.h \
  /opt/homebrew/lib/QtGui.framework/Headers/QFont \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/XK67ICHAIF/../../src/terminal/../utils/glassmorphism_style_manager.h \
  /opt/homebrew/lib/QtWidgets.framework/Headers/QApplication \
  /opt/homebrew/lib/QtWidgets.framework/Headers/qapplication.h \
  /opt/homebrew/lib/QtGui.framework/Headers/QPalette \
  /opt/homebrew/lib/QtGui.framework/Headers/qpalette.h \
  /opt/homebrew/lib/QtWidgets.framework/Headers/QStyleFactory \
  /opt/homebrew/lib/QtWidgets.framework/Headers/qstylefactory.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/XK67ICHAIF/../../src/terminal/components/ai_visualization.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/XK67ICHAIF/../../src/terminal/../binaryoptionsbot/core/binary_options_bot.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/XK67ICHAIF/moc_qml_bridge.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/XK67ICHAIF/../../src/terminal/qml_bridge.h \
  /opt/homebrew/lib/QtCore.framework/Headers/QVariant \
  /opt/homebrew/lib/QtQml.framework/Headers/QQmlEngine \
  /opt/homebrew/lib/QtQml.framework/Headers/qqmlengine.h \
  /opt/homebrew/include/QtQml/qjsengine.h \
  /opt/homebrew/include/QtCore/qtimezone.h \
  /opt/homebrew/include/QtQml/qjsvalue.h \
  /opt/homebrew/include/QtQml/qtqmlglobal.h \
  /opt/homebrew/include/QtQml/qtqml-config.h \
  /opt/homebrew/include/QtQml/qtqmlexports.h \
  /opt/homebrew/include/QtQml/qjsmanagedvalue.h \
  /opt/homebrew/include/QtQml/qjsprimitivevalue.h \
  /opt/homebrew/include/QtQml/qjsnumbercoercion.h \
  /opt/homebrew/include/QtQml/qqmldebug.h \
  /opt/homebrew/include/QtQml/qqml.h \
  /opt/homebrew/include/QtQml/qqmlprivate.h \
  /opt/homebrew/include/QtQml/qqmllist.h \
  /opt/homebrew/include/QtQml/qqmlparserstatus.h \
  /opt/homebrew/include/QtQml/qqmlpropertyvaluesource.h \
  /opt/homebrew/include/QtCore/qpointer.h \
  /opt/homebrew/include/QtCore/qversionnumber.h \
  /opt/homebrew/include/QtCore/qtyperevision.h \
  /opt/homebrew/include/QtQml/qqmlregistration.h \
  /opt/homebrew/include/QtQmlIntegration/qqmlintegration.h \
  /opt/homebrew/include/QtQml/qqmlerror.h \
  /opt/homebrew/include/QtQml/qqmlabstracturlinterceptor.h \
  /opt/homebrew/lib/QtQml.framework/Headers/QJSEngine \
  /opt/homebrew/lib/QtQml.framework/Headers/qjsengine.h \
  /opt/homebrew/lib/QtCore.framework/Headers/QDebug \
  /opt/homebrew/lib/QtCore.framework/Headers/qdebug.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/XK67ICHAIF/../../src/terminal/../ai/patterns/pattern.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/7QBRZEVWJ2/moc_analysis_view.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/7QBRZEVWJ2/../../src/terminal/widgets/analysis_view.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/models/technical_indicators.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/7QBRZEVWJ2/moc_performance_view.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/7QBRZEVWJ2/../../src/terminal/widgets/performance_view.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/7QBRZEVWJ2/moc_settings_view.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/7QBRZEVWJ2/../../src/terminal/widgets/settings_view.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/7QBRZEVWJ2/moc_trading_view.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/7QBRZEVWJ2/../../src/terminal/widgets/trading_view.h \
  /opt/homebrew/include/QtWidgets/QWidget \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/moc_trading_core.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/../../src/trading/trading_core.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/../../src/trading/../models/qobject_compat.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/../../src/trading/../binaryoptionsbot/core/binary_options_bot.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/../../src/trading/market_data_handler.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/../../src/trading/../market/market_context.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/../../src/trading/../market/market_metrics.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/../../src/trading/../binaryoptionsbot/orders/order_manager.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/../../src/trading/../binaryoptionsbot/orders/../../models/qdatetime_compat.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/../../src/trading/../binaryoptionsbot/orders/../../models/trading_config.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/../../src/trading/../binaryoptionsbot/orders/../../models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/../../src/trading/../binaryoptionsbot/orders/order_types.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/../../src/trading/../binaryoptionsbot/orders/order_execution.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/../../src/trading/../binaryoptionsbot/orders/../../models/trade_types.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/../../src/trading/../binaryoptionsbot/orders/../../models/trade.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/../../src/trading/../binaryoptionsbot/orders/../../trading/trading_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/../../src/trading/../binaryoptionsbot/orders/../risk/risk_manager.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/../../src/trading/../binaryoptionsbot/orders/../trading/otc_trading.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/../../src/trading/../binaryoptionsbot/orders/../trading/../../models/trading_config.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/../../src/trading/../binaryoptionsbot/orders/../trading/../../models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/../../src/trading/../binaryoptionsbot/orders/../trading/../../models/trade_types.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/../../src/trading/../binaryoptionsbot/orders/../trading/../core/binary_options_bot.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/../../src/trading/../binaryoptionsbot/orders/../trading/../risk/risk_manager.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/../../src/trading/simple_strategy.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/../../src/trading/trade_types.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/../../src/trading/../models/market_data.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/../../src/trading/../models/trade_result.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/../../src/trading/../common/drawing_types.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/../../src/trading/../common/drawing.h \
  /opt/homebrew/lib/QtCore.framework/Headers/QPointF \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/../../src/trading/../analysis/test_config.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/../../src/trading/../ai/ml/feature_engineering.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/../../src/trading/../ai/ml/feature_extractor.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/../../src/trading/../ai/ml/model_evaluation.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/../../src/trading/../ai/patterns/pattern_analyzer.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/../../src/trading/../ai/realtime/pattern_optimizer.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/../../src/trading/../ai/patterns/pattern.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/../../src/trading/auto_pilot.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/moc_trading_interface.cpp \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/../../src/trading/trading_interface.h \
  /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/BinaryOptionsTrader_autogen/3UPS5SVQAW/../../src/trading/../models/qdatetime_compat.h

# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/BinaryOptionsTrader.dir/all
all: pybind11/all
all: src/stubs/all
all: src/api/all
all: tests/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/BinaryOptionsTrader.dir/codegen
codegen: pybind11/codegen
codegen: src/stubs/codegen
codegen: src/api/codegen
codegen: tests/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall: pybind11/preinstall
preinstall: src/stubs/preinstall
preinstall: src/api/preinstall
preinstall: tests/preinstall
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/BinaryOptionsTrader.dir/clean
clean: CMakeFiles/BinaryOptionsTrader_qmlimportscan.dir/clean
clean: CMakeFiles/BinaryOptionsTrader_autogen_timestamp_deps.dir/clean
clean: CMakeFiles/BinaryOptionsTrader_autogen.dir/clean
clean: pybind11/clean
clean: src/stubs/clean
clean: src/api/clean
clean: tests/clean
.PHONY : clean

#=============================================================================
# Directory level rules for directory pybind11

# Recursive "all" directory target.
pybind11/all:
.PHONY : pybind11/all

# Recursive "codegen" directory target.
pybind11/codegen:
.PHONY : pybind11/codegen

# Recursive "preinstall" directory target.
pybind11/preinstall:
.PHONY : pybind11/preinstall

# Recursive "clean" directory target.
pybind11/clean:
.PHONY : pybind11/clean

#=============================================================================
# Directory level rules for directory src/api

# Recursive "all" directory target.
src/api/all: src/api/CMakeFiles/trading_api.dir/all
.PHONY : src/api/all

# Recursive "codegen" directory target.
src/api/codegen: src/api/CMakeFiles/trading_api.dir/codegen
.PHONY : src/api/codegen

# Recursive "preinstall" directory target.
src/api/preinstall:
.PHONY : src/api/preinstall

# Recursive "clean" directory target.
src/api/clean: src/api/CMakeFiles/trading_api.dir/clean
src/api/clean: src/api/CMakeFiles/trading_api_autogen_timestamp_deps.dir/clean
src/api/clean: src/api/CMakeFiles/trading_api_autogen.dir/clean
.PHONY : src/api/clean

#=============================================================================
# Directory level rules for directory src/stubs

# Recursive "all" directory target.
src/stubs/all: src/stubs/CMakeFiles/test_stubs.dir/all
.PHONY : src/stubs/all

# Recursive "codegen" directory target.
src/stubs/codegen: src/stubs/CMakeFiles/test_stubs.dir/codegen
.PHONY : src/stubs/codegen

# Recursive "preinstall" directory target.
src/stubs/preinstall:
.PHONY : src/stubs/preinstall

# Recursive "clean" directory target.
src/stubs/clean: src/stubs/CMakeFiles/test_stubs.dir/clean
src/stubs/clean: src/stubs/CMakeFiles/test_stubs_autogen_timestamp_deps.dir/clean
src/stubs/clean: src/stubs/CMakeFiles/test_stubs_autogen.dir/clean
.PHONY : src/stubs/clean

#=============================================================================
# Directory level rules for directory tests

# Recursive "all" directory target.
tests/all: tests/CMakeFiles/pyquotex_rest_api_test.dir/all
tests/all: tests/CMakeFiles/bot_rest_api_connection_test.dir/all
.PHONY : tests/all

# Recursive "codegen" directory target.
tests/codegen: tests/CMakeFiles/pyquotex_rest_api_test.dir/codegen
tests/codegen: tests/CMakeFiles/bot_rest_api_connection_test.dir/codegen
.PHONY : tests/codegen

# Recursive "preinstall" directory target.
tests/preinstall:
.PHONY : tests/preinstall

# Recursive "clean" directory target.
tests/clean: tests/CMakeFiles/pyquotex_rest_api_test.dir/clean
tests/clean: tests/CMakeFiles/bot_rest_api_connection_test.dir/clean
tests/clean: tests/CMakeFiles/pyquotex_rest_api_test_autogen_timestamp_deps.dir/clean
tests/clean: tests/CMakeFiles/pyquotex_rest_api_test_autogen.dir/clean
tests/clean: tests/CMakeFiles/bot_rest_api_connection_test_autogen_timestamp_deps.dir/clean
tests/clean: tests/CMakeFiles/bot_rest_api_connection_test_autogen.dir/clean
.PHONY : tests/clean

#=============================================================================
# Target rules for target CMakeFiles/BinaryOptionsTrader.dir

# All Build rule for target.
CMakeFiles/BinaryOptionsTrader.dir/all: CMakeFiles/BinaryOptionsTrader_qmlimportscan.dir/all
CMakeFiles/BinaryOptionsTrader.dir/all: CMakeFiles/BinaryOptionsTrader_autogen.dir/all
CMakeFiles/BinaryOptionsTrader.dir/all: CMakeFiles/BinaryOptionsTrader_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BinaryOptionsTrader.dir/build.make CMakeFiles/BinaryOptionsTrader.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BinaryOptionsTrader.dir/build.make CMakeFiles/BinaryOptionsTrader.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92 "Built target BinaryOptionsTrader"
.PHONY : CMakeFiles/BinaryOptionsTrader.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/BinaryOptionsTrader.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles 92
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/BinaryOptionsTrader.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles 0
.PHONY : CMakeFiles/BinaryOptionsTrader.dir/rule

# Convenience name for target.
BinaryOptionsTrader: CMakeFiles/BinaryOptionsTrader.dir/rule
.PHONY : BinaryOptionsTrader

# codegen rule for target.
CMakeFiles/BinaryOptionsTrader.dir/codegen: CMakeFiles/BinaryOptionsTrader_qmlimportscan.dir/all
CMakeFiles/BinaryOptionsTrader.dir/codegen: CMakeFiles/BinaryOptionsTrader_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BinaryOptionsTrader.dir/build.make CMakeFiles/BinaryOptionsTrader.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92 "Finished codegen for target BinaryOptionsTrader"
.PHONY : CMakeFiles/BinaryOptionsTrader.dir/codegen

# clean rule for target.
CMakeFiles/BinaryOptionsTrader.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BinaryOptionsTrader.dir/build.make CMakeFiles/BinaryOptionsTrader.dir/clean
.PHONY : CMakeFiles/BinaryOptionsTrader.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/BinaryOptionsTrader_qmlimportscan.dir

# All Build rule for target.
CMakeFiles/BinaryOptionsTrader_qmlimportscan.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BinaryOptionsTrader_qmlimportscan.dir/build.make CMakeFiles/BinaryOptionsTrader_qmlimportscan.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BinaryOptionsTrader_qmlimportscan.dir/build.make CMakeFiles/BinaryOptionsTrader_qmlimportscan.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles --progress-num= "Built target BinaryOptionsTrader_qmlimportscan"
.PHONY : CMakeFiles/BinaryOptionsTrader_qmlimportscan.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/BinaryOptionsTrader_qmlimportscan.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/BinaryOptionsTrader_qmlimportscan.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles 0
.PHONY : CMakeFiles/BinaryOptionsTrader_qmlimportscan.dir/rule

# Convenience name for target.
BinaryOptionsTrader_qmlimportscan: CMakeFiles/BinaryOptionsTrader_qmlimportscan.dir/rule
.PHONY : BinaryOptionsTrader_qmlimportscan

# codegen rule for target.
CMakeFiles/BinaryOptionsTrader_qmlimportscan.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BinaryOptionsTrader_qmlimportscan.dir/build.make CMakeFiles/BinaryOptionsTrader_qmlimportscan.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles --progress-num= "Finished codegen for target BinaryOptionsTrader_qmlimportscan"
.PHONY : CMakeFiles/BinaryOptionsTrader_qmlimportscan.dir/codegen

# clean rule for target.
CMakeFiles/BinaryOptionsTrader_qmlimportscan.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BinaryOptionsTrader_qmlimportscan.dir/build.make CMakeFiles/BinaryOptionsTrader_qmlimportscan.dir/clean
.PHONY : CMakeFiles/BinaryOptionsTrader_qmlimportscan.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/BinaryOptionsTrader_autogen_timestamp_deps.dir

# All Build rule for target.
CMakeFiles/BinaryOptionsTrader_autogen_timestamp_deps.dir/all: CMakeFiles/BinaryOptionsTrader_qmlimportscan.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BinaryOptionsTrader_autogen_timestamp_deps.dir/build.make CMakeFiles/BinaryOptionsTrader_autogen_timestamp_deps.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BinaryOptionsTrader_autogen_timestamp_deps.dir/build.make CMakeFiles/BinaryOptionsTrader_autogen_timestamp_deps.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles --progress-num= "Built target BinaryOptionsTrader_autogen_timestamp_deps"
.PHONY : CMakeFiles/BinaryOptionsTrader_autogen_timestamp_deps.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/BinaryOptionsTrader_autogen_timestamp_deps.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/BinaryOptionsTrader_autogen_timestamp_deps.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles 0
.PHONY : CMakeFiles/BinaryOptionsTrader_autogen_timestamp_deps.dir/rule

# Convenience name for target.
BinaryOptionsTrader_autogen_timestamp_deps: CMakeFiles/BinaryOptionsTrader_autogen_timestamp_deps.dir/rule
.PHONY : BinaryOptionsTrader_autogen_timestamp_deps

# codegen rule for target.
CMakeFiles/BinaryOptionsTrader_autogen_timestamp_deps.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BinaryOptionsTrader_autogen_timestamp_deps.dir/build.make CMakeFiles/BinaryOptionsTrader_autogen_timestamp_deps.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles --progress-num= "Finished codegen for target BinaryOptionsTrader_autogen_timestamp_deps"
.PHONY : CMakeFiles/BinaryOptionsTrader_autogen_timestamp_deps.dir/codegen

# clean rule for target.
CMakeFiles/BinaryOptionsTrader_autogen_timestamp_deps.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BinaryOptionsTrader_autogen_timestamp_deps.dir/build.make CMakeFiles/BinaryOptionsTrader_autogen_timestamp_deps.dir/clean
.PHONY : CMakeFiles/BinaryOptionsTrader_autogen_timestamp_deps.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/BinaryOptionsTrader_autogen.dir

# All Build rule for target.
CMakeFiles/BinaryOptionsTrader_autogen.dir/all: CMakeFiles/BinaryOptionsTrader_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BinaryOptionsTrader_autogen.dir/build.make CMakeFiles/BinaryOptionsTrader_autogen.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BinaryOptionsTrader_autogen.dir/build.make CMakeFiles/BinaryOptionsTrader_autogen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles --progress-num= "Built target BinaryOptionsTrader_autogen"
.PHONY : CMakeFiles/BinaryOptionsTrader_autogen.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/BinaryOptionsTrader_autogen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/BinaryOptionsTrader_autogen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles 0
.PHONY : CMakeFiles/BinaryOptionsTrader_autogen.dir/rule

# Convenience name for target.
BinaryOptionsTrader_autogen: CMakeFiles/BinaryOptionsTrader_autogen.dir/rule
.PHONY : BinaryOptionsTrader_autogen

# codegen rule for target.
CMakeFiles/BinaryOptionsTrader_autogen.dir/codegen: CMakeFiles/BinaryOptionsTrader_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BinaryOptionsTrader_autogen.dir/build.make CMakeFiles/BinaryOptionsTrader_autogen.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles --progress-num= "Finished codegen for target BinaryOptionsTrader_autogen"
.PHONY : CMakeFiles/BinaryOptionsTrader_autogen.dir/codegen

# clean rule for target.
CMakeFiles/BinaryOptionsTrader_autogen.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BinaryOptionsTrader_autogen.dir/build.make CMakeFiles/BinaryOptionsTrader_autogen.dir/clean
.PHONY : CMakeFiles/BinaryOptionsTrader_autogen.dir/clean

#=============================================================================
# Target rules for target src/stubs/CMakeFiles/test_stubs.dir

# All Build rule for target.
src/stubs/CMakeFiles/test_stubs.dir/all: src/stubs/CMakeFiles/test_stubs_autogen_timestamp_deps.dir/all
src/stubs/CMakeFiles/test_stubs.dir/all: src/stubs/CMakeFiles/test_stubs_autogen.dir/all
	$(MAKE) $(MAKESILENT) -f src/stubs/CMakeFiles/test_stubs.dir/build.make src/stubs/CMakeFiles/test_stubs.dir/depend
	$(MAKE) $(MAKESILENT) -f src/stubs/CMakeFiles/test_stubs.dir/build.make src/stubs/CMakeFiles/test_stubs.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles --progress-num=96 "Built target test_stubs"
.PHONY : src/stubs/CMakeFiles/test_stubs.dir/all

# Build rule for subdir invocation for target.
src/stubs/CMakeFiles/test_stubs.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/stubs/CMakeFiles/test_stubs.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles 0
.PHONY : src/stubs/CMakeFiles/test_stubs.dir/rule

# Convenience name for target.
test_stubs: src/stubs/CMakeFiles/test_stubs.dir/rule
.PHONY : test_stubs

# codegen rule for target.
src/stubs/CMakeFiles/test_stubs.dir/codegen: src/stubs/CMakeFiles/test_stubs_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f src/stubs/CMakeFiles/test_stubs.dir/build.make src/stubs/CMakeFiles/test_stubs.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles --progress-num=96 "Finished codegen for target test_stubs"
.PHONY : src/stubs/CMakeFiles/test_stubs.dir/codegen

# clean rule for target.
src/stubs/CMakeFiles/test_stubs.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/stubs/CMakeFiles/test_stubs.dir/build.make src/stubs/CMakeFiles/test_stubs.dir/clean
.PHONY : src/stubs/CMakeFiles/test_stubs.dir/clean

#=============================================================================
# Target rules for target src/stubs/CMakeFiles/test_stubs_autogen_timestamp_deps.dir

# All Build rule for target.
src/stubs/CMakeFiles/test_stubs_autogen_timestamp_deps.dir/all:
	$(MAKE) $(MAKESILENT) -f src/stubs/CMakeFiles/test_stubs_autogen_timestamp_deps.dir/build.make src/stubs/CMakeFiles/test_stubs_autogen_timestamp_deps.dir/depend
	$(MAKE) $(MAKESILENT) -f src/stubs/CMakeFiles/test_stubs_autogen_timestamp_deps.dir/build.make src/stubs/CMakeFiles/test_stubs_autogen_timestamp_deps.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles --progress-num= "Built target test_stubs_autogen_timestamp_deps"
.PHONY : src/stubs/CMakeFiles/test_stubs_autogen_timestamp_deps.dir/all

# Build rule for subdir invocation for target.
src/stubs/CMakeFiles/test_stubs_autogen_timestamp_deps.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/stubs/CMakeFiles/test_stubs_autogen_timestamp_deps.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles 0
.PHONY : src/stubs/CMakeFiles/test_stubs_autogen_timestamp_deps.dir/rule

# Convenience name for target.
test_stubs_autogen_timestamp_deps: src/stubs/CMakeFiles/test_stubs_autogen_timestamp_deps.dir/rule
.PHONY : test_stubs_autogen_timestamp_deps

# codegen rule for target.
src/stubs/CMakeFiles/test_stubs_autogen_timestamp_deps.dir/codegen:
	$(MAKE) $(MAKESILENT) -f src/stubs/CMakeFiles/test_stubs_autogen_timestamp_deps.dir/build.make src/stubs/CMakeFiles/test_stubs_autogen_timestamp_deps.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles --progress-num= "Finished codegen for target test_stubs_autogen_timestamp_deps"
.PHONY : src/stubs/CMakeFiles/test_stubs_autogen_timestamp_deps.dir/codegen

# clean rule for target.
src/stubs/CMakeFiles/test_stubs_autogen_timestamp_deps.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/stubs/CMakeFiles/test_stubs_autogen_timestamp_deps.dir/build.make src/stubs/CMakeFiles/test_stubs_autogen_timestamp_deps.dir/clean
.PHONY : src/stubs/CMakeFiles/test_stubs_autogen_timestamp_deps.dir/clean

#=============================================================================
# Target rules for target src/stubs/CMakeFiles/test_stubs_autogen.dir

# All Build rule for target.
src/stubs/CMakeFiles/test_stubs_autogen.dir/all: src/stubs/CMakeFiles/test_stubs_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f src/stubs/CMakeFiles/test_stubs_autogen.dir/build.make src/stubs/CMakeFiles/test_stubs_autogen.dir/depend
	$(MAKE) $(MAKESILENT) -f src/stubs/CMakeFiles/test_stubs_autogen.dir/build.make src/stubs/CMakeFiles/test_stubs_autogen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles --progress-num= "Built target test_stubs_autogen"
.PHONY : src/stubs/CMakeFiles/test_stubs_autogen.dir/all

# Build rule for subdir invocation for target.
src/stubs/CMakeFiles/test_stubs_autogen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/stubs/CMakeFiles/test_stubs_autogen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles 0
.PHONY : src/stubs/CMakeFiles/test_stubs_autogen.dir/rule

# Convenience name for target.
test_stubs_autogen: src/stubs/CMakeFiles/test_stubs_autogen.dir/rule
.PHONY : test_stubs_autogen

# codegen rule for target.
src/stubs/CMakeFiles/test_stubs_autogen.dir/codegen: src/stubs/CMakeFiles/test_stubs_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f src/stubs/CMakeFiles/test_stubs_autogen.dir/build.make src/stubs/CMakeFiles/test_stubs_autogen.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles --progress-num= "Finished codegen for target test_stubs_autogen"
.PHONY : src/stubs/CMakeFiles/test_stubs_autogen.dir/codegen

# clean rule for target.
src/stubs/CMakeFiles/test_stubs_autogen.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/stubs/CMakeFiles/test_stubs_autogen.dir/build.make src/stubs/CMakeFiles/test_stubs_autogen.dir/clean
.PHONY : src/stubs/CMakeFiles/test_stubs_autogen.dir/clean

#=============================================================================
# Target rules for target src/api/CMakeFiles/trading_api.dir

# All Build rule for target.
src/api/CMakeFiles/trading_api.dir/all: src/api/CMakeFiles/trading_api_autogen.dir/all
src/api/CMakeFiles/trading_api.dir/all: src/api/CMakeFiles/trading_api_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f src/api/CMakeFiles/trading_api.dir/build.make src/api/CMakeFiles/trading_api.dir/depend
	$(MAKE) $(MAKESILENT) -f src/api/CMakeFiles/trading_api.dir/build.make src/api/CMakeFiles/trading_api.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles --progress-num=97,98,99 "Built target trading_api"
.PHONY : src/api/CMakeFiles/trading_api.dir/all

# Build rule for subdir invocation for target.
src/api/CMakeFiles/trading_api.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles 4
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/api/CMakeFiles/trading_api.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles 0
.PHONY : src/api/CMakeFiles/trading_api.dir/rule

# Convenience name for target.
trading_api: src/api/CMakeFiles/trading_api.dir/rule
.PHONY : trading_api

# codegen rule for target.
src/api/CMakeFiles/trading_api.dir/codegen: src/api/CMakeFiles/trading_api_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f src/api/CMakeFiles/trading_api.dir/build.make src/api/CMakeFiles/trading_api.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles --progress-num=97,98,99 "Finished codegen for target trading_api"
.PHONY : src/api/CMakeFiles/trading_api.dir/codegen

# clean rule for target.
src/api/CMakeFiles/trading_api.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/api/CMakeFiles/trading_api.dir/build.make src/api/CMakeFiles/trading_api.dir/clean
.PHONY : src/api/CMakeFiles/trading_api.dir/clean

#=============================================================================
# Target rules for target src/api/CMakeFiles/trading_api_autogen_timestamp_deps.dir

# All Build rule for target.
src/api/CMakeFiles/trading_api_autogen_timestamp_deps.dir/all:
	$(MAKE) $(MAKESILENT) -f src/api/CMakeFiles/trading_api_autogen_timestamp_deps.dir/build.make src/api/CMakeFiles/trading_api_autogen_timestamp_deps.dir/depend
	$(MAKE) $(MAKESILENT) -f src/api/CMakeFiles/trading_api_autogen_timestamp_deps.dir/build.make src/api/CMakeFiles/trading_api_autogen_timestamp_deps.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles --progress-num= "Built target trading_api_autogen_timestamp_deps"
.PHONY : src/api/CMakeFiles/trading_api_autogen_timestamp_deps.dir/all

# Build rule for subdir invocation for target.
src/api/CMakeFiles/trading_api_autogen_timestamp_deps.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/api/CMakeFiles/trading_api_autogen_timestamp_deps.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles 0
.PHONY : src/api/CMakeFiles/trading_api_autogen_timestamp_deps.dir/rule

# Convenience name for target.
trading_api_autogen_timestamp_deps: src/api/CMakeFiles/trading_api_autogen_timestamp_deps.dir/rule
.PHONY : trading_api_autogen_timestamp_deps

# codegen rule for target.
src/api/CMakeFiles/trading_api_autogen_timestamp_deps.dir/codegen:
	$(MAKE) $(MAKESILENT) -f src/api/CMakeFiles/trading_api_autogen_timestamp_deps.dir/build.make src/api/CMakeFiles/trading_api_autogen_timestamp_deps.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles --progress-num= "Finished codegen for target trading_api_autogen_timestamp_deps"
.PHONY : src/api/CMakeFiles/trading_api_autogen_timestamp_deps.dir/codegen

# clean rule for target.
src/api/CMakeFiles/trading_api_autogen_timestamp_deps.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/api/CMakeFiles/trading_api_autogen_timestamp_deps.dir/build.make src/api/CMakeFiles/trading_api_autogen_timestamp_deps.dir/clean
.PHONY : src/api/CMakeFiles/trading_api_autogen_timestamp_deps.dir/clean

#=============================================================================
# Target rules for target src/api/CMakeFiles/trading_api_autogen.dir

# All Build rule for target.
src/api/CMakeFiles/trading_api_autogen.dir/all: src/api/CMakeFiles/trading_api_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f src/api/CMakeFiles/trading_api_autogen.dir/build.make src/api/CMakeFiles/trading_api_autogen.dir/depend
	$(MAKE) $(MAKESILENT) -f src/api/CMakeFiles/trading_api_autogen.dir/build.make src/api/CMakeFiles/trading_api_autogen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles --progress-num=100 "Built target trading_api_autogen"
.PHONY : src/api/CMakeFiles/trading_api_autogen.dir/all

# Build rule for subdir invocation for target.
src/api/CMakeFiles/trading_api_autogen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/api/CMakeFiles/trading_api_autogen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles 0
.PHONY : src/api/CMakeFiles/trading_api_autogen.dir/rule

# Convenience name for target.
trading_api_autogen: src/api/CMakeFiles/trading_api_autogen.dir/rule
.PHONY : trading_api_autogen

# codegen rule for target.
src/api/CMakeFiles/trading_api_autogen.dir/codegen: src/api/CMakeFiles/trading_api_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f src/api/CMakeFiles/trading_api_autogen.dir/build.make src/api/CMakeFiles/trading_api_autogen.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles --progress-num=100 "Finished codegen for target trading_api_autogen"
.PHONY : src/api/CMakeFiles/trading_api_autogen.dir/codegen

# clean rule for target.
src/api/CMakeFiles/trading_api_autogen.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/api/CMakeFiles/trading_api_autogen.dir/build.make src/api/CMakeFiles/trading_api_autogen.dir/clean
.PHONY : src/api/CMakeFiles/trading_api_autogen.dir/clean

#=============================================================================
# Target rules for target tests/CMakeFiles/pyquotex_rest_api_test.dir

# All Build rule for target.
tests/CMakeFiles/pyquotex_rest_api_test.dir/all: tests/CMakeFiles/pyquotex_rest_api_test_autogen_timestamp_deps.dir/all
tests/CMakeFiles/pyquotex_rest_api_test.dir/all: tests/CMakeFiles/pyquotex_rest_api_test_autogen.dir/all
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/pyquotex_rest_api_test.dir/build.make tests/CMakeFiles/pyquotex_rest_api_test.dir/depend
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/pyquotex_rest_api_test.dir/build.make tests/CMakeFiles/pyquotex_rest_api_test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles --progress-num=95 "Built target pyquotex_rest_api_test"
.PHONY : tests/CMakeFiles/pyquotex_rest_api_test.dir/all

# Build rule for subdir invocation for target.
tests/CMakeFiles/pyquotex_rest_api_test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 tests/CMakeFiles/pyquotex_rest_api_test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles 0
.PHONY : tests/CMakeFiles/pyquotex_rest_api_test.dir/rule

# Convenience name for target.
pyquotex_rest_api_test: tests/CMakeFiles/pyquotex_rest_api_test.dir/rule
.PHONY : pyquotex_rest_api_test

# codegen rule for target.
tests/CMakeFiles/pyquotex_rest_api_test.dir/codegen: tests/CMakeFiles/pyquotex_rest_api_test_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/pyquotex_rest_api_test.dir/build.make tests/CMakeFiles/pyquotex_rest_api_test.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles --progress-num=95 "Finished codegen for target pyquotex_rest_api_test"
.PHONY : tests/CMakeFiles/pyquotex_rest_api_test.dir/codegen

# clean rule for target.
tests/CMakeFiles/pyquotex_rest_api_test.dir/clean:
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/pyquotex_rest_api_test.dir/build.make tests/CMakeFiles/pyquotex_rest_api_test.dir/clean
.PHONY : tests/CMakeFiles/pyquotex_rest_api_test.dir/clean

#=============================================================================
# Target rules for target tests/CMakeFiles/bot_rest_api_connection_test.dir

# All Build rule for target.
tests/CMakeFiles/bot_rest_api_connection_test.dir/all: tests/CMakeFiles/bot_rest_api_connection_test_autogen_timestamp_deps.dir/all
tests/CMakeFiles/bot_rest_api_connection_test.dir/all: tests/CMakeFiles/bot_rest_api_connection_test_autogen.dir/all
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/bot_rest_api_connection_test.dir/build.make tests/CMakeFiles/bot_rest_api_connection_test.dir/depend
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/bot_rest_api_connection_test.dir/build.make tests/CMakeFiles/bot_rest_api_connection_test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles --progress-num=93 "Built target bot_rest_api_connection_test"
.PHONY : tests/CMakeFiles/bot_rest_api_connection_test.dir/all

# Build rule for subdir invocation for target.
tests/CMakeFiles/bot_rest_api_connection_test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 tests/CMakeFiles/bot_rest_api_connection_test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles 0
.PHONY : tests/CMakeFiles/bot_rest_api_connection_test.dir/rule

# Convenience name for target.
bot_rest_api_connection_test: tests/CMakeFiles/bot_rest_api_connection_test.dir/rule
.PHONY : bot_rest_api_connection_test

# codegen rule for target.
tests/CMakeFiles/bot_rest_api_connection_test.dir/codegen: tests/CMakeFiles/bot_rest_api_connection_test_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/bot_rest_api_connection_test.dir/build.make tests/CMakeFiles/bot_rest_api_connection_test.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles --progress-num=93 "Finished codegen for target bot_rest_api_connection_test"
.PHONY : tests/CMakeFiles/bot_rest_api_connection_test.dir/codegen

# clean rule for target.
tests/CMakeFiles/bot_rest_api_connection_test.dir/clean:
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/bot_rest_api_connection_test.dir/build.make tests/CMakeFiles/bot_rest_api_connection_test.dir/clean
.PHONY : tests/CMakeFiles/bot_rest_api_connection_test.dir/clean

#=============================================================================
# Target rules for target tests/CMakeFiles/pyquotex_rest_api_test_autogen_timestamp_deps.dir

# All Build rule for target.
tests/CMakeFiles/pyquotex_rest_api_test_autogen_timestamp_deps.dir/all:
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/pyquotex_rest_api_test_autogen_timestamp_deps.dir/build.make tests/CMakeFiles/pyquotex_rest_api_test_autogen_timestamp_deps.dir/depend
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/pyquotex_rest_api_test_autogen_timestamp_deps.dir/build.make tests/CMakeFiles/pyquotex_rest_api_test_autogen_timestamp_deps.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles --progress-num= "Built target pyquotex_rest_api_test_autogen_timestamp_deps"
.PHONY : tests/CMakeFiles/pyquotex_rest_api_test_autogen_timestamp_deps.dir/all

# Build rule for subdir invocation for target.
tests/CMakeFiles/pyquotex_rest_api_test_autogen_timestamp_deps.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 tests/CMakeFiles/pyquotex_rest_api_test_autogen_timestamp_deps.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles 0
.PHONY : tests/CMakeFiles/pyquotex_rest_api_test_autogen_timestamp_deps.dir/rule

# Convenience name for target.
pyquotex_rest_api_test_autogen_timestamp_deps: tests/CMakeFiles/pyquotex_rest_api_test_autogen_timestamp_deps.dir/rule
.PHONY : pyquotex_rest_api_test_autogen_timestamp_deps

# codegen rule for target.
tests/CMakeFiles/pyquotex_rest_api_test_autogen_timestamp_deps.dir/codegen:
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/pyquotex_rest_api_test_autogen_timestamp_deps.dir/build.make tests/CMakeFiles/pyquotex_rest_api_test_autogen_timestamp_deps.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles --progress-num= "Finished codegen for target pyquotex_rest_api_test_autogen_timestamp_deps"
.PHONY : tests/CMakeFiles/pyquotex_rest_api_test_autogen_timestamp_deps.dir/codegen

# clean rule for target.
tests/CMakeFiles/pyquotex_rest_api_test_autogen_timestamp_deps.dir/clean:
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/pyquotex_rest_api_test_autogen_timestamp_deps.dir/build.make tests/CMakeFiles/pyquotex_rest_api_test_autogen_timestamp_deps.dir/clean
.PHONY : tests/CMakeFiles/pyquotex_rest_api_test_autogen_timestamp_deps.dir/clean

#=============================================================================
# Target rules for target tests/CMakeFiles/pyquotex_rest_api_test_autogen.dir

# All Build rule for target.
tests/CMakeFiles/pyquotex_rest_api_test_autogen.dir/all: tests/CMakeFiles/pyquotex_rest_api_test_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/pyquotex_rest_api_test_autogen.dir/build.make tests/CMakeFiles/pyquotex_rest_api_test_autogen.dir/depend
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/pyquotex_rest_api_test_autogen.dir/build.make tests/CMakeFiles/pyquotex_rest_api_test_autogen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles --progress-num= "Built target pyquotex_rest_api_test_autogen"
.PHONY : tests/CMakeFiles/pyquotex_rest_api_test_autogen.dir/all

# Build rule for subdir invocation for target.
tests/CMakeFiles/pyquotex_rest_api_test_autogen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 tests/CMakeFiles/pyquotex_rest_api_test_autogen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles 0
.PHONY : tests/CMakeFiles/pyquotex_rest_api_test_autogen.dir/rule

# Convenience name for target.
pyquotex_rest_api_test_autogen: tests/CMakeFiles/pyquotex_rest_api_test_autogen.dir/rule
.PHONY : pyquotex_rest_api_test_autogen

# codegen rule for target.
tests/CMakeFiles/pyquotex_rest_api_test_autogen.dir/codegen: tests/CMakeFiles/pyquotex_rest_api_test_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/pyquotex_rest_api_test_autogen.dir/build.make tests/CMakeFiles/pyquotex_rest_api_test_autogen.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles --progress-num= "Finished codegen for target pyquotex_rest_api_test_autogen"
.PHONY : tests/CMakeFiles/pyquotex_rest_api_test_autogen.dir/codegen

# clean rule for target.
tests/CMakeFiles/pyquotex_rest_api_test_autogen.dir/clean:
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/pyquotex_rest_api_test_autogen.dir/build.make tests/CMakeFiles/pyquotex_rest_api_test_autogen.dir/clean
.PHONY : tests/CMakeFiles/pyquotex_rest_api_test_autogen.dir/clean

#=============================================================================
# Target rules for target tests/CMakeFiles/bot_rest_api_connection_test_autogen_timestamp_deps.dir

# All Build rule for target.
tests/CMakeFiles/bot_rest_api_connection_test_autogen_timestamp_deps.dir/all:
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/bot_rest_api_connection_test_autogen_timestamp_deps.dir/build.make tests/CMakeFiles/bot_rest_api_connection_test_autogen_timestamp_deps.dir/depend
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/bot_rest_api_connection_test_autogen_timestamp_deps.dir/build.make tests/CMakeFiles/bot_rest_api_connection_test_autogen_timestamp_deps.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles --progress-num= "Built target bot_rest_api_connection_test_autogen_timestamp_deps"
.PHONY : tests/CMakeFiles/bot_rest_api_connection_test_autogen_timestamp_deps.dir/all

# Build rule for subdir invocation for target.
tests/CMakeFiles/bot_rest_api_connection_test_autogen_timestamp_deps.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 tests/CMakeFiles/bot_rest_api_connection_test_autogen_timestamp_deps.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles 0
.PHONY : tests/CMakeFiles/bot_rest_api_connection_test_autogen_timestamp_deps.dir/rule

# Convenience name for target.
bot_rest_api_connection_test_autogen_timestamp_deps: tests/CMakeFiles/bot_rest_api_connection_test_autogen_timestamp_deps.dir/rule
.PHONY : bot_rest_api_connection_test_autogen_timestamp_deps

# codegen rule for target.
tests/CMakeFiles/bot_rest_api_connection_test_autogen_timestamp_deps.dir/codegen:
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/bot_rest_api_connection_test_autogen_timestamp_deps.dir/build.make tests/CMakeFiles/bot_rest_api_connection_test_autogen_timestamp_deps.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles --progress-num= "Finished codegen for target bot_rest_api_connection_test_autogen_timestamp_deps"
.PHONY : tests/CMakeFiles/bot_rest_api_connection_test_autogen_timestamp_deps.dir/codegen

# clean rule for target.
tests/CMakeFiles/bot_rest_api_connection_test_autogen_timestamp_deps.dir/clean:
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/bot_rest_api_connection_test_autogen_timestamp_deps.dir/build.make tests/CMakeFiles/bot_rest_api_connection_test_autogen_timestamp_deps.dir/clean
.PHONY : tests/CMakeFiles/bot_rest_api_connection_test_autogen_timestamp_deps.dir/clean

#=============================================================================
# Target rules for target tests/CMakeFiles/bot_rest_api_connection_test_autogen.dir

# All Build rule for target.
tests/CMakeFiles/bot_rest_api_connection_test_autogen.dir/all: tests/CMakeFiles/bot_rest_api_connection_test_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/bot_rest_api_connection_test_autogen.dir/build.make tests/CMakeFiles/bot_rest_api_connection_test_autogen.dir/depend
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/bot_rest_api_connection_test_autogen.dir/build.make tests/CMakeFiles/bot_rest_api_connection_test_autogen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles --progress-num=94 "Built target bot_rest_api_connection_test_autogen"
.PHONY : tests/CMakeFiles/bot_rest_api_connection_test_autogen.dir/all

# Build rule for subdir invocation for target.
tests/CMakeFiles/bot_rest_api_connection_test_autogen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 tests/CMakeFiles/bot_rest_api_connection_test_autogen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles 0
.PHONY : tests/CMakeFiles/bot_rest_api_connection_test_autogen.dir/rule

# Convenience name for target.
bot_rest_api_connection_test_autogen: tests/CMakeFiles/bot_rest_api_connection_test_autogen.dir/rule
.PHONY : bot_rest_api_connection_test_autogen

# codegen rule for target.
tests/CMakeFiles/bot_rest_api_connection_test_autogen.dir/codegen: tests/CMakeFiles/bot_rest_api_connection_test_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/bot_rest_api_connection_test_autogen.dir/build.make tests/CMakeFiles/bot_rest_api_connection_test_autogen.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/CMakeFiles --progress-num=94 "Finished codegen for target bot_rest_api_connection_test_autogen"
.PHONY : tests/CMakeFiles/bot_rest_api_connection_test_autogen.dir/codegen

# clean rule for target.
tests/CMakeFiles/bot_rest_api_connection_test_autogen.dir/clean:
	$(MAKE) $(MAKESILENT) -f tests/CMakeFiles/bot_rest_api_connection_test_autogen.dir/build.make tests/CMakeFiles/bot_rest_api_connection_test_autogen.dir/clean
.PHONY : tests/CMakeFiles/bot_rest_api_connection_test_autogen.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system


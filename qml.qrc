<!DOCTYPE RCC>
<RCC version="1.0">
    <qresource prefix="/">
        <file>qml/main.qml</file>
        <file>qml/components/TopNavBar.qml</file>
        <file>qml/components/SideBar.qml</file>
        <file>qml/components/MainPanel.qml</file>
        <file>qml/components/PriceChart.qml</file>
        <file>qml/components/Candle.qml</file>
        <file>qml/components/PatternFeedPanel.qml</file>
        <file>qml/components/NewsSignalOverlay.qml</file>
        <file>qml/components/IndicatorsPanel.qml</file>
        <file>qml/components/SentimentPanel.qml</file>
        <file>qml/components/TradeControls.qml</file>
        <file>qml/components/OrderPanel.qml</file>
        <file>qml/components/SettingsPanel.qml</file>
        <file>qml/components/StrategyTestingPanel.qml</file>
        <file>qml/popups/IndicatorPopup.qml</file>
        <file>qml/popups/TradePopup.qml</file>
        <file>qml/popups/SettingsPopup.qml</file>
        <file>qml/styles/AppTheme.qml</file>
        <file>qml/styles/AppStyle.qml</file>
        <file>qml/components/PinCodeDialog.qml</file>
    </qresource>
</RCC>

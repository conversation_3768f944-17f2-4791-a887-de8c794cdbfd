{"api": {"apiEndpoint": "https://api.qxbroker.com/v1", "quotexWsEndpoint": "wss://ws2.qxbroker.com/socket.io/?EIO=3&transport=websocket&sid="}, "connection": {"wsEndpoint": "mongodb://localhost:27017/ricaXbulan", "reconnectAttempts": 3, "reconnectDelay": 5000, "pingInterval": 30000, "connectionTimeout": 10000}, "mongodb": {"uri": "mongodb://localhost:27017", "dbName": "ricaXbulan", "dataPath": "/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/mongo_data"}, "redis": {"host": "127.0.0.1", "port": 6379, "username": "", "password": "", "useSSL": false}, "trading": {"symbols": ["EUR/USD", "GBP/USD", "USD/JPY", "BTC/USD"], "initialBalance": 1000, "defaultTradeAmount": 10, "maxConcurrentTrades": 3, "timeframeSeconds": 60, "maxDailyTrades": 20, "engineType": "BinaryOptionsBot"}, "risk": {"maxRiskPerTrade": 0.02, "baseRiskPerTrade": 0.01, "maxRiskExposure": 0.02, "minTradeSize": 0.001, "maxDailyLoss": 0.05, "maxWeeklyLoss": 0.15, "maxDrawdown": 0.15, "maxAllowedDrawdown": 0.1, "useStopLoss": true}, "analysis": {"highVolatilityThreshold": 0.15, "trendThreshold": 0.6, "minConfidenceThreshold": 0.7, "highStressThreshold": 0.8, "maxVolatility": 0.2, "correlationThreshold": 0.8, "maxExposure": 1000000, "minRequiredVolume": 1000, "minWinProbability": 0.6, "correlationImpact": 0.5}, "marketQuality": {"minLiquidityScore": 0.5, "maxVolatilityScore": 0.8, "maxSpreadThreshold": 0.03, "minMarketDepth": 10000}, "recovery": {"strategyName": "dynamic", "maxRecoveryMultiplier": 3.0, "maxConsecutiveLosses": 5, "recoveryAggressiveness": 1.0}, "news_api": {"marketaux": {"api_key": "ySHUjoNtBnBcOLSAJdzVXX5ISoZdk9jcxg6jgVNn", "enabled": true, "priority": 1}, "finnhub": {"api_key": "d0s2m4hr01qumephb940d0s2m4hr01qumephb94g", "enabled": true, "priority": 2}, "newsdata": {"api_key": "pub_58dfe4710edf41c3be29db6efcd7afe3", "enabled": true, "priority": 3}, "cache_ttl": {"news_seconds": 300, "sentiment_seconds": 600, "economic_calendar_seconds": 3600}, "filtering": {"min_impact": "MEDIUM", "max_age_hours": 24, "enable_sentiment_analysis": true}}}
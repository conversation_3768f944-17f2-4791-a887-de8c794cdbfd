#include "../src/api/news/news_api_client.h"
#include "../src/utils/logger.h"
#include <iostream>
#include <fstream>
#include <nlohmann/json.hpp>

/**
 * @brief Test program for the multi-API news client
 * 
 * This test demonstrates:
 * 1. API rotation and failover
 * 2. Caching mechanisms
 * 3. Sentiment analysis
 * 4. Symbol-specific news fetching
 * 5. Economic calendar integration
 */

void loadConfigAndInitialize(NewsAPIClient& client) {
    try {
        std::ifstream configFile("config/news_api_config.json");
        if (!configFile.is_open()) {
            throw std::runtime_error("Could not open news API config file");
        }
        
        nlohmann::json config;
        configFile >> config;
        
        auto apis = config["news_apis"];
        
        std::string marketauxKey = apis["marketaux"]["api_key"];
        std::string finnhubKey = apis["finnhub"]["api_key"];
        std::string newsdataKey = apis["newsdata"]["api_key"];
        
        client.initialize(marketauxKey, finnhubKey, newsdataKey);
        
        Logger::getInstance().info("News API client initialized with configuration");
        
    } catch (const std::exception& e) {
        Logger::getInstance().error("Error loading config: " + std::string(e.what()));
        throw;
    }
}

void testLatestNews(NewsAPIClient& client) {
    std::cout << "\n=== Testing Latest Financial News ===" << std::endl;
    
    try {
        auto news = client.fetchLatestNews();
        
        std::cout << "Fetched " << news.size() << " news items:" << std::endl;
        
        for (size_t i = 0; i < std::min(news.size(), size_t(5)); ++i) {
            const auto& item = news[i];
            std::cout << "\n" << (i + 1) << ". " << item.title << std::endl;
            std::cout << "   Source: " << item.source << std::endl;
            std::cout << "   Impact: ";
            switch (item.impact) {
                case Models::NewsImpact::HIGH: std::cout << "HIGH"; break;
                case Models::NewsImpact::MEDIUM: std::cout << "MEDIUM"; break;
                case Models::NewsImpact::LOW: std::cout << "LOW"; break;
                default: std::cout << "UNKNOWN"; break;
            }
            std::cout << std::endl;
            
            if (!item.symbol.empty()) {
                std::cout << "   Symbol: " << item.symbol << std::endl;
            }
            
            if (!item.description.empty()) {
                std::string desc = item.description.substr(0, 100);
                if (item.description.length() > 100) desc += "...";
                std::cout << "   Description: " << desc << std::endl;
            }
        }
        
    } catch (const std::exception& e) {
        std::cout << "Error fetching latest news: " << e.what() << std::endl;
    }
}

void testSymbolSpecificNews(NewsAPIClient& client) {
    std::cout << "\n=== Testing Symbol-Specific News ===" << std::endl;
    
    std::vector<std::string> testSymbols = {"AAPL", "TSLA", "EUR/USD", "BTC/USD"};
    
    for (const auto& symbol : testSymbols) {
        try {
            std::cout << "\nFetching news for " << symbol << ":" << std::endl;
            
            auto news = client.fetchNewsBySymbols({symbol});
            std::cout << "Found " << news.size() << " news items for " << symbol << std::endl;
            
            if (!news.empty()) {
                const auto& item = news[0];
                std::cout << "Latest: " << item.title << std::endl;
                std::cout << "Source: " << item.source << std::endl;
            }
            
        } catch (const std::exception& e) {
            std::cout << "Error fetching news for " << symbol << ": " << e.what() << std::endl;
        }
    }
}

void testSentimentAnalysis(NewsAPIClient& client) {
    std::cout << "\n=== Testing Sentiment Analysis ===" << std::endl;
    
    std::vector<std::string> testSymbols = {"AAPL", "TSLA", "EUR/USD"};
    
    for (const auto& symbol : testSymbols) {
        try {
            std::cout << "\nAnalyzing sentiment for " << symbol << ":" << std::endl;
            
            auto sentiment = client.fetchSentimentAnalysis(symbol);
            
            std::cout << "Sentiment Score: " << sentiment.sentimentScore << std::endl;
            std::cout << "Market Impact: " << sentiment.marketImpact << std::endl;
            std::cout << "Volatility Expectation: " << sentiment.volatilityExpectation << std::endl;
            std::cout << "Trading Recommended: " << (sentiment.isTradingRecommended ? "Yes" : "No") << std::endl;
            
            if (!sentiment.keywords.empty()) {
                std::cout << "Keywords: ";
                for (size_t i = 0; i < sentiment.keywords.size(); ++i) {
                    if (i > 0) std::cout << ", ";
                    std::cout << sentiment.keywords[i];
                }
                std::cout << std::endl;
            }
            
        } catch (const std::exception& e) {
            std::cout << "Error analyzing sentiment for " << symbol << ": " << e.what() << std::endl;
        }
    }
}

void testEconomicCalendar(NewsAPIClient& client) {
    std::cout << "\n=== Testing Economic Calendar ===" << std::endl;
    
    try {
        auto events = client.fetchEconomicCalendar();
        
        std::cout << "Fetched " << events.size() << " economic events:" << std::endl;
        
        for (size_t i = 0; i < std::min(events.size(), size_t(3)); ++i) {
            const auto& event = events[i];
            std::cout << "\n" << (i + 1) << ". " << event.title << std::endl;
            std::cout << "   Currency: " << event.currency << std::endl;
            std::cout << "   Forecast: " << event.forecast << std::endl;
            std::cout << "   Previous: " << event.previous << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "Error fetching economic calendar: " << e.what() << std::endl;
    }
}

void testAPIUsageStats(NewsAPIClient& client) {
    std::cout << "\n=== API Usage Statistics ===" << std::endl;
    
    try {
        auto stats = client.getAPIUsageStats();
        
        for (const auto& [provider, creds] : stats) {
            std::string providerName;
            switch (provider) {
                case NewsAPIProvider::MARKETAUX: providerName = "Marketaux"; break;
                case NewsAPIProvider::FINNHUB: providerName = "Finnhub"; break;
                case NewsAPIProvider::NEWSDATA: providerName = "NewsData"; break;
                default: providerName = "Unknown"; break;
            }
            
            std::cout << "\n" << providerName << ":" << std::endl;
            std::cout << "  Active: " << (creds.isActive ? "Yes" : "No") << std::endl;
            std::cout << "  Remaining Calls: " << creds.remainingCalls << std::endl;
            std::cout << "  Error Count: " << creds.errorCount << std::endl;
            std::cout << "  Avg Response Time: " << creds.responseTimeMs << "ms" << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "Error getting API stats: " << e.what() << std::endl;
    }
}

int main() {
    std::cout << "RicaXBulan Multi-API News Client Test" << std::endl;
    std::cout << "=====================================" << std::endl;
    
    try {
        NewsAPIClient client;
        
        // Load configuration and initialize
        loadConfigAndInitialize(client);
        
        // Set timeout
        client.setTimeout(30);
        
        // Run tests
        testLatestNews(client);
        testSymbolSpecificNews(client);
        testSentimentAnalysis(client);
        testEconomicCalendar(client);
        testAPIUsageStats(client);
        
        std::cout << "\n=== Test Completed Successfully ===" << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "Test failed: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}

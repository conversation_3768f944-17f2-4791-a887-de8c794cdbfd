#include "../src/binaryoptionsbot/core/binary_options_bot.h"
#include "../src/ai/trading_brain.h"
#include "../src/services/news_service.h"
#include "../src/api/news/news_api_client.h"
#include "../src/utils/logger.h"
#include <iostream>
#include <chrono>
#include <thread>

/**
 * @brief Test to verify AI and Bot integration with the new multi-API news system
 * 
 * This test verifies:
 * 1. NewsService initializes with multi-API support
 * 2. BinaryOptionsBot receives news updates
 * 3. AI TradingBrain gets news data for decision making
 * 4. News data flows correctly through the entire system
 */

class NewsIntegrationTest {
public:
    void runTest() {
        std::cout << "=== AI + Bot + News API Integration Test ===" << std::endl;
        
        try {
            // Test 1: Initialize BinaryOptionsBot with news integration
            testBotNewsInitialization();
            
            // Test 2: Verify news data flow to AI system
            testAINewsIntegration();
            
            // Test 3: Test multi-API rotation and caching
            testMultiAPIRotation();
            
            // Test 4: Test sentiment analysis integration
            testSentimentAnalysisIntegration();
            
            // Test 5: Test real-time news updates
            testRealTimeNewsUpdates();
            
            std::cout << "\n✅ All integration tests passed!" << std::endl;
            
        } catch (const std::exception& e) {
            std::cout << "\n❌ Integration test failed: " << e.what() << std::endl;
            throw;
        }
    }

private:
    void testBotNewsInitialization() {
        std::cout << "\n1. Testing Bot News Initialization..." << std::endl;
        
        // Create trading config
        Models::TradingConfig config;
        config.baseRiskLevel = 0.02;
        config.minAIConfidence = 0.7;
        config.defaultTradeAmount = 10.0;
        
        // Create bot instance
        auto bot = std::make_unique<BinaryOptionsBot>(config);
        
        // Verify news service is initialized
        auto recentNews = bot->getRecentNews();
        std::cout << "   ✓ Bot initialized with news service" << std::endl;
        std::cout << "   ✓ Initial news count: " << recentNews.size() << std::endl;
        
        // Wait a moment for news to be fetched
        std::this_thread::sleep_for(std::chrono::seconds(2));
        
        // Check if news was fetched
        recentNews = bot->getRecentNews();
        std::cout << "   ✓ News after fetch: " << recentNews.size() << " items" << std::endl;
        
        if (!recentNews.empty()) {
            const auto& firstNews = recentNews[0];
            std::cout << "   ✓ Latest news: " << firstNews.title << std::endl;
            std::cout << "   ✓ News source: " << firstNews.source << std::endl;
            std::cout << "   ✓ News impact: " << static_cast<int>(firstNews.impact) << std::endl;
        }
    }
    
    void testAINewsIntegration() {
        std::cout << "\n2. Testing AI News Integration..." << std::endl;
        
        // Create a TradingBrain instance
        auto tradingBrain = std::make_unique<TradingBrain>();
        
        // Create sample market data
        Models::MarketData marketData;
        marketData.symbol = "EUR/USD";
        marketData.price = 1.0850;
        marketData.timestamp = std::chrono::system_clock::now();
        
        // Create sample historical data
        std::vector<Models::MarketData> historicalData;
        for (int i = 0; i < 10; ++i) {
            Models::MarketData data;
            data.symbol = "EUR/USD";
            data.price = 1.0800 + (i * 0.001);
            data.timestamp = std::chrono::system_clock::now() - std::chrono::minutes(10 - i);
            historicalData.push_back(data);
        }
        
        // Create sample news data
        std::vector<Models::NewsEvent> newsData;
        Models::NewsEvent newsEvent;
        newsEvent.title = "ECB Signals Rate Cut Possibility";
        newsEvent.description = "European Central Bank officials hint at potential interest rate reduction";
        newsEvent.impact = Models::NewsImpact::HIGH;
        newsEvent.source = "Marketaux";
        newsEvent.symbol = "EUR";
        newsEvent.releaseTime = compat::QtDateTime::currentDateTime();
        newsData.push_back(newsEvent);
        
        // Test AI decision making with news
        try {
            auto decision = tradingBrain->makeTradeDecision(marketData, historicalData, newsData);
            
            std::cout << "   ✓ AI processed news data successfully" << std::endl;
            std::cout << "   ✓ Trade decision: " << (decision.shouldTrade ? "TRADE" : "NO TRADE") << std::endl;
            std::cout << "   ✓ Confidence: " << decision.confidence << std::endl;
            
            if (decision.shouldTrade) {
                std::cout << "   ✓ Direction: " << static_cast<int>(decision.direction) << std::endl;
                std::cout << "   ✓ Amount: " << decision.suggestedAmount << std::endl;
            }
            
        } catch (const std::exception& e) {
            std::cout << "   ⚠️  AI decision making error: " << e.what() << std::endl;
            // This is expected if some components are not fully initialized
        }
    }
    
    void testMultiAPIRotation() {
        std::cout << "\n3. Testing Multi-API Rotation..." << std::endl;
        
        // Create news API client
        NewsAPIClient client;
        client.initialize(
            "ySHUjoNtBnBcOLSAJdzVXX5ISoZdk9jcxg6jgVNn", // Marketaux
            "d0s2m4hr01qumephb940d0s2m4hr01qumephb94g",   // Finnhub
            "pub_58dfe4710edf41c3be29db6efcd7afe3"        // NewsData
        );
        
        // Test API usage stats
        auto stats = client.getAPIUsageStats();
        std::cout << "   ✓ Initialized " << stats.size() << " API providers" << std::endl;
        
        for (const auto& [provider, creds] : stats) {
            std::string providerName;
            switch (provider) {
                case NewsAPIProvider::MARKETAUX: providerName = "Marketaux"; break;
                case NewsAPIProvider::FINNHUB: providerName = "Finnhub"; break;
                case NewsAPIProvider::NEWSDATA: providerName = "NewsData"; break;
                default: providerName = "Unknown"; break;
            }
            
            std::cout << "   ✓ " << providerName << ": " 
                      << (creds.isActive ? "Active" : "Inactive")
                      << " (" << creds.remainingCalls << " calls remaining)" << std::endl;
        }
        
        // Test fetching news (this will test API rotation)
        try {
            auto news = client.fetchLatestNews();
            std::cout << "   ✓ Fetched " << news.size() << " news items via API rotation" << std::endl;
            
            if (!news.empty()) {
                std::cout << "   ✓ Sample news: " << news[0].title << std::endl;
            }
        } catch (const std::exception& e) {
            std::cout << "   ⚠️  API fetch error (expected if no internet): " << e.what() << std::endl;
        }
    }
    
    void testSentimentAnalysisIntegration() {
        std::cout << "\n4. Testing Sentiment Analysis Integration..." << std::endl;
        
        NewsAPIClient client;
        client.initialize(
            "ySHUjoNtBnBcOLSAJdzVXX5ISoZdk9jcxg6jgVNn",
            "d0s2m4hr01qumephb940d0s2m4hr01qumephb94g",
            "pub_58dfe4710edf41c3be29db6efcd7afe3"
        );
        
        // Test sentiment analysis for different symbols
        std::vector<std::string> testSymbols = {"EUR/USD", "AAPL", "BTC/USD"};
        
        for (const auto& symbol : testSymbols) {
            try {
                auto sentiment = client.fetchSentimentAnalysis(symbol);
                
                std::cout << "   ✓ " << symbol << " sentiment: " << sentiment.sentimentScore << std::endl;
                std::cout << "     Market impact: " << sentiment.marketImpact << std::endl;
                std::cout << "     Trading recommended: " << (sentiment.isTradingRecommended ? "Yes" : "No") << std::endl;
                
            } catch (const std::exception& e) {
                std::cout << "   ⚠️  Sentiment analysis error for " << symbol << ": " << e.what() << std::endl;
            }
        }
    }
    
    void testRealTimeNewsUpdates() {
        std::cout << "\n5. Testing Real-Time News Updates..." << std::endl;
        
        // Create a news service
        NewsService newsService;
        newsService.initialize("default");
        
        // Set up a callback to count news updates
        int newsUpdateCount = 0;
        newsService.setNewsCallback([&newsUpdateCount](const Models::NewsEvent& event) {
            newsUpdateCount++;
            std::cout << "   📰 News update: " << event.title << std::endl;
        });
        
        // Start monitoring
        newsService.startNewsMonitoring();
        
        std::cout << "   ✓ Started real-time news monitoring" << std::endl;
        std::cout << "   ⏳ Waiting for news updates (10 seconds)..." << std::endl;
        
        // Wait for some news updates
        std::this_thread::sleep_for(std::chrono::seconds(10));
        
        std::cout << "   ✓ Received " << newsUpdateCount << " news updates" << std::endl;
        
        // Stop monitoring
        newsService.stopNewsMonitoring();
        std::cout << "   ✓ Stopped news monitoring" << std::endl;
    }
};

int main() {
    try {
        NewsIntegrationTest test;
        test.runTest();
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Test failed: " << e.what() << std::endl;
        return 1;
    }
}

# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

tests/pyquotex_rest_api_test_autogen/timestamp: CMakeFiles/3.31.5/CMakeCCompiler.cmake \
  tests/CMakeLists.txt \
  tests/pyquotex_rest_api_test.cpp \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeature.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeatureCommon.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAndroidHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAppleHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicGitHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers_v2.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomQtEntityHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTargetHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTestHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicToolHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWindowsHelpers.cmake \
  /opt/homebrew/bin/cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Config.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigExtras.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersion.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Dependencies.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Targets.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtInstallPaths.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake \
  /opt/homebrew/share/cmake/Modules/CMakeCInformation.cmake \
  /opt/homebrew/share/cmake/Modules/CMakeCommonLanguageInclude.cmake \
  /opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake \
  /opt/homebrew/share/cmake/Modules/CMakeLanguageInformation.cmake \
  /opt/homebrew/share/cmake/Modules/CheckCXXCompilerFlag.cmake \
  /opt/homebrew/share/cmake/Modules/Compiler/Clang-C.cmake \
  /opt/homebrew/share/cmake/Modules/Compiler/Clang.cmake \
  /opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake \
  /opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake \
  /opt/homebrew/share/cmake/Modules/FindPython/Support.cmake \
  /opt/homebrew/share/cmake/Modules/FindPython3.cmake \
  /opt/homebrew/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake \
  /opt/homebrew/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake \
  /opt/homebrew/share/cmake/Modules/Linker/AppleClang-C.cmake \
  /opt/homebrew/share/cmake/Modules/Linker/AppleClang.cmake \
  /opt/homebrew/share/cmake/Modules/Platform/Apple-Clang-C.cmake \
  /opt/homebrew/share/cmake/Modules/Platform/Apple-Clang.cmake \
  /opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang-C.cmake \
  /opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake \
  CMakeFiles/3.31.5/CMakeCCompiler.cmake \
  tests/CMakeLists.txt \
  tests/pyquotex_rest_api_test.cpp \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeature.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeatureCommon.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAndroidHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAppleHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicGitHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers_v2.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomQtEntityHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTargetHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTestHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicToolHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWindowsHelpers.cmake \
  /opt/homebrew/bin/cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Config.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigExtras.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersion.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Dependencies.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Targets.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtInstallPaths.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake \
  /opt/homebrew/share/cmake/Modules/CMakeCInformation.cmake \
  /opt/homebrew/share/cmake/Modules/CMakeCommonLanguageInclude.cmake \
  /opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake \
  /opt/homebrew/share/cmake/Modules/CMakeLanguageInformation.cmake \
  /opt/homebrew/share/cmake/Modules/CheckCXXCompilerFlag.cmake \
  /opt/homebrew/share/cmake/Modules/Compiler/Clang-C.cmake \
  /opt/homebrew/share/cmake/Modules/Compiler/Clang.cmake \
  /opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake \
  /opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake \
  /opt/homebrew/share/cmake/Modules/FindPython/Support.cmake \
  /opt/homebrew/share/cmake/Modules/FindPython3.cmake \
  /opt/homebrew/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake \
  /opt/homebrew/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake \
  /opt/homebrew/share/cmake/Modules/Linker/AppleClang-C.cmake \
  /opt/homebrew/share/cmake/Modules/Linker/AppleClang.cmake \
  /opt/homebrew/share/cmake/Modules/Platform/Apple-Clang-C.cmake \
  /opt/homebrew/share/cmake/Modules/Platform/Apple-Clang.cmake \
  /opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang-C.cmake \
  /opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake \
  CMakeFiles/3.31.5/CMakeCCompiler.cmake \
  tests/CMakeLists.txt \
  tests/pyquotex_rest_api_test.cpp \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeature.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeatureCommon.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAndroidHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAppleHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicGitHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers_v2.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomQtEntityHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTargetHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTestHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicToolHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWindowsHelpers.cmake \
  /opt/homebrew/bin/cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Config.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigExtras.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersion.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Dependencies.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Targets.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtInstallPaths.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake \
  /opt/homebrew/share/cmake/Modules/CMakeCInformation.cmake \
  /opt/homebrew/share/cmake/Modules/CMakeCommonLanguageInclude.cmake \
  /opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake \
  /opt/homebrew/share/cmake/Modules/CMakeLanguageInformation.cmake \
  /opt/homebrew/share/cmake/Modules/CheckCXXCompilerFlag.cmake \
  /opt/homebrew/share/cmake/Modules/Compiler/Clang-C.cmake \
  /opt/homebrew/share/cmake/Modules/Compiler/Clang.cmake \
  /opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake \
  /opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake \
  /opt/homebrew/share/cmake/Modules/FindPython/Support.cmake \
  /opt/homebrew/share/cmake/Modules/FindPython3.cmake \
  /opt/homebrew/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake \
  /opt/homebrew/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake \
  /opt/homebrew/share/cmake/Modules/Linker/AppleClang-C.cmake \
  /opt/homebrew/share/cmake/Modules/Linker/AppleClang.cmake \
  /opt/homebrew/share/cmake/Modules/Platform/Apple-Clang-C.cmake \
  /opt/homebrew/share/cmake/Modules/Platform/Apple-Clang.cmake \
  /opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang-C.cmake \
  /opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake \
  CMakeFiles/3.31.5/CMakeCCompiler.cmake \
  tests/CMakeLists.txt \
  tests/pyquotex_rest_api_test.cpp \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeature.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeatureCommon.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAndroidHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAppleHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicGitHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers_v2.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomQtEntityHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTargetHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTestHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicToolHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWindowsHelpers.cmake \
  /opt/homebrew/bin/cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Config.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigExtras.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersion.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Dependencies.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Targets.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtInstallPaths.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake \
  /opt/homebrew/share/cmake/Modules/CMakeCInformation.cmake \
  /opt/homebrew/share/cmake/Modules/CMakeCommonLanguageInclude.cmake \
  /opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake \
  /opt/homebrew/share/cmake/Modules/CMakeLanguageInformation.cmake \
  /opt/homebrew/share/cmake/Modules/CheckCXXCompilerFlag.cmake \
  /opt/homebrew/share/cmake/Modules/Compiler/Clang-C.cmake \
  /opt/homebrew/share/cmake/Modules/Compiler/Clang.cmake \
  /opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake \
  /opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake \
  /opt/homebrew/share/cmake/Modules/FindPython/Support.cmake \
  /opt/homebrew/share/cmake/Modules/FindPython3.cmake \
  /opt/homebrew/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake \
  /opt/homebrew/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake \
  /opt/homebrew/share/cmake/Modules/Linker/AppleClang-C.cmake \
  /opt/homebrew/share/cmake/Modules/Linker/AppleClang.cmake \
  /opt/homebrew/share/cmake/Modules/Platform/Apple-Clang-C.cmake \
  /opt/homebrew/share/cmake/Modules/Platform/Apple-Clang.cmake \
  /opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang-C.cmake \
  /opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake \
  CMakeFiles/3.31.5/CMakeCCompiler.cmake \
  tests/CMakeLists.txt \
  tests/pyquotex_rest_api_test.cpp \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeature.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeatureCommon.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAndroidHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAppleHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicGitHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers_v2.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomQtEntityHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTargetHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTestHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicToolHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWindowsHelpers.cmake \
  /opt/homebrew/bin/cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Config.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigExtras.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersion.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Dependencies.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Targets.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtInstallPaths.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake \
  /opt/homebrew/share/cmake/Modules/CMakeCInformation.cmake \
  /opt/homebrew/share/cmake/Modules/CMakeCommonLanguageInclude.cmake \
  /opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake \
  /opt/homebrew/share/cmake/Modules/CMakeLanguageInformation.cmake \
  /opt/homebrew/share/cmake/Modules/CheckCXXCompilerFlag.cmake \
  /opt/homebrew/share/cmake/Modules/Compiler/Clang-C.cmake \
  /opt/homebrew/share/cmake/Modules/Compiler/Clang.cmake \
  /opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake \
  /opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake \
  /opt/homebrew/share/cmake/Modules/FindPython/Support.cmake \
  /opt/homebrew/share/cmake/Modules/FindPython3.cmake \
  /opt/homebrew/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake \
  /opt/homebrew/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake \
  /opt/homebrew/share/cmake/Modules/Linker/AppleClang-C.cmake \
  /opt/homebrew/share/cmake/Modules/Linker/AppleClang.cmake \
  /opt/homebrew/share/cmake/Modules/Platform/Apple-Clang-C.cmake \
  /opt/homebrew/share/cmake/Modules/Platform/Apple-Clang.cmake \
  /opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang-C.cmake \
  /opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake \
  CMakeFiles/3.31.5/CMakeCCompiler.cmake \
  tests/CMakeLists.txt \
  tests/pyquotex_rest_api_test.cpp \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeature.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeatureCommon.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAndroidHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAppleHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicGitHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers_v2.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomQtEntityHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTargetHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTestHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicToolHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWindowsHelpers.cmake \
  /opt/homebrew/bin/cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Config.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigExtras.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersion.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Dependencies.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Targets.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtInstallPaths.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake \
  /opt/homebrew/share/cmake/Modules/CMakeCInformation.cmake \
  /opt/homebrew/share/cmake/Modules/CMakeCommonLanguageInclude.cmake \
  /opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake \
  /opt/homebrew/share/cmake/Modules/CMakeLanguageInformation.cmake \
  /opt/homebrew/share/cmake/Modules/CheckCXXCompilerFlag.cmake \
  /opt/homebrew/share/cmake/Modules/Compiler/Clang-C.cmake \
  /opt/homebrew/share/cmake/Modules/Compiler/Clang.cmake \
  /opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake \
  /opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake \
  /opt/homebrew/share/cmake/Modules/FindPython/Support.cmake \
  /opt/homebrew/share/cmake/Modules/FindPython3.cmake \
  /opt/homebrew/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake \
  /opt/homebrew/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake \
  /opt/homebrew/share/cmake/Modules/Linker/AppleClang-C.cmake \
  /opt/homebrew/share/cmake/Modules/Linker/AppleClang.cmake \
  /opt/homebrew/share/cmake/Modules/Platform/Apple-Clang-C.cmake \
  /opt/homebrew/share/cmake/Modules/Platform/Apple-Clang.cmake \
  /opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang-C.cmake \
  /opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake \
  CMakeFiles/3.31.5/CMakeCCompiler.cmake \
  tests/CMakeLists.txt \
  tests/pyquotex_rest_api_test.cpp \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeature.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeatureCommon.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAndroidHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAppleHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicGitHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers_v2.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomQtEntityHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTargetHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTestHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicToolHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake \
  /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWindowsHelpers.cmake \
  /opt/homebrew/bin/cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Config.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigExtras.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersion.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Dependencies.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Targets.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtInstallPaths.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake \
  /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake \
  /opt/homebrew/share/cmake/Modules/CMakeCInformation.cmake \
  /opt/homebrew/share/cmake/Modules/CMakeCommonLanguageInclude.cmake \
  /opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake \
  /opt/homebrew/share/cmake/Modules/CMakeLanguageInformation.cmake \
  /opt/homebrew/share/cmake/Modules/CheckCXXCompilerFlag.cmake \
  /opt/homebrew/share/cmake/Modules/Compiler/Clang-C.cmake \
  /opt/homebrew/share/cmake/Modules/Compiler/Clang.cmake \
  /opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake \
  /opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake \
  /opt/homebrew/share/cmake/Modules/FindPython/Support.cmake \
  /opt/homebrew/share/cmake/Modules/FindPython3.cmake \
  /opt/homebrew/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake \
  /opt/homebrew/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake \
  /opt/homebrew/share/cmake/Modules/Linker/AppleClang-C.cmake \
  /opt/homebrew/share/cmake/Modules/Linker/AppleClang.cmake \
  /opt/homebrew/share/cmake/Modules/Platform/Apple-Clang-C.cmake \
  /opt/homebrew/share/cmake/Modules/Platform/Apple-Clang.cmake \
  /opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang-C.cmake \
  /opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake


/opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake:

/opt/homebrew/share/cmake/Modules/Platform/Apple-Clang-C.cmake:

/opt/homebrew/share/cmake/Modules/Linker/AppleClang.cmake:

/opt/homebrew/share/cmake/Modules/Linker/AppleClang-C.cmake:

/opt/homebrew/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake:

/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake:

/opt/homebrew/share/cmake/Modules/Compiler/Clang.cmake:

/opt/homebrew/share/cmake/Modules/Compiler/Clang-C.cmake:

/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake:

/opt/homebrew/share/cmake/Modules/CheckCXXCompilerFlag.cmake:

/opt/homebrew/share/cmake/Modules/CMakeCInformation.cmake:

/opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake:

/opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake:

/opt/homebrew/share/cmake/Modules/CMakeLanguageInformation.cmake:

/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAndroidHelpers.cmake:

/opt/homebrew/bin/cmake:

/opt/homebrew/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake:

/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake:

/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake:

/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicToolHelpers.cmake:

/opt/homebrew/share/cmake/Modules/CMakeCommonLanguageInclude.cmake:

/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTargetHelpers.cmake:

/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake:

/opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Config.cmake:

/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomQtEntityHelpers.cmake:

/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWindowsHelpers.cmake:

/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake:

/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake:

tests/CMakeLists.txt:

/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake:

/opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigExtras.cmake:

/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake:

/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake:

/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomHelpers.cmake:

/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake:

/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake:

/opt/homebrew/share/cmake/Modules/FindPython3.cmake:

/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake:

/opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Targets.cmake:

/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake:

/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake:

/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers.cmake:

/opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake:

/opt/homebrew/share/cmake/Modules/FindPython/Support.cmake:

/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake:

/opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Dependencies.cmake:

/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake:

/opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtInstallPaths.cmake:

/opt/homebrew/share/cmake/Modules/Platform/Apple-Clang.cmake:

/opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersion.cmake:

/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:

/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake:

/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTestHelpers.cmake:

/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeature.cmake:

/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAppleHelpers.cmake:

/opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake:

/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicGitHelpers.cmake:

CMakeFiles/3.31.5/CMakeCCompiler.cmake:

/opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang-C.cmake:

/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeatureCommon.cmake:

tests/pyquotex_rest_api_test.cpp:

/opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake:

/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers_v2.cmake:

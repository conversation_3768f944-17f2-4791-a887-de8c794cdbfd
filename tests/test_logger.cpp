// Simple logger implementation for testing
#include "../src/utils/logger.h"
#include <iostream>
#include <mutex>

// Global logger instance
static std::mutex logMutex;

Logger& Logger::getInstance() {
    static Logger instance;
    return instance;
}

void Logger::info(const std::string& message) {
    std::lock_guard<std::mutex> lock(logMutex);
    std::cout << "[INFO] " << message << std::endl;
}

void Logger::error(const std::string& message) {
    std::lock_guard<std::mutex> lock(logMutex);
    std::cout << "[ERROR] " << message << std::endl;
}

void Logger::warning(const std::string& message) {
    std::lock_guard<std::mutex> lock(logMutex);
    std::cout << "[WARNING] " << message << std::endl;
}

void Logger::debug(const std::string& message) {
    std::lock_guard<std::mutex> lock(logMutex);
    std::cout << "[DEBUG] " << message << std::endl;
}

void Logger::log(LogLevel level, const std::string& message) {
    switch (level) {
        case LogLevel::INFO:
            info(message);
            break;
        case LogLevel::ERROR:
            error(message);
            break;
        case LogLevel::WARNING:
            warning(message);
            break;
        case LogLevel::DEBUG:
            debug(message);
            break;
    }
}

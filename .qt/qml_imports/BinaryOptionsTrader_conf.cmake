set(qml_import_scanner_imports_count 37)
set(qml_import_scanner_import_0 "CLASSNAME;QtQuick2Plugin;LINKTARGET;Qt6::qtquick2plugin;NAME;QtQuick;PATH;/opt/homebrew/share/qt/qml/QtQuick;PLUGIN;qtquick2plugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQuick/;RELATIVEPATH;QtQuick;TYPE;module;")
set(qml_import_scanner_import_1 "CLASSNAME;QtQmlPlugin;LINKTARGET;Qt6::qmlplugin;NAME;QtQml;PATH;/opt/homebrew/share/qt/qml/QtQml;PLUGIN;qmlplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQml/;RELATIVEPATH;QtQml;TYPE;module;")
set(qml_import_scanner_import_2 "NAME;QML;PATH;/opt/homebrew/share/qt/qml/QML;PREFER;:/qt-project.org/imports/QML/;RELATIVEPATH;QML;TYPE;module;")
set(qml_import_scanner_import_3 "CLASSNAME;QtQmlModelsPlugin;LINKTARGET;Qt6::modelsplugin;NAME;QtQml.Models;PATH;/opt/homebrew/share/qt/qml/QtQml/Models;PLUGIN;modelsplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQml/Models/;RELATIVEPATH;QtQml/Models;TYPE;module;")
set(qml_import_scanner_import_4 "CLASSNAME;QtQmlWorkerScriptPlugin;LINKTARGET;Qt6::workerscriptplugin;NAME;QtQml.WorkerScript;PATH;/opt/homebrew/share/qt/qml/QtQml/WorkerScript;PLUGIN;workerscriptplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQml/WorkerScript/;RELATIVEPATH;QtQml/WorkerScript;TYPE;module;")
set(qml_import_scanner_import_5 "CLASSNAME;QtQuickControls2Plugin;LINKTARGET;Qt6::qtquickcontrols2plugin;NAME;QtQuick.Controls;PATH;/opt/homebrew/share/qt/qml/QtQuick/Controls;PLUGIN;qtquickcontrols2plugin;PREFER;:/qt-project.org/imports/QtQuick/Controls/;RELATIVEPATH;QtQuick/Controls;TYPE;module;")
set(qml_import_scanner_import_6 "CLASSNAME;QtQuickControls2FusionStylePlugin;COMPONENTS;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/ApplicationWindow.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/BusyIndicator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/Button.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/CheckBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/CheckDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/ComboBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/DelayButton.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/Dial.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/Dialog.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/DialogButtonBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/Drawer.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/Frame.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/GroupBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/HorizontalHeaderView.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/ItemDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/Label.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/Menu.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/MenuBar.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/MenuBarItem.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/MenuItem.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/MenuSeparator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/Page.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/PageIndicator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/Pane.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/Popup.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/ProgressBar.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/RadioButton.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/RadioDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/RangeSlider.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/RoundButton.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/ScrollBar.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/ScrollIndicator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/ScrollView.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/SelectionRectangle.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/Slider.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/SpinBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/SplitView.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/SwipeDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/Switch.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/SwitchDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/TabBar.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/TabButton.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/TextArea.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/TextField.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/ToolBar.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/ToolButton.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/ToolSeparator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/ToolTip.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/TreeViewDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/Tumbler.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/VerticalHeaderView.qml;LINKTARGET;Qt6::qtquickcontrols2fusionstyleplugin;NAME;QtQuick.Controls.Fusion;PATH;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion;PLUGIN;qtquickcontrols2fusionstyleplugin;PREFER;:/qt-project.org/imports/QtQuick/Controls/Fusion/;RELATIVEPATH;QtQuick/Controls/Fusion;TYPE;module;")
set(qml_import_scanner_import_7 "CLASSNAME;QtQuickControls2MaterialStylePlugin;COMPONENTS;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/ApplicationWindow.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/BusyIndicator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/Button.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/CheckBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/CheckDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/ComboBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/DelayButton.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/Dial.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/Dialog.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/DialogButtonBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/Drawer.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/Frame.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/GroupBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/HorizontalHeaderView.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/ItemDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/Label.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/Menu.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/MenuBar.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/MenuBarItem.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/MenuItem.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/MenuSeparator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/Page.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/PageIndicator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/Pane.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/Popup.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/ProgressBar.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/RadioButton.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/RadioDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/RangeSlider.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/RoundButton.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/ScrollBar.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/ScrollIndicator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/ScrollView.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/SelectionRectangle.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/Slider.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/SpinBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/SplitView.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/StackView.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/SwipeDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/SwipeView.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/Switch.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/SwitchDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/TabBar.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/TabButton.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/TextArea.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/TextField.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/ToolBar.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/ToolButton.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/ToolSeparator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/ToolTip.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/TreeViewDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/Tumbler.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/VerticalHeaderView.qml;LINKTARGET;Qt6::qtquickcontrols2materialstyleplugin;NAME;QtQuick.Controls.Material;PATH;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material;PLUGIN;qtquickcontrols2materialstyleplugin;PREFER;:/qt-project.org/imports/QtQuick/Controls/Material/;RELATIVEPATH;QtQuick/Controls/Material;TYPE;module;")
set(qml_import_scanner_import_8 "CLASSNAME;QtQuickControls2ImagineStylePlugin;COMPONENTS;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/ApplicationWindow.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/BusyIndicator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/Button.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/CheckBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/CheckDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/ComboBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/DelayButton.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/Dial.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/Dialog.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/DialogButtonBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/Drawer.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/Frame.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/GroupBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/HorizontalHeaderView.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/ItemDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/Label.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/Menu.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/MenuItem.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/MenuSeparator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/Page.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/PageIndicator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/Pane.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/Popup.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/ProgressBar.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/RadioButton.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/RadioDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/RangeSlider.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/RoundButton.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/ScrollBar.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/ScrollIndicator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/ScrollView.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/SelectionRectangle.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/Slider.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/SpinBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/SplitView.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/StackView.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/SwipeDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/SwipeView.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/Switch.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/SwitchDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/TabBar.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/TabButton.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/TextArea.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/TextField.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/ToolBar.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/ToolButton.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/ToolSeparator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/ToolTip.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/Tumbler.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/VerticalHeaderView.qml;LINKTARGET;Qt6::qtquickcontrols2imaginestyleplugin;NAME;QtQuick.Controls.Imagine;PATH;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine;PLUGIN;qtquickcontrols2imaginestyleplugin;PREFER;:/qt-project.org/imports/QtQuick/Controls/Imagine/;RELATIVEPATH;QtQuick/Controls/Imagine;TYPE;module;")
set(qml_import_scanner_import_9 "CLASSNAME;QtQuickControls2UniversalStylePlugin;COMPONENTS;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/ApplicationWindow.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/BusyIndicator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/Button.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/CheckBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/CheckDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/ComboBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/DelayButton.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/Dial.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/Dialog.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/DialogButtonBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/Drawer.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/Frame.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/GroupBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/HorizontalHeaderView.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/ItemDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/Label.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/Menu.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/MenuBar.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/MenuBarItem.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/MenuItem.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/MenuSeparator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/Page.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/PageIndicator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/Pane.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/Popup.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/ProgressBar.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/RadioButton.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/RadioDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/RangeSlider.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/RoundButton.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/ScrollBar.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/ScrollIndicator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/ScrollView.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/SelectionRectangle.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/Slider.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/SpinBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/SplitView.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/StackView.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/SwipeDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/Switch.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/SwitchDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/TabBar.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/TabButton.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/TextArea.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/TextField.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/ToolBar.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/ToolButton.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/ToolSeparator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/ToolTip.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/Tumbler.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/VerticalHeaderView.qml;LINKTARGET;Qt6::qtquickcontrols2universalstyleplugin;NAME;QtQuick.Controls.Universal;PATH;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal;PLUGIN;qtquickcontrols2universalstyleplugin;PREFER;:/qt-project.org/imports/QtQuick/Controls/Universal/;RELATIVEPATH;QtQuick/Controls/Universal;TYPE;module;")
set(qml_import_scanner_import_10 "CLASSNAME;QtQuickControls2FluentWinUI3StylePlugin;COMPONENTS;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/ApplicationWindow.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/BusyIndicator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/Button.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/CheckBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/CheckDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/ComboBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/Config.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/DelayButton.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/Dialog.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/DialogButtonBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/FocusFrame.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/Frame.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/GroupBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/ItemDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/Menu.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/MenuBar.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/MenuBarItem.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/MenuItem.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/MenuSeparator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/PageIndicator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/Popup.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/ProgressBar.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/RadioButton.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/RadioDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/RangeSlider.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/RoundButton.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/Slider.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/SpinBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/StyleImage.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/SwipeDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/Switch.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/SwitchDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/TabBar.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/TabButton.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/TextArea.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/TextField.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/ToolBar.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/ToolButton.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/ToolSeparator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/ToolTip.qml;LINKTARGET;Qt6::qtquickcontrols2fluentwinui3styleplugin;NAME;QtQuick.Controls.FluentWinUI3;PATH;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3;PLUGIN;qtquickcontrols2fluentwinui3styleplugin;PREFER;:/qt-project.org/imports/QtQuick/Controls/FluentWinUI3/;RELATIVEPATH;QtQuick/Controls/FluentWinUI3;TYPE;module;")
set(qml_import_scanner_import_11 "NAME;QtQuick.Controls.Windows;TYPE;module;")
set(qml_import_scanner_import_12 "CLASSNAME;QtQuickControls2MacOSStylePlugin;COMPONENTS;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/BusyIndicator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/Button.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/CheckBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/CheckDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/ComboBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/DelayButton.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/Dial.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/Dialog.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/DialogButtonBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/Frame.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/GroupBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/ItemDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/Menu.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/MenuBar.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/MenuBarItem.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/MenuItem.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/MenuSeparator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/ProgressBar.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/RadioButton.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/RadioDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/RangeSlider.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/ScrollBar.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/ScrollIndicator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/ScrollView.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/SelectionRectangle.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/Slider.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/SpinBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/Switch.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/SwitchDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/TextArea.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/TextField.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/TreeViewDelegate.qml;LINKTARGET;Qt6::qtquickcontrols2macosstyleplugin;NAME;QtQuick.Controls.macOS;PATH;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS;PLUGIN;qtquickcontrols2macosstyleplugin;PREFER;:/qt-project.org/imports/QtQuick/Controls/macOS/;RELATIVEPATH;QtQuick/Controls/macOS;TYPE;module;")
set(qml_import_scanner_import_13 "CLASSNAME;QtQuickControls2IOSStylePlugin;COMPONENTS;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/BusyIndicator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/Button.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/CheckBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/CheckDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/ComboBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/DelayButton.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/Dial.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/Dialog.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/DialogButtonBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/Drawer.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/Frame.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/GroupBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/HorizontalHeaderView.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/ItemDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/Menu.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/MenuBar.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/MenuBarItem.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/MenuItem.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/MenuSeparator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/PageIndicator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/Popup.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/ProgressBar.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/RadioButton.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/RadioDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/RangeSlider.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/ScrollBar.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/ScrollIndicator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/SelectionRectangle.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/Slider.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/SpinBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/SplitView.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/StackView.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/SwipeDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/Switch.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/SwitchDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/TabBar.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/TabButton.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/TextArea.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/TextField.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/ToolBar.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/ToolButton.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/ToolSeparator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/ToolTip.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/TreeViewDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/VerticalHeaderView.qml;LINKTARGET;Qt6::qtquickcontrols2iosstyleplugin;NAME;QtQuick.Controls.iOS;PATH;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS;PLUGIN;qtquickcontrols2iosstyleplugin;PREFER;:/qt-project.org/imports/QtQuick/Controls/iOS/;RELATIVEPATH;QtQuick/Controls/iOS;TYPE;module;")
set(qml_import_scanner_import_14 "CLASSNAME;QtQuickControls2BasicStylePlugin;COMPONENTS;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/AbstractButton.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/Action.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/ActionGroup.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/ApplicationWindow.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/BusyIndicator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/Button.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/ButtonGroup.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/Calendar.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/CalendarModel.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/CheckBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/CheckDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/ComboBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/Container.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/Control.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/DayOfWeekRow.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/DelayButton.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/Dial.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/Dialog.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/DialogButtonBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/Drawer.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/Frame.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/GroupBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/HorizontalHeaderView.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/ItemDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/Label.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/Menu.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/MenuBar.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/MenuBarItem.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/MenuItem.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/MenuSeparator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/MonthGrid.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/Page.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/PageIndicator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/Pane.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/Popup.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/ProgressBar.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/RadioButton.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/RadioDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/RangeSlider.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/RoundButton.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/ScrollBar.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/ScrollIndicator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/ScrollView.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/SelectionRectangle.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/Slider.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/SpinBox.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/SplitView.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/StackView.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/SwipeDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/SwipeView.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/Switch.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/SwitchDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/TabBar.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/TabButton.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/TableViewDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/TextArea.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/TextField.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/ToolBar.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/ToolButton.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/ToolSeparator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/ToolTip.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/TreeViewDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/Tumbler.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/VerticalHeaderView.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/WeekNumberColumn.qml;LINKTARGET;Qt6::qtquickcontrols2basicstyleplugin;NAME;QtQuick.Controls.Basic;PATH;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic;PLUGIN;qtquickcontrols2basicstyleplugin;PREFER;:/qt-project.org/imports/QtQuick/Controls/Basic/;RELATIVEPATH;QtQuick/Controls/Basic;TYPE;module;")
set(qml_import_scanner_import_15 "CLASSNAME;QtQuickTemplates2Plugin;LINKTARGET;Qt6::qtquicktemplates2plugin;NAME;QtQuick.Templates;PATH;/opt/homebrew/share/qt/qml/QtQuick/Templates;PLUGIN;qtquicktemplates2plugin;PREFER;:/qt-project.org/imports/QtQuick/Templates/;RELATIVEPATH;QtQuick/Templates;TYPE;module;")
set(qml_import_scanner_import_16 "CLASSNAME;QtQuickControls2ImplPlugin;COMPONENTS;/opt/homebrew/share/qt/qml/QtQuick/Controls/impl/CopyAction.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/impl/CutAction.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/impl/DeleteAction.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/impl/PasteAction.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/impl/SelectAllAction.qml;LINKTARGET;Qt6::qtquickcontrols2implplugin;NAME;QtQuick.Controls.impl;PATH;/opt/homebrew/share/qt/qml/QtQuick/Controls/impl;PLUGIN;qtquickcontrols2implplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQuick/Controls/impl/;RELATIVEPATH;QtQuick/Controls/impl;TYPE;module;")
set(qml_import_scanner_import_17 "CLASSNAME;QtQuickControls2FusionStyleImplPlugin;COMPONENTS;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/impl/ButtonPanel.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/impl/CheckIndicator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/impl/CopyAction.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/impl/CutAction.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/impl/DeleteAction.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/impl/PasteAction.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/impl/RadioIndicator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/impl/SelectAllAction.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/impl/SliderGroove.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/impl/SliderHandle.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/impl/SwitchIndicator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/impl/TextEditingContextMenu.qml;LINKTARGET;Qt6::qtquickcontrols2fusionstyleimplplugin;NAME;QtQuick.Controls.Fusion.impl;PATH;/opt/homebrew/share/qt/qml/QtQuick/Controls/Fusion/impl;PLUGIN;qtquickcontrols2fusionstyleimplplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQuick/Controls/Fusion/impl/;RELATIVEPATH;QtQuick/Controls/Fusion/impl;TYPE;module;")
set(qml_import_scanner_import_18 "CLASSNAME;QtQuick_WindowPlugin;LINKTARGET;Qt6::quickwindow;NAME;QtQuick.Window;PATH;/opt/homebrew/share/qt/qml/QtQuick/Window;PLUGIN;quickwindowplugin;PREFER;:/qt-project.org/imports/QtQuick/Window/;RELATIVEPATH;QtQuick/Window;TYPE;module;")
set(qml_import_scanner_import_19 "CLASSNAME;QtQuickControls2MaterialStyleImplPlugin;COMPONENTS;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/impl/BoxShadow.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/impl/CheckIndicator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/impl/CursorDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/impl/ElevationEffect.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/impl/RadioIndicator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/impl/RectangularGlow.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/impl/RoundedElevationEffect.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/impl/SliderHandle.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/impl/SwitchIndicator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/impl/TextEditingContextMenu.qml;LINKTARGET;Qt6::qtquickcontrols2materialstyleimplplugin;NAME;QtQuick.Controls.Material.impl;PATH;/opt/homebrew/share/qt/qml/QtQuick/Controls/Material/impl;PLUGIN;qtquickcontrols2materialstyleimplplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQuick/Controls/Material/impl/;RELATIVEPATH;QtQuick/Controls/Material/impl;TYPE;module;")
set(qml_import_scanner_import_20 "CLASSNAME;QtQuickControls2ImagineStyleImplPlugin;COMPONENTS;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/impl/OpacityMask.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/impl/TextEditingContextMenu.qml;LINKTARGET;Qt6::qtquickcontrols2imaginestyleimplplugin;NAME;QtQuick.Controls.Imagine.impl;PATH;/opt/homebrew/share/qt/qml/QtQuick/Controls/Imagine/impl;PLUGIN;qtquickcontrols2imaginestyleimplplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQuick/Controls/Imagine/impl/;RELATIVEPATH;QtQuick/Controls/Imagine/impl;TYPE;module;")
set(qml_import_scanner_import_21 "CLASSNAME;QtQuickControls2UniversalStyleImplPlugin;COMPONENTS;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/impl/CheckIndicator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/impl/CopyAction.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/impl/CutAction.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/impl/DeleteAction.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/impl/PasteAction.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/impl/RadioIndicator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/impl/SelectAllAction.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/impl/SwitchIndicator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/impl/TextEditingContextMenu.qml;LINKTARGET;Qt6::qtquickcontrols2universalstyleimplplugin;NAME;QtQuick.Controls.Universal.impl;PATH;/opt/homebrew/share/qt/qml/QtQuick/Controls/Universal/impl;PLUGIN;qtquickcontrols2universalstyleimplplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQuick/Controls/Universal/impl/;RELATIVEPATH;QtQuick/Controls/Universal/impl;TYPE;module;")
set(qml_import_scanner_import_22 "CLASSNAME;QtQuickControls2FluentWinUI3StyleImplPlugin;COMPONENTS;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/impl/ButtonBackground.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/impl/CheckIndicator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/impl/CopyAction.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/impl/CutAction.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/impl/DeleteAction.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/impl/FocusFrame.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/impl/PasteAction.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/impl/RadioIndicator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/impl/SelectAllAction.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/impl/StyleImage.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/impl/SwitchIndicator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/impl/TextEditingContextMenu.qml;LINKTARGET;Qt6::qtquickcontrols2fluentwinui3styleimplplugin;NAME;QtQuick.Controls.FluentWinUI3.impl;PATH;/opt/homebrew/share/qt/qml/QtQuick/Controls/FluentWinUI3/impl;PLUGIN;qtquickcontrols2fluentwinui3styleimplplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQuick/Controls/FluentWinUI3/impl/;RELATIVEPATH;QtQuick/Controls/FluentWinUI3/impl;TYPE;module;")
set(qml_import_scanner_import_23 "CLASSNAME;QtQuickEffectsPlugin;LINKTARGET;Qt6::effectsplugin;NAME;QtQuick.Effects;PATH;/opt/homebrew/share/qt/qml/QtQuick/Effects;PLUGIN;effectsplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQuick/Effects/;RELATIVEPATH;QtQuick/Effects;TYPE;module;")
set(qml_import_scanner_import_24 "CLASSNAME;QtQuickLayoutsPlugin;LINKTARGET;Qt6::qquicklayoutsplugin;NAME;QtQuick.Layouts;PATH;/opt/homebrew/share/qt/qml/QtQuick/Layouts;PLUGIN;qquicklayoutsplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQuick/Layouts/;RELATIVEPATH;QtQuick/Layouts;TYPE;module;")
set(qml_import_scanner_import_25 "CLASSNAME;QmlShapesPlugin;LINKTARGET;Qt6::qmlshapesplugin;NAME;QtQuick.Shapes;PATH;/opt/homebrew/share/qt/qml/QtQuick/Shapes;PLUGIN;qmlshapesplugin;PREFER;:/qt-project.org/imports/QtQuick/Shapes/;RELATIVEPATH;QtQuick/Shapes;TYPE;module;")
set(qml_import_scanner_import_26 "CLASSNAME;QtQuickControls2NativeStylePlugin;COMPONENTS;/opt/homebrew/share/qt/qml/QtQuick/NativeStyle/controls/DefaultButton.qml;/opt/homebrew/share/qt/qml/QtQuick/NativeStyle/controls/DefaultCheckBox.qml;/opt/homebrew/share/qt/qml/QtQuick/NativeStyle/controls/DefaultComboBox.qml;/opt/homebrew/share/qt/qml/QtQuick/NativeStyle/controls/DefaultDial.qml;/opt/homebrew/share/qt/qml/QtQuick/NativeStyle/controls/DefaultFrame.qml;/opt/homebrew/share/qt/qml/QtQuick/NativeStyle/controls/DefaultGroupBox.qml;/opt/homebrew/share/qt/qml/QtQuick/NativeStyle/controls/DefaultItemDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/NativeStyle/controls/DefaultItemDelegateIconLabel.qml;/opt/homebrew/share/qt/qml/QtQuick/NativeStyle/controls/DefaultProgressBar.qml;/opt/homebrew/share/qt/qml/QtQuick/NativeStyle/controls/DefaultRadioButton.qml;/opt/homebrew/share/qt/qml/QtQuick/NativeStyle/controls/DefaultRadioDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/NativeStyle/controls/DefaultScrollBar.qml;/opt/homebrew/share/qt/qml/QtQuick/NativeStyle/controls/DefaultSlider.qml;/opt/homebrew/share/qt/qml/QtQuick/NativeStyle/controls/DefaultSpinBox.qml;/opt/homebrew/share/qt/qml/QtQuick/NativeStyle/controls/DefaultTextArea.qml;/opt/homebrew/share/qt/qml/QtQuick/NativeStyle/controls/DefaultTextField.qml;/opt/homebrew/share/qt/qml/QtQuick/NativeStyle/controls/DefaultTreeViewDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/NativeStyle/util/MacFocusFrame.qml;LINKTARGET;Qt6::qtquickcontrols2nativestyleplugin;NAME;QtQuick.NativeStyle;PATH;/opt/homebrew/share/qt/qml/QtQuick/NativeStyle;PLUGIN;qtquickcontrols2nativestyleplugin;PREFER;:/qt-project.org/imports/QtQuick/NativeStyle/;RELATIVEPATH;QtQuick/NativeStyle;TYPE;module;")
set(qml_import_scanner_import_27 "CLASSNAME;QtQuickControls2macOSStyleImplPlugin;COMPONENTS;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/impl/CheckIndicator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/impl/CopyAction.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/impl/CutAction.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/impl/DeleteAction.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/impl/PasteAction.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/impl/SelectAllAction.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/impl/SwitchHandle.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/impl/SwitchIndicator.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/impl/TextEditingContextMenu.qml;LINKTARGET;Qt6::qtquickcontrols2macosstyleimplplugin;NAME;QtQuick.Controls.macOS.impl;PATH;/opt/homebrew/share/qt/qml/QtQuick/Controls/macOS/impl;PLUGIN;qtquickcontrols2macosstyleimplplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQuick/Controls/macOS/impl/;RELATIVEPATH;QtQuick/Controls/macOS/impl;TYPE;module;")
set(qml_import_scanner_import_28 "CLASSNAME;QtQuick_Controls_iOS_implPlugin;COMPONENTS;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/impl/CopyAction.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/impl/CursorDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/impl/CutAction.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/impl/DeleteAction.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/impl/DialogButtonBoxDelegate.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/impl/PasteAction.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/impl/SelectAllAction.qml;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/impl/TextEditingContextMenu.qml;LINKTARGET;Qt6::qtquickcontrols2iosstyleimplplugin;NAME;QtQuick.Controls.iOS.impl;PATH;/opt/homebrew/share/qt/qml/QtQuick/Controls/iOS/impl;PLUGIN;qtquickcontrols2iosstyleimplplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQuick/Controls/iOS/impl/;RELATIVEPATH;QtQuick/Controls/iOS/impl;TYPE;module;")
set(qml_import_scanner_import_29 "CLASSNAME;QtQuickControls2BasicStyleImplPlugin;COMPONENTS;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/impl/TextEditingContextMenu.qml;LINKTARGET;Qt6::qtquickcontrols2basicstyleimplplugin;NAME;QtQuick.Controls.Basic.impl;PATH;/opt/homebrew/share/qt/qml/QtQuick/Controls/Basic/impl;PLUGIN;qtquickcontrols2basicstyleimplplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQuick/Controls/Basic/impl/;RELATIVEPATH;QtQuick/Controls/Basic/impl;TYPE;module;")
set(qml_import_scanner_import_30 "CLASSNAME;QtQmlLabsModelsPlugin;LINKTARGET;Qt6::labsmodelsplugin;NAME;Qt.labs.qmlmodels;PATH;/opt/homebrew/share/qt/qml/Qt/labs/qmlmodels;PLUGIN;labsmodelsplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/Qt/labs/qmlmodels/;RELATIVEPATH;Qt/labs/qmlmodels;TYPE;module;")
set(qml_import_scanner_import_31 "NAME;Terminal;TYPE;module;")
set(qml_import_scanner_import_32 "NAME;components;PATH;/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/qml/components;TYPE;directory;")
set(qml_import_scanner_import_33 "NAME;popups;PATH;/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/qml/popups;TYPE;directory;")
set(qml_import_scanner_import_34 "NAME;styles;PATH;/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/qml/styles;TYPE;directory;")
set(qml_import_scanner_import_35 "NAME;.;PATH;/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/qml/styles;TYPE;directory;")
set(qml_import_scanner_import_36 "NAME;QtGraphicalEffects;TYPE;module;")


/****************************************************************************
** Meta object code from reading C++ file 'qml_bridge.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../src/terminal/qml_bridge.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'qml_bridge.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN8Terminal9QmlBridgeE_t {};
} // unnamed namespace

template <> constexpr inline auto Terminal::QmlBridge::qt_create_metaobjectdata<qt_meta_tag_ZN8Terminal9QmlBridgeE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "Terminal::QmlBridge",
        "accountBalanceChanged",
        "",
        "accountEquityChanged",
        "profitLossChanged",
        "connectionStatusChanged",
        "marketStatusChanged",
        "userNicknameChanged",
        "userProfileIdChanged",
        "userAvatarChanged",
        "userCountryChanged",
        "userCountryNameChanged",
        "userCurrencyCodeChanged",
        "userCurrencySymbolChanged",
        "userDemoBalanceChanged",
        "userLiveBalanceChanged",
        "userTimezoneChanged",
        "autoPilotStatusChanged",
        "indicatorsChanged",
        "sentimentChanged",
        "priceDataChanged",
        "activeOrdersChanged",
        "loadingStatusChanged",
        "errorMessageChanged",
        "currentPhaseChanged",
        "availableTradingPairsChanged",
        "currentTradingPairChanged",
        "strategyTestResultsChanged",
        "isStrategyTestingChanged",
        "currentTestingStrategyChanged",
        "overallWinRateChanged",
        "strategyTestCompleted",
        "strategyName",
        "winRate",
        "QVariantMap",
        "metrics",
        "allStrategiesTestCompleted",
        "overallWinRate",
        "performanceTestCompleted",
        "results",
        "phaseDescriptionsChanged",
        "autoPhaseChanged",
        "phaseRepetitionsChanged",
        "phaseProgressChanged",
        "completedPhasesChanged",
        "detectedPatternsChanged",
        "launchTimeChanged",
        "topNewsHeadlineChanged",
        "newsSentimentScoreChanged",
        "newsSentimentTextChanged",
        "newsSentimentColorChanged",
        "newsConfidenceLevelChanged",
        "newsSourceChanged",
        "hasActiveNewsChanged",
        "notification",
        "message",
        "type",
        "tradeResult",
        "success",
        "connectionStatusUpdate",
        "status",
        "isError",
        "connect",
        "disconnect",
        "placeTrade",
        "asset",
        "amount",
        "direction",
        "expiryMinutes",
        "toggleAutoPilot",
        "enabled",
        "showSettings",
        "refreshData",
        "addIndicator",
        "name",
        "removeIndicator",
        "updateChartTimeframe",
        "timeframe",
        "startBot",
        "pauseBot",
        "stopBot",
        "setTradingMode",
        "mode",
        "setBinaryOptionsBot",
        "std::shared_ptr<BinaryOptionsBot>",
        "bot",
        "releaseBotReference",
        "submitPinCode",
        "pinCode",
        "detectAIPatterns",
        "fetchRealCandlestickData",
        "period",
        "count",
        "triggerPatternDetection",
        "connectPyQuotexAPI",
        "getBestAvailableAsset",
        "updateTradingPairs",
        "testAllStrategies",
        "testSpecificStrategy",
        "getAvailableStrategies",
        "QVariantList",
        "getStrategyPerformance",
        "resetStrategyTests",
        "runPerformanceTest",
        "setLoadingState",
        "isLoading",
        "setConnected",
        "connected",
        "setAccountBalance",
        "balance",
        "setMarketStatus",
        "setAutoPilotActive",
        "active",
        "setSentiment",
        "sentiment",
        "setPriceData",
        "priceData",
        "setLaunchTime",
        "seconds",
        "accountBalance",
        "accountEquity",
        "profitLoss",
        "isConnected",
        "marketStatus",
        "userNickname",
        "userProfileId",
        "userAvatar",
        "userCountry",
        "userCountryName",
        "userCurrencyCode",
        "userCurrencySymbol",
        "userDemoBalance",
        "userLiveBalance",
        "userTimezone",
        "isAutoPilotActive",
        "indicators",
        "activeOrders",
        "errorMessage",
        "currentPhase",
        "availableTradingPairs",
        "currentTradingPair",
        "strategyTestResults",
        "isStrategyTesting",
        "currentTestingStrategy",
        "phaseDescriptions",
        "autoPhaseMode",
        "phaseRepetitions",
        "phaseProgress",
        "completedPhases",
        "detectedPatterns",
        "launchTime",
        "topNewsHeadline",
        "newsSentimentScore",
        "newsSentimentText",
        "newsSentimentColor",
        "newsConfidenceLevel",
        "newsSource",
        "hasActiveNews"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'accountBalanceChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'accountEquityChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'profitLossChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'connectionStatusChanged'
        QtMocHelpers::SignalData<void()>(5, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'marketStatusChanged'
        QtMocHelpers::SignalData<void()>(6, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'userNicknameChanged'
        QtMocHelpers::SignalData<void()>(7, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'userProfileIdChanged'
        QtMocHelpers::SignalData<void()>(8, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'userAvatarChanged'
        QtMocHelpers::SignalData<void()>(9, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'userCountryChanged'
        QtMocHelpers::SignalData<void()>(10, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'userCountryNameChanged'
        QtMocHelpers::SignalData<void()>(11, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'userCurrencyCodeChanged'
        QtMocHelpers::SignalData<void()>(12, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'userCurrencySymbolChanged'
        QtMocHelpers::SignalData<void()>(13, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'userDemoBalanceChanged'
        QtMocHelpers::SignalData<void()>(14, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'userLiveBalanceChanged'
        QtMocHelpers::SignalData<void()>(15, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'userTimezoneChanged'
        QtMocHelpers::SignalData<void()>(16, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'autoPilotStatusChanged'
        QtMocHelpers::SignalData<void()>(17, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'indicatorsChanged'
        QtMocHelpers::SignalData<void()>(18, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'sentimentChanged'
        QtMocHelpers::SignalData<void()>(19, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'priceDataChanged'
        QtMocHelpers::SignalData<void()>(20, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'activeOrdersChanged'
        QtMocHelpers::SignalData<void()>(21, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'loadingStatusChanged'
        QtMocHelpers::SignalData<void()>(22, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'errorMessageChanged'
        QtMocHelpers::SignalData<void()>(23, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'currentPhaseChanged'
        QtMocHelpers::SignalData<void()>(24, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'availableTradingPairsChanged'
        QtMocHelpers::SignalData<void()>(25, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'currentTradingPairChanged'
        QtMocHelpers::SignalData<void()>(26, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'strategyTestResultsChanged'
        QtMocHelpers::SignalData<void()>(27, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'isStrategyTestingChanged'
        QtMocHelpers::SignalData<void()>(28, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'currentTestingStrategyChanged'
        QtMocHelpers::SignalData<void()>(29, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'overallWinRateChanged'
        QtMocHelpers::SignalData<void()>(30, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'strategyTestCompleted'
        QtMocHelpers::SignalData<void(const QString &, double, const QVariantMap &)>(31, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 32 }, { QMetaType::Double, 33 }, { 0x80000000 | 34, 35 },
        }}),
        // Signal 'allStrategiesTestCompleted'
        QtMocHelpers::SignalData<void(double)>(36, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Double, 37 },
        }}),
        // Signal 'performanceTestCompleted'
        QtMocHelpers::SignalData<void(const QVariantMap &)>(38, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 34, 39 },
        }}),
        // Signal 'phaseDescriptionsChanged'
        QtMocHelpers::SignalData<void()>(40, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'autoPhaseChanged'
        QtMocHelpers::SignalData<void()>(41, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'phaseRepetitionsChanged'
        QtMocHelpers::SignalData<void()>(42, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'phaseProgressChanged'
        QtMocHelpers::SignalData<void()>(43, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'completedPhasesChanged'
        QtMocHelpers::SignalData<void()>(44, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'detectedPatternsChanged'
        QtMocHelpers::SignalData<void()>(45, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'launchTimeChanged'
        QtMocHelpers::SignalData<void()>(46, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'topNewsHeadlineChanged'
        QtMocHelpers::SignalData<void()>(47, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'newsSentimentScoreChanged'
        QtMocHelpers::SignalData<void()>(48, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'newsSentimentTextChanged'
        QtMocHelpers::SignalData<void()>(49, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'newsSentimentColorChanged'
        QtMocHelpers::SignalData<void()>(50, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'newsConfidenceLevelChanged'
        QtMocHelpers::SignalData<void()>(51, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'newsSourceChanged'
        QtMocHelpers::SignalData<void()>(52, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'hasActiveNewsChanged'
        QtMocHelpers::SignalData<void()>(53, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'notification'
        QtMocHelpers::SignalData<void(const QString &, const QString &)>(54, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 55 }, { QMetaType::QString, 56 },
        }}),
        // Signal 'tradeResult'
        QtMocHelpers::SignalData<void(bool, const QString &)>(57, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 58 }, { QMetaType::QString, 55 },
        }}),
        // Signal 'connectionStatusUpdate'
        QtMocHelpers::SignalData<void(const QString &, bool)>(59, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 60 }, { QMetaType::Bool, 61 },
        }}),
        // Slot 'connect'
        QtMocHelpers::SlotData<void()>(62, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'disconnect'
        QtMocHelpers::SlotData<void()>(63, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'placeTrade'
        QtMocHelpers::SlotData<void(const QString &, double, const QString &, int)>(64, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 65 }, { QMetaType::Double, 66 }, { QMetaType::QString, 67 }, { QMetaType::Int, 68 },
        }}),
        // Slot 'toggleAutoPilot'
        QtMocHelpers::SlotData<void(bool)>(69, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 70 },
        }}),
        // Slot 'showSettings'
        QtMocHelpers::SlotData<void()>(71, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'refreshData'
        QtMocHelpers::SlotData<void()>(72, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'addIndicator'
        QtMocHelpers::SlotData<void(const QString &)>(73, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 74 },
        }}),
        // Slot 'removeIndicator'
        QtMocHelpers::SlotData<void(const QString &)>(75, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 74 },
        }}),
        // Slot 'updateChartTimeframe'
        QtMocHelpers::SlotData<void(const QString &)>(76, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 77 },
        }}),
        // Slot 'startBot'
        QtMocHelpers::SlotData<void()>(78, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'pauseBot'
        QtMocHelpers::SlotData<void()>(79, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'stopBot'
        QtMocHelpers::SlotData<void()>(80, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'setTradingMode'
        QtMocHelpers::SlotData<void(const QString &)>(81, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 82 },
        }}),
        // Slot 'setBinaryOptionsBot'
        QtMocHelpers::SlotData<void(std::shared_ptr<BinaryOptionsBot>)>(83, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 84, 85 },
        }}),
        // Slot 'releaseBotReference'
        QtMocHelpers::SlotData<void()>(86, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'submitPinCode'
        QtMocHelpers::SlotData<bool(const QString &)>(87, 2, QMC::AccessPublic, QMetaType::Bool, {{
            { QMetaType::QString, 88 },
        }}),
        // Slot 'detectAIPatterns'
        QtMocHelpers::SlotData<void()>(89, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'fetchRealCandlestickData'
        QtMocHelpers::SlotData<void(const QString &, int, int)>(90, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 65 }, { QMetaType::Int, 91 }, { QMetaType::Int, 92 },
        }}),
        // Slot 'fetchRealCandlestickData'
        QtMocHelpers::SlotData<void(const QString &, int)>(90, 2, QMC::AccessPublic | QMC::MethodCloned, QMetaType::Void, {{
            { QMetaType::QString, 65 }, { QMetaType::Int, 91 },
        }}),
        // Slot 'fetchRealCandlestickData'
        QtMocHelpers::SlotData<void(const QString &)>(90, 2, QMC::AccessPublic | QMC::MethodCloned, QMetaType::Void, {{
            { QMetaType::QString, 65 },
        }}),
        // Slot 'fetchRealCandlestickData'
        QtMocHelpers::SlotData<void()>(90, 2, QMC::AccessPublic | QMC::MethodCloned, QMetaType::Void),
        // Slot 'triggerPatternDetection'
        QtMocHelpers::SlotData<void()>(93, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'connectPyQuotexAPI'
        QtMocHelpers::SlotData<bool()>(94, 2, QMC::AccessPublic, QMetaType::Bool),
        // Slot 'getBestAvailableAsset'
        QtMocHelpers::SlotData<QString()>(95, 2, QMC::AccessPublic, QMetaType::QString),
        // Slot 'updateTradingPairs'
        QtMocHelpers::SlotData<void()>(96, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'testAllStrategies'
        QtMocHelpers::SlotData<void()>(97, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'testSpecificStrategy'
        QtMocHelpers::SlotData<void(const QString &)>(98, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 32 },
        }}),
        // Slot 'getAvailableStrategies'
        QtMocHelpers::SlotData<QVariantList()>(99, 2, QMC::AccessPublic, 0x80000000 | 100),
        // Slot 'getStrategyPerformance'
        QtMocHelpers::SlotData<QVariantMap(const QString &)>(101, 2, QMC::AccessPublic, 0x80000000 | 34, {{
            { QMetaType::QString, 32 },
        }}),
        // Slot 'resetStrategyTests'
        QtMocHelpers::SlotData<void()>(102, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'runPerformanceTest'
        QtMocHelpers::SlotData<void()>(103, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'setLoadingState'
        QtMocHelpers::SlotData<void(bool)>(104, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 105 },
        }}),
        // Slot 'setConnected'
        QtMocHelpers::SlotData<void(bool)>(106, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 107 },
        }}),
        // Slot 'setAccountBalance'
        QtMocHelpers::SlotData<void(const QString &)>(108, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 109 },
        }}),
        // Slot 'setMarketStatus'
        QtMocHelpers::SlotData<void(const QString &)>(110, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 60 },
        }}),
        // Slot 'setAutoPilotActive'
        QtMocHelpers::SlotData<void(bool)>(111, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 112 },
        }}),
        // Slot 'setSentiment'
        QtMocHelpers::SlotData<void(const QVariantMap &)>(113, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 34, 114 },
        }}),
        // Slot 'setPriceData'
        QtMocHelpers::SlotData<void(const QVariantList &)>(115, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 100, 116 },
        }}),
        // Slot 'setLaunchTime'
        QtMocHelpers::SlotData<void(double)>(117, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Double, 118 },
        }}),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'accountBalance'
        QtMocHelpers::PropertyData<QString>(119, QMetaType::QString, QMC::DefaultPropertyFlags, 0),
        // property 'accountEquity'
        QtMocHelpers::PropertyData<QString>(120, QMetaType::QString, QMC::DefaultPropertyFlags, 1),
        // property 'profitLoss'
        QtMocHelpers::PropertyData<QString>(121, QMetaType::QString, QMC::DefaultPropertyFlags, 2),
        // property 'isConnected'
        QtMocHelpers::PropertyData<bool>(122, QMetaType::Bool, QMC::DefaultPropertyFlags, 3),
        // property 'marketStatus'
        QtMocHelpers::PropertyData<QString>(123, QMetaType::QString, QMC::DefaultPropertyFlags, 4),
        // property 'userNickname'
        QtMocHelpers::PropertyData<QString>(124, QMetaType::QString, QMC::DefaultPropertyFlags, 5),
        // property 'userProfileId'
        QtMocHelpers::PropertyData<QString>(125, QMetaType::QString, QMC::DefaultPropertyFlags, 6),
        // property 'userAvatar'
        QtMocHelpers::PropertyData<QString>(126, QMetaType::QString, QMC::DefaultPropertyFlags, 7),
        // property 'userCountry'
        QtMocHelpers::PropertyData<QString>(127, QMetaType::QString, QMC::DefaultPropertyFlags, 8),
        // property 'userCountryName'
        QtMocHelpers::PropertyData<QString>(128, QMetaType::QString, QMC::DefaultPropertyFlags, 9),
        // property 'userCurrencyCode'
        QtMocHelpers::PropertyData<QString>(129, QMetaType::QString, QMC::DefaultPropertyFlags, 10),
        // property 'userCurrencySymbol'
        QtMocHelpers::PropertyData<QString>(130, QMetaType::QString, QMC::DefaultPropertyFlags, 11),
        // property 'userDemoBalance'
        QtMocHelpers::PropertyData<QString>(131, QMetaType::QString, QMC::DefaultPropertyFlags, 12),
        // property 'userLiveBalance'
        QtMocHelpers::PropertyData<QString>(132, QMetaType::QString, QMC::DefaultPropertyFlags, 13),
        // property 'userTimezone'
        QtMocHelpers::PropertyData<QString>(133, QMetaType::QString, QMC::DefaultPropertyFlags, 14),
        // property 'isAutoPilotActive'
        QtMocHelpers::PropertyData<bool>(134, QMetaType::Bool, QMC::DefaultPropertyFlags, 15),
        // property 'indicators'
        QtMocHelpers::PropertyData<QVariantList>(135, 0x80000000 | 100, QMC::DefaultPropertyFlags | QMC::EnumOrFlag, 16),
        // property 'sentiment'
        QtMocHelpers::PropertyData<QVariantMap>(114, 0x80000000 | 34, QMC::DefaultPropertyFlags | QMC::EnumOrFlag, 17),
        // property 'priceData'
        QtMocHelpers::PropertyData<QVariantList>(116, 0x80000000 | 100, QMC::DefaultPropertyFlags | QMC::EnumOrFlag, 18),
        // property 'activeOrders'
        QtMocHelpers::PropertyData<QVariantList>(136, 0x80000000 | 100, QMC::DefaultPropertyFlags | QMC::EnumOrFlag, 19),
        // property 'isLoading'
        QtMocHelpers::PropertyData<bool>(105, QMetaType::Bool, QMC::DefaultPropertyFlags, 20),
        // property 'errorMessage'
        QtMocHelpers::PropertyData<QString>(137, QMetaType::QString, QMC::DefaultPropertyFlags, 21),
        // property 'currentPhase'
        QtMocHelpers::PropertyData<int>(138, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 22),
        // property 'availableTradingPairs'
        QtMocHelpers::PropertyData<QVariantList>(139, 0x80000000 | 100, QMC::DefaultPropertyFlags | QMC::EnumOrFlag, 23),
        // property 'currentTradingPair'
        QtMocHelpers::PropertyData<QString>(140, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 24),
        // property 'strategyTestResults'
        QtMocHelpers::PropertyData<QVariantList>(141, 0x80000000 | 100, QMC::DefaultPropertyFlags | QMC::EnumOrFlag, 25),
        // property 'isStrategyTesting'
        QtMocHelpers::PropertyData<bool>(142, QMetaType::Bool, QMC::DefaultPropertyFlags, 26),
        // property 'currentTestingStrategy'
        QtMocHelpers::PropertyData<QString>(143, QMetaType::QString, QMC::DefaultPropertyFlags, 27),
        // property 'overallWinRate'
        QtMocHelpers::PropertyData<double>(37, QMetaType::Double, QMC::DefaultPropertyFlags, 28),
        // property 'phaseDescriptions'
        QtMocHelpers::PropertyData<QVariantList>(144, 0x80000000 | 100, QMC::DefaultPropertyFlags | QMC::EnumOrFlag, 32),
        // property 'autoPhaseMode'
        QtMocHelpers::PropertyData<bool>(145, QMetaType::Bool, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 33),
        // property 'phaseRepetitions'
        QtMocHelpers::PropertyData<int>(146, QMetaType::Int, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 34),
        // property 'phaseProgress'
        QtMocHelpers::PropertyData<double>(147, QMetaType::Double, QMC::DefaultPropertyFlags, 35),
        // property 'completedPhases'
        QtMocHelpers::PropertyData<int>(148, QMetaType::Int, QMC::DefaultPropertyFlags, 36),
        // property 'detectedPatterns'
        QtMocHelpers::PropertyData<QVariantList>(149, 0x80000000 | 100, QMC::DefaultPropertyFlags | QMC::EnumOrFlag, 37),
        // property 'launchTime'
        QtMocHelpers::PropertyData<double>(150, QMetaType::Double, QMC::DefaultPropertyFlags, 38),
        // property 'topNewsHeadline'
        QtMocHelpers::PropertyData<QString>(151, QMetaType::QString, QMC::DefaultPropertyFlags, 39),
        // property 'newsSentimentScore'
        QtMocHelpers::PropertyData<double>(152, QMetaType::Double, QMC::DefaultPropertyFlags, 40),
        // property 'newsSentimentText'
        QtMocHelpers::PropertyData<QString>(153, QMetaType::QString, QMC::DefaultPropertyFlags, 41),
        // property 'newsSentimentColor'
        QtMocHelpers::PropertyData<QString>(154, QMetaType::QString, QMC::DefaultPropertyFlags, 42),
        // property 'newsConfidenceLevel'
        QtMocHelpers::PropertyData<double>(155, QMetaType::Double, QMC::DefaultPropertyFlags, 43),
        // property 'newsSource'
        QtMocHelpers::PropertyData<QString>(156, QMetaType::QString, QMC::DefaultPropertyFlags, 44),
        // property 'hasActiveNews'
        QtMocHelpers::PropertyData<bool>(157, QMetaType::Bool, QMC::DefaultPropertyFlags, 45),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<QmlBridge, qt_meta_tag_ZN8Terminal9QmlBridgeE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject Terminal::QmlBridge::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN8Terminal9QmlBridgeE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN8Terminal9QmlBridgeE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN8Terminal9QmlBridgeE_t>.metaTypes,
    nullptr
} };

void Terminal::QmlBridge::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<QmlBridge *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->accountBalanceChanged(); break;
        case 1: _t->accountEquityChanged(); break;
        case 2: _t->profitLossChanged(); break;
        case 3: _t->connectionStatusChanged(); break;
        case 4: _t->marketStatusChanged(); break;
        case 5: _t->userNicknameChanged(); break;
        case 6: _t->userProfileIdChanged(); break;
        case 7: _t->userAvatarChanged(); break;
        case 8: _t->userCountryChanged(); break;
        case 9: _t->userCountryNameChanged(); break;
        case 10: _t->userCurrencyCodeChanged(); break;
        case 11: _t->userCurrencySymbolChanged(); break;
        case 12: _t->userDemoBalanceChanged(); break;
        case 13: _t->userLiveBalanceChanged(); break;
        case 14: _t->userTimezoneChanged(); break;
        case 15: _t->autoPilotStatusChanged(); break;
        case 16: _t->indicatorsChanged(); break;
        case 17: _t->sentimentChanged(); break;
        case 18: _t->priceDataChanged(); break;
        case 19: _t->activeOrdersChanged(); break;
        case 20: _t->loadingStatusChanged(); break;
        case 21: _t->errorMessageChanged(); break;
        case 22: _t->currentPhaseChanged(); break;
        case 23: _t->availableTradingPairsChanged(); break;
        case 24: _t->currentTradingPairChanged(); break;
        case 25: _t->strategyTestResultsChanged(); break;
        case 26: _t->isStrategyTestingChanged(); break;
        case 27: _t->currentTestingStrategyChanged(); break;
        case 28: _t->overallWinRateChanged(); break;
        case 29: _t->strategyTestCompleted((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<double>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QVariantMap>>(_a[3]))); break;
        case 30: _t->allStrategiesTestCompleted((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 31: _t->performanceTestCompleted((*reinterpret_cast< std::add_pointer_t<QVariantMap>>(_a[1]))); break;
        case 32: _t->phaseDescriptionsChanged(); break;
        case 33: _t->autoPhaseChanged(); break;
        case 34: _t->phaseRepetitionsChanged(); break;
        case 35: _t->phaseProgressChanged(); break;
        case 36: _t->completedPhasesChanged(); break;
        case 37: _t->detectedPatternsChanged(); break;
        case 38: _t->launchTimeChanged(); break;
        case 39: _t->topNewsHeadlineChanged(); break;
        case 40: _t->newsSentimentScoreChanged(); break;
        case 41: _t->newsSentimentTextChanged(); break;
        case 42: _t->newsSentimentColorChanged(); break;
        case 43: _t->newsConfidenceLevelChanged(); break;
        case 44: _t->newsSourceChanged(); break;
        case 45: _t->hasActiveNewsChanged(); break;
        case 46: _t->notification((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 47: _t->tradeResult((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 48: _t->connectionStatusUpdate((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<bool>>(_a[2]))); break;
        case 49: _t->connect(); break;
        case 50: _t->disconnect(); break;
        case 51: _t->placeTrade((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<double>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[3])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[4]))); break;
        case 52: _t->toggleAutoPilot((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 53: _t->showSettings(); break;
        case 54: _t->refreshData(); break;
        case 55: _t->addIndicator((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 56: _t->removeIndicator((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 57: _t->updateChartTimeframe((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 58: _t->startBot(); break;
        case 59: _t->pauseBot(); break;
        case 60: _t->stopBot(); break;
        case 61: _t->setTradingMode((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 62: _t->setBinaryOptionsBot((*reinterpret_cast< std::add_pointer_t<std::shared_ptr<BinaryOptionsBot>>>(_a[1]))); break;
        case 63: _t->releaseBotReference(); break;
        case 64: { bool _r = _t->submitPinCode((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 65: _t->detectAIPatterns(); break;
        case 66: _t->fetchRealCandlestickData((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[3]))); break;
        case 67: _t->fetchRealCandlestickData((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2]))); break;
        case 68: _t->fetchRealCandlestickData((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 69: _t->fetchRealCandlestickData(); break;
        case 70: _t->triggerPatternDetection(); break;
        case 71: { bool _r = _t->connectPyQuotexAPI();
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 72: { QString _r = _t->getBestAvailableAsset();
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        case 73: _t->updateTradingPairs(); break;
        case 74: _t->testAllStrategies(); break;
        case 75: _t->testSpecificStrategy((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 76: { QVariantList _r = _t->getAvailableStrategies();
            if (_a[0]) *reinterpret_cast< QVariantList*>(_a[0]) = std::move(_r); }  break;
        case 77: { QVariantMap _r = _t->getStrategyPerformance((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])));
            if (_a[0]) *reinterpret_cast< QVariantMap*>(_a[0]) = std::move(_r); }  break;
        case 78: _t->resetStrategyTests(); break;
        case 79: _t->runPerformanceTest(); break;
        case 80: _t->setLoadingState((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 81: _t->setConnected((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 82: _t->setAccountBalance((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 83: _t->setMarketStatus((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 84: _t->setAutoPilotActive((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 85: _t->setSentiment((*reinterpret_cast< std::add_pointer_t<QVariantMap>>(_a[1]))); break;
        case 86: _t->setPriceData((*reinterpret_cast< std::add_pointer_t<QVariantList>>(_a[1]))); break;
        case 87: _t->setLaunchTime((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::accountBalanceChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::accountEquityChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::profitLossChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::connectionStatusChanged, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::marketStatusChanged, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::userNicknameChanged, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::userProfileIdChanged, 6))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::userAvatarChanged, 7))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::userCountryChanged, 8))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::userCountryNameChanged, 9))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::userCurrencyCodeChanged, 10))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::userCurrencySymbolChanged, 11))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::userDemoBalanceChanged, 12))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::userLiveBalanceChanged, 13))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::userTimezoneChanged, 14))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::autoPilotStatusChanged, 15))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::indicatorsChanged, 16))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::sentimentChanged, 17))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::priceDataChanged, 18))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::activeOrdersChanged, 19))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::loadingStatusChanged, 20))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::errorMessageChanged, 21))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::currentPhaseChanged, 22))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::availableTradingPairsChanged, 23))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::currentTradingPairChanged, 24))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::strategyTestResultsChanged, 25))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::isStrategyTestingChanged, 26))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::currentTestingStrategyChanged, 27))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::overallWinRateChanged, 28))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)(const QString & , double , const QVariantMap & )>(_a, &QmlBridge::strategyTestCompleted, 29))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)(double )>(_a, &QmlBridge::allStrategiesTestCompleted, 30))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)(const QVariantMap & )>(_a, &QmlBridge::performanceTestCompleted, 31))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::phaseDescriptionsChanged, 32))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::autoPhaseChanged, 33))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::phaseRepetitionsChanged, 34))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::phaseProgressChanged, 35))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::completedPhasesChanged, 36))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::detectedPatternsChanged, 37))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::launchTimeChanged, 38))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::topNewsHeadlineChanged, 39))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::newsSentimentScoreChanged, 40))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::newsSentimentTextChanged, 41))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::newsSentimentColorChanged, 42))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::newsConfidenceLevelChanged, 43))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::newsSourceChanged, 44))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)()>(_a, &QmlBridge::hasActiveNewsChanged, 45))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)(const QString & , const QString & )>(_a, &QmlBridge::notification, 46))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)(bool , const QString & )>(_a, &QmlBridge::tradeResult, 47))
            return;
        if (QtMocHelpers::indexOfMethod<void (QmlBridge::*)(const QString & , bool )>(_a, &QmlBridge::connectionStatusUpdate, 48))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<QString*>(_v) = _t->accountBalance(); break;
        case 1: *reinterpret_cast<QString*>(_v) = _t->accountEquity(); break;
        case 2: *reinterpret_cast<QString*>(_v) = _t->profitLoss(); break;
        case 3: *reinterpret_cast<bool*>(_v) = _t->isConnected(); break;
        case 4: *reinterpret_cast<QString*>(_v) = _t->marketStatus(); break;
        case 5: *reinterpret_cast<QString*>(_v) = _t->userNickname(); break;
        case 6: *reinterpret_cast<QString*>(_v) = _t->userProfileId(); break;
        case 7: *reinterpret_cast<QString*>(_v) = _t->userAvatar(); break;
        case 8: *reinterpret_cast<QString*>(_v) = _t->userCountry(); break;
        case 9: *reinterpret_cast<QString*>(_v) = _t->userCountryName(); break;
        case 10: *reinterpret_cast<QString*>(_v) = _t->userCurrencyCode(); break;
        case 11: *reinterpret_cast<QString*>(_v) = _t->userCurrencySymbol(); break;
        case 12: *reinterpret_cast<QString*>(_v) = _t->userDemoBalance(); break;
        case 13: *reinterpret_cast<QString*>(_v) = _t->userLiveBalance(); break;
        case 14: *reinterpret_cast<QString*>(_v) = _t->userTimezone(); break;
        case 15: *reinterpret_cast<bool*>(_v) = _t->isAutoPilotActive(); break;
        case 16: *reinterpret_cast<QVariantList*>(_v) = _t->indicators(); break;
        case 17: *reinterpret_cast<QVariantMap*>(_v) = _t->sentiment(); break;
        case 18: *reinterpret_cast<QVariantList*>(_v) = _t->priceData(); break;
        case 19: *reinterpret_cast<QVariantList*>(_v) = _t->activeOrders(); break;
        case 20: *reinterpret_cast<bool*>(_v) = _t->isLoading(); break;
        case 21: *reinterpret_cast<QString*>(_v) = _t->errorMessage(); break;
        case 22: *reinterpret_cast<int*>(_v) = _t->currentPhase(); break;
        case 23: *reinterpret_cast<QVariantList*>(_v) = _t->availableTradingPairs(); break;
        case 24: *reinterpret_cast<QString*>(_v) = _t->currentTradingPair(); break;
        case 25: *reinterpret_cast<QVariantList*>(_v) = _t->strategyTestResults(); break;
        case 26: *reinterpret_cast<bool*>(_v) = _t->isStrategyTesting(); break;
        case 27: *reinterpret_cast<QString*>(_v) = _t->currentTestingStrategy(); break;
        case 28: *reinterpret_cast<double*>(_v) = _t->overallWinRate(); break;
        case 29: *reinterpret_cast<QVariantList*>(_v) = _t->phaseDescriptions(); break;
        case 30: *reinterpret_cast<bool*>(_v) = _t->autoPhaseMode(); break;
        case 31: *reinterpret_cast<int*>(_v) = _t->phaseRepetitions(); break;
        case 32: *reinterpret_cast<double*>(_v) = _t->phaseProgress(); break;
        case 33: *reinterpret_cast<int*>(_v) = _t->completedPhases(); break;
        case 34: *reinterpret_cast<QVariantList*>(_v) = _t->detectedPatterns(); break;
        case 35: *reinterpret_cast<double*>(_v) = _t->launchTime(); break;
        case 36: *reinterpret_cast<QString*>(_v) = _t->topNewsHeadline(); break;
        case 37: *reinterpret_cast<double*>(_v) = _t->newsSentimentScore(); break;
        case 38: *reinterpret_cast<QString*>(_v) = _t->newsSentimentText(); break;
        case 39: *reinterpret_cast<QString*>(_v) = _t->newsSentimentColor(); break;
        case 40: *reinterpret_cast<double*>(_v) = _t->newsConfidenceLevel(); break;
        case 41: *reinterpret_cast<QString*>(_v) = _t->newsSource(); break;
        case 42: *reinterpret_cast<bool*>(_v) = _t->hasActiveNews(); break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 22: _t->setCurrentPhase(*reinterpret_cast<int*>(_v)); break;
        case 24: _t->setCurrentTradingPair(*reinterpret_cast<QString*>(_v)); break;
        case 30: _t->setAutoPhaseMode(*reinterpret_cast<bool*>(_v)); break;
        case 31: _t->setPhaseRepetitions(*reinterpret_cast<int*>(_v)); break;
        default: break;
        }
    }
}

const QMetaObject *Terminal::QmlBridge::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *Terminal::QmlBridge::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN8Terminal9QmlBridgeE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int Terminal::QmlBridge::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 88)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 88;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 88)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 88;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 43;
    }
    return _id;
}

// SIGNAL 0
void Terminal::QmlBridge::accountBalanceChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void Terminal::QmlBridge::accountEquityChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void Terminal::QmlBridge::profitLossChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void Terminal::QmlBridge::connectionStatusChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void Terminal::QmlBridge::marketStatusChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}

// SIGNAL 5
void Terminal::QmlBridge::userNicknameChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 5, nullptr);
}

// SIGNAL 6
void Terminal::QmlBridge::userProfileIdChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 6, nullptr);
}

// SIGNAL 7
void Terminal::QmlBridge::userAvatarChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 7, nullptr);
}

// SIGNAL 8
void Terminal::QmlBridge::userCountryChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 8, nullptr);
}

// SIGNAL 9
void Terminal::QmlBridge::userCountryNameChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 9, nullptr);
}

// SIGNAL 10
void Terminal::QmlBridge::userCurrencyCodeChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 10, nullptr);
}

// SIGNAL 11
void Terminal::QmlBridge::userCurrencySymbolChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 11, nullptr);
}

// SIGNAL 12
void Terminal::QmlBridge::userDemoBalanceChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 12, nullptr);
}

// SIGNAL 13
void Terminal::QmlBridge::userLiveBalanceChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 13, nullptr);
}

// SIGNAL 14
void Terminal::QmlBridge::userTimezoneChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 14, nullptr);
}

// SIGNAL 15
void Terminal::QmlBridge::autoPilotStatusChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 15, nullptr);
}

// SIGNAL 16
void Terminal::QmlBridge::indicatorsChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 16, nullptr);
}

// SIGNAL 17
void Terminal::QmlBridge::sentimentChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 17, nullptr);
}

// SIGNAL 18
void Terminal::QmlBridge::priceDataChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 18, nullptr);
}

// SIGNAL 19
void Terminal::QmlBridge::activeOrdersChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 19, nullptr);
}

// SIGNAL 20
void Terminal::QmlBridge::loadingStatusChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 20, nullptr);
}

// SIGNAL 21
void Terminal::QmlBridge::errorMessageChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 21, nullptr);
}

// SIGNAL 22
void Terminal::QmlBridge::currentPhaseChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 22, nullptr);
}

// SIGNAL 23
void Terminal::QmlBridge::availableTradingPairsChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 23, nullptr);
}

// SIGNAL 24
void Terminal::QmlBridge::currentTradingPairChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 24, nullptr);
}

// SIGNAL 25
void Terminal::QmlBridge::strategyTestResultsChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 25, nullptr);
}

// SIGNAL 26
void Terminal::QmlBridge::isStrategyTestingChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 26, nullptr);
}

// SIGNAL 27
void Terminal::QmlBridge::currentTestingStrategyChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 27, nullptr);
}

// SIGNAL 28
void Terminal::QmlBridge::overallWinRateChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 28, nullptr);
}

// SIGNAL 29
void Terminal::QmlBridge::strategyTestCompleted(const QString & _t1, double _t2, const QVariantMap & _t3)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 29, nullptr, _t1, _t2, _t3);
}

// SIGNAL 30
void Terminal::QmlBridge::allStrategiesTestCompleted(double _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 30, nullptr, _t1);
}

// SIGNAL 31
void Terminal::QmlBridge::performanceTestCompleted(const QVariantMap & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 31, nullptr, _t1);
}

// SIGNAL 32
void Terminal::QmlBridge::phaseDescriptionsChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 32, nullptr);
}

// SIGNAL 33
void Terminal::QmlBridge::autoPhaseChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 33, nullptr);
}

// SIGNAL 34
void Terminal::QmlBridge::phaseRepetitionsChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 34, nullptr);
}

// SIGNAL 35
void Terminal::QmlBridge::phaseProgressChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 35, nullptr);
}

// SIGNAL 36
void Terminal::QmlBridge::completedPhasesChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 36, nullptr);
}

// SIGNAL 37
void Terminal::QmlBridge::detectedPatternsChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 37, nullptr);
}

// SIGNAL 38
void Terminal::QmlBridge::launchTimeChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 38, nullptr);
}

// SIGNAL 39
void Terminal::QmlBridge::topNewsHeadlineChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 39, nullptr);
}

// SIGNAL 40
void Terminal::QmlBridge::newsSentimentScoreChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 40, nullptr);
}

// SIGNAL 41
void Terminal::QmlBridge::newsSentimentTextChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 41, nullptr);
}

// SIGNAL 42
void Terminal::QmlBridge::newsSentimentColorChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 42, nullptr);
}

// SIGNAL 43
void Terminal::QmlBridge::newsConfidenceLevelChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 43, nullptr);
}

// SIGNAL 44
void Terminal::QmlBridge::newsSourceChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 44, nullptr);
}

// SIGNAL 45
void Terminal::QmlBridge::hasActiveNewsChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 45, nullptr);
}

// SIGNAL 46
void Terminal::QmlBridge::notification(const QString & _t1, const QString & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 46, nullptr, _t1, _t2);
}

// SIGNAL 47
void Terminal::QmlBridge::tradeResult(bool _t1, const QString & _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 47, nullptr, _t1, _t2);
}

// SIGNAL 48
void Terminal::QmlBridge::connectionStatusUpdate(const QString & _t1, bool _t2)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 48, nullptr, _t1, _t2);
}
QT_WARNING_POP

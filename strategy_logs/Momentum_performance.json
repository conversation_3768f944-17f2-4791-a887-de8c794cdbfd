[{"description": "Momentum-based trading strategy", "final_balance": 1608.5, "is_real_strategy": true, "losing_trades": 59, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 20.283333333333335, "strategy_name": "Momentum", "test_date": "2025-05-26T13:02:26", "timestamp": "2025-05-26T13:02:26", "total_profit": 608.5, "total_trades": 200, "win_rate": 70.5, "winning_trades": 141}, {"description": "Momentum-based trading strategy", "final_balance": 1608.5, "is_real_strategy": true, "losing_trades": 59, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 20.283333333333335, "strategy_name": "Momentum", "test_date": "2025-05-26T13:02:26", "timestamp": "2025-05-26T13:02:28", "total_profit": 608.5, "total_trades": 200, "win_rate": 70.5, "winning_trades": 141}, {"description": "Momentum-based trading strategy", "final_balance": 1516, "is_real_strategy": true, "losing_trades": 64, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 10.32, "strategy_name": "Momentum", "test_date": "2025-05-26T13:06:02", "timestamp": "2025-05-26T13:06:02", "total_profit": 516, "total_trades": 200, "win_rate": 68, "winning_trades": 136}, {"description": "Momentum-based trading strategy", "final_balance": 1516, "is_real_strategy": true, "losing_trades": 64, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 10.32, "strategy_name": "Momentum", "test_date": "2025-05-26T13:06:02", "timestamp": "2025-05-26T13:06:06", "total_profit": 516, "total_trades": 200, "win_rate": 68, "winning_trades": 136}, {"description": "Momentum-based trading strategy", "final_balance": 1571.5, "is_real_strategy": true, "losing_trades": 61, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 19.05, "strategy_name": "Momentum", "test_date": "2025-05-26T13:07:34", "timestamp": "2025-05-26T13:07:34", "total_profit": 571.5, "total_trades": 200, "win_rate": 69.5, "winning_trades": 139}, {"description": "Momentum-based trading strategy", "final_balance": 1571.5, "is_real_strategy": true, "losing_trades": 61, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 19.05, "strategy_name": "Momentum", "test_date": "2025-05-26T13:07:34", "timestamp": "2025-05-26T13:07:38", "total_profit": 571.5, "total_trades": 200, "win_rate": 69.5, "winning_trades": 139}, {"description": "Momentum-based trading strategy", "final_balance": 1627, "is_real_strategy": true, "losing_trades": 58, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 12.54, "strategy_name": "Momentum", "test_date": "2025-05-26T13:11:30", "timestamp": "2025-05-26T13:11:30", "total_profit": 627, "total_trades": 200, "win_rate": 71, "winning_trades": 142}, {"description": "Momentum-based trading strategy", "final_balance": 1627, "is_real_strategy": true, "losing_trades": 58, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 12.54, "strategy_name": "Momentum", "test_date": "2025-05-26T13:11:30", "timestamp": "2025-05-26T13:11:32", "total_profit": 627, "total_trades": 200, "win_rate": 71, "winning_trades": 142}, {"description": "Momentum-based trading strategy", "final_balance": 1553, "is_real_strategy": true, "losing_trades": 62, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 13.825, "strategy_name": "Momentum", "test_date": "2025-05-26T13:14:03", "timestamp": "2025-05-26T13:14:03", "total_profit": 553, "total_trades": 200, "win_rate": 69, "winning_trades": 138}, {"description": "Momentum-based trading strategy", "final_balance": 1553, "is_real_strategy": true, "losing_trades": 62, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 13.825, "strategy_name": "Momentum", "test_date": "2025-05-26T13:14:03", "timestamp": "2025-05-26T13:14:07", "total_profit": 553, "total_trades": 200, "win_rate": 69, "winning_trades": 138}, {"description": "Momentum-based trading strategy", "final_balance": 1553, "is_real_strategy": true, "losing_trades": 62, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 13.825, "strategy_name": "Momentum", "test_date": "2025-05-26T13:15:44", "timestamp": "2025-05-26T13:15:44", "total_profit": 553, "total_trades": 200, "win_rate": 69, "winning_trades": 138}, {"description": "Momentum-based trading strategy", "final_balance": 1553, "is_real_strategy": true, "losing_trades": 62, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 13.825, "strategy_name": "Momentum", "test_date": "2025-05-26T13:15:44", "timestamp": "2025-05-26T13:15:46", "total_profit": 553, "total_trades": 200, "win_rate": 69, "winning_trades": 138}, {"description": "Momentum-based trading strategy", "final_balance": 1386.5, "is_real_strategy": true, "losing_trades": 71, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 6.441666666666666, "strategy_name": "Momentum", "test_date": "2025-05-26T13:30:33", "timestamp": "2025-05-26T13:30:33", "total_profit": 386.5, "total_trades": 200, "win_rate": 64.5, "winning_trades": 129}, {"description": "Momentum-based trading strategy", "final_balance": 1386.5, "is_real_strategy": true, "losing_trades": 71, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 6.441666666666666, "strategy_name": "Momentum", "test_date": "2025-05-26T13:30:33", "timestamp": "2025-05-26T13:30:35", "total_profit": 386.5, "total_trades": 200, "win_rate": 64.5, "winning_trades": 129}, {"description": "Momentum-based trading strategy", "final_balance": 1312.5, "is_real_strategy": true, "losing_trades": 75, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 6.25, "strategy_name": "Momentum", "test_date": "2025-05-26T13:30:57", "timestamp": "2025-05-26T13:30:57", "total_profit": 312.5, "total_trades": 200, "win_rate": 62.5, "winning_trades": 125}, {"description": "Momentum-based trading strategy", "final_balance": 1312.5, "is_real_strategy": true, "losing_trades": 75, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 6.25, "strategy_name": "Momentum", "test_date": "2025-05-26T13:30:57", "timestamp": "2025-05-26T13:30:59", "total_profit": 312.5, "total_trades": 200, "win_rate": 62.5, "winning_trades": 125}, {"description": "Momentum-based trading strategy", "final_balance": 1571.5, "is_real_strategy": true, "losing_trades": 61, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 11.43, "strategy_name": "Momentum", "test_date": "2025-05-26T13:41:23", "timestamp": "2025-05-26T13:41:23", "total_profit": 571.5, "total_trades": 200, "win_rate": 69.5, "winning_trades": 139}, {"description": "Momentum-based trading strategy", "final_balance": 1571.5, "is_real_strategy": true, "losing_trades": 61, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 11.43, "strategy_name": "Momentum", "test_date": "2025-05-26T13:41:23", "timestamp": "2025-05-26T13:41:25", "total_profit": 571.5, "total_trades": 200, "win_rate": 69.5, "winning_trades": 139}, {"description": "Momentum-based trading strategy", "final_balance": 1608.5, "is_real_strategy": true, "losing_trades": 59, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 20.283333333333335, "strategy_name": "Momentum", "test_date": "2025-05-27T10:30:17", "timestamp": "2025-05-27T10:30:17", "total_profit": 608.5, "total_trades": 200, "win_rate": 70.5, "winning_trades": 141}, {"description": "Momentum-based trading strategy", "final_balance": 1608.5, "is_real_strategy": true, "losing_trades": 59, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 20.283333333333335, "strategy_name": "Momentum", "test_date": "2025-05-27T10:30:17", "timestamp": "2025-05-27T10:30:19", "total_profit": 608.5, "total_trades": 200, "win_rate": 70.5, "winning_trades": 141}, {"description": "Momentum-based trading strategy", "final_balance": 1423.5, "is_real_strategy": true, "losing_trades": 69, "max_drawdown": 70, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 6.05, "strategy_name": "Momentum", "test_date": "2025-05-27T11:12:57", "timestamp": "2025-05-27T11:12:57", "total_profit": 423.5, "total_trades": 200, "win_rate": 65.5, "winning_trades": 131}, {"description": "Momentum-based trading strategy", "final_balance": 1423.5, "is_real_strategy": true, "losing_trades": 69, "max_drawdown": 70, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 6.05, "strategy_name": "Momentum", "test_date": "2025-05-27T11:12:57", "timestamp": "2025-05-27T11:12:59", "total_profit": 423.5, "total_trades": 200, "win_rate": 65.5, "winning_trades": 131}, {"description": "Momentum-based trading strategy", "final_balance": 1553, "is_real_strategy": true, "losing_trades": 62, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 11.06, "strategy_name": "Momentum", "test_date": "2025-05-27T11:25:33", "timestamp": "2025-05-27T11:25:33", "total_profit": 553, "total_trades": 200, "win_rate": 69, "winning_trades": 138}, {"description": "Momentum-based trading strategy", "final_balance": 1553, "is_real_strategy": true, "losing_trades": 62, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 11.06, "strategy_name": "Momentum", "test_date": "2025-05-27T11:25:33", "timestamp": "2025-05-27T11:25:35", "total_profit": 553, "total_trades": 200, "win_rate": 69, "winning_trades": 138}, {"description": "Momentum-based trading strategy", "final_balance": 1590, "is_real_strategy": true, "losing_trades": 60, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 19.666666666666668, "strategy_name": "Momentum", "test_date": "2025-05-27T12:00:29", "timestamp": "2025-05-27T12:00:29", "total_profit": 590, "total_trades": 200, "win_rate": 70, "winning_trades": 140}, {"description": "Momentum-based trading strategy", "final_balance": 1590, "is_real_strategy": true, "losing_trades": 60, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 19.666666666666668, "strategy_name": "Momentum", "test_date": "2025-05-27T12:00:29", "timestamp": "2025-05-27T12:00:31", "total_profit": 590, "total_trades": 200, "win_rate": 70, "winning_trades": 140}, {"description": "Momentum-based trading strategy", "final_balance": 1664, "is_real_strategy": true, "losing_trades": 56, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 16.6, "strategy_name": "Momentum", "test_date": "2025-05-27T12:39:02", "timestamp": "2025-05-27T12:39:02", "total_profit": 664, "total_trades": 200, "win_rate": 72, "winning_trades": 144}, {"description": "Momentum-based trading strategy", "final_balance": 1664, "is_real_strategy": true, "losing_trades": 56, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 16.6, "strategy_name": "Momentum", "test_date": "2025-05-27T12:39:02", "timestamp": "2025-05-27T12:39:04", "total_profit": 664, "total_trades": 200, "win_rate": 72, "winning_trades": 144}, {"description": "Momentum-based trading strategy", "final_balance": 1682.5, "is_real_strategy": true, "losing_trades": 55, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 13.65, "strategy_name": "Momentum", "test_date": "2025-05-27T12:41:00", "timestamp": "2025-05-27T12:41:00", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5, "winning_trades": 145}, {"description": "Momentum-based trading strategy", "final_balance": 1682.5, "is_real_strategy": true, "losing_trades": 55, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 13.65, "strategy_name": "Momentum", "test_date": "2025-05-27T12:41:00", "timestamp": "2025-05-27T12:41:02", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5, "winning_trades": 145}, {"description": "Momentum-based trading strategy", "final_balance": 1423.5, "is_real_strategy": true, "losing_trades": 69, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 8.47, "strategy_name": "Momentum", "test_date": "2025-05-29T15:58:19", "timestamp": "2025-05-29T15:58:19", "total_profit": 423.5, "total_trades": 200, "win_rate": 65.5, "winning_trades": 131}, {"description": "Momentum-based trading strategy", "final_balance": 1423.5, "is_real_strategy": true, "losing_trades": 69, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 8.47, "strategy_name": "Momentum", "test_date": "2025-05-29T15:58:19", "timestamp": "2025-05-29T15:58:21", "total_profit": 423.5, "total_trades": 200, "win_rate": 65.5, "winning_trades": 131}, {"description": "Momentum-based trading strategy", "final_balance": 1497.5, "is_real_strategy": true, "losing_trades": 65, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 9.95, "strategy_name": "Momentum", "test_date": "2025-05-29T15:58:56", "timestamp": "2025-05-29T15:58:56", "total_profit": 497.5, "total_trades": 200, "win_rate": 67.5, "winning_trades": 135}, {"description": "Momentum-based trading strategy", "final_balance": 1497.5, "is_real_strategy": true, "losing_trades": 65, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 9.95, "strategy_name": "Momentum", "test_date": "2025-05-29T15:58:56", "timestamp": "2025-05-29T15:58:58", "total_profit": 497.5, "total_trades": 200, "win_rate": 67.5, "winning_trades": 135}]
[{"description": "Advanced multi-factor strategy", "final_balance": 1423.5, "is_real_strategy": true, "losing_trades": 69, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 8.47, "strategy_name": "Advanced Strategy", "test_date": "2025-05-26T13:02:26", "timestamp": "2025-05-26T13:02:26", "total_profit": 423.5, "total_trades": 200, "win_rate": 65.5, "winning_trades": 131}, {"description": "Advanced multi-factor strategy", "final_balance": 1423.5, "is_real_strategy": true, "losing_trades": 69, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 8.47, "strategy_name": "Advanced Strategy", "test_date": "2025-05-26T13:02:26", "timestamp": "2025-05-26T13:02:28", "total_profit": 423.5, "total_trades": 200, "win_rate": 65.5, "winning_trades": 131}, {"description": "Advanced multi-factor strategy", "final_balance": 1664, "is_real_strategy": true, "losing_trades": 56, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 22.133333333333333, "strategy_name": "Advanced Strategy", "test_date": "2025-05-26T13:06:03", "timestamp": "2025-05-26T13:06:03", "total_profit": 664, "total_trades": 200, "win_rate": 72, "winning_trades": 144}, {"description": "Advanced multi-factor strategy", "final_balance": 1664, "is_real_strategy": true, "losing_trades": 56, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 22.133333333333333, "strategy_name": "Advanced Strategy", "test_date": "2025-05-26T13:06:03", "timestamp": "2025-05-26T13:06:06", "total_profit": 664, "total_trades": 200, "win_rate": 72, "winning_trades": 144}, {"description": "Advanced multi-factor strategy", "final_balance": 1497.5, "is_real_strategy": true, "losing_trades": 65, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 12.4375, "strategy_name": "Advanced Strategy", "test_date": "2025-05-26T13:07:34", "timestamp": "2025-05-26T13:07:34", "total_profit": 497.5, "total_trades": 200, "win_rate": 67.5, "winning_trades": 135}, {"description": "Advanced multi-factor strategy", "final_balance": 1497.5, "is_real_strategy": true, "losing_trades": 65, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 12.4375, "strategy_name": "Advanced Strategy", "test_date": "2025-05-26T13:07:34", "timestamp": "2025-05-26T13:07:38", "total_profit": 497.5, "total_trades": 200, "win_rate": 67.5, "winning_trades": 135}, {"description": "Advanced multi-factor strategy", "final_balance": 1719.5, "is_real_strategy": true, "losing_trades": 53, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 14.39, "strategy_name": "Advanced Strategy", "test_date": "2025-05-26T13:11:30", "timestamp": "2025-05-26T13:11:30", "total_profit": 719.5, "total_trades": 200, "win_rate": 73.5, "winning_trades": 147}, {"description": "Advanced multi-factor strategy", "final_balance": 1719.5, "is_real_strategy": true, "losing_trades": 53, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 14.39, "strategy_name": "Advanced Strategy", "test_date": "2025-05-26T13:11:30", "timestamp": "2025-05-26T13:11:32", "total_profit": 719.5, "total_trades": 200, "win_rate": 73.5, "winning_trades": 147}, {"description": "Advanced multi-factor strategy", "final_balance": 1553, "is_real_strategy": true, "losing_trades": 62, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 11.06, "strategy_name": "Advanced Strategy", "test_date": "2025-05-26T13:14:03", "timestamp": "2025-05-26T13:14:03", "total_profit": 553, "total_trades": 200, "win_rate": 69, "winning_trades": 138}, {"description": "Advanced multi-factor strategy", "final_balance": 1553, "is_real_strategy": true, "losing_trades": 62, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 11.06, "strategy_name": "Advanced Strategy", "test_date": "2025-05-26T13:14:03", "timestamp": "2025-05-26T13:14:07", "total_profit": 553, "total_trades": 200, "win_rate": 69, "winning_trades": 138}, {"description": "Advanced multi-factor strategy", "final_balance": 1146, "is_real_strategy": true, "losing_trades": 84, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 2.433333333333333, "strategy_name": "Advanced Strategy", "test_date": "2025-05-26T13:15:45", "timestamp": "2025-05-26T13:15:45", "total_profit": 146, "total_trades": 200, "win_rate": 57.99999999999999, "winning_trades": 116}, {"description": "Advanced multi-factor strategy", "final_balance": 1146, "is_real_strategy": true, "losing_trades": 84, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 2.433333333333333, "strategy_name": "Advanced Strategy", "test_date": "2025-05-26T13:15:45", "timestamp": "2025-05-26T13:15:46", "total_profit": 146, "total_trades": 200, "win_rate": 57.99999999999999, "winning_trades": 116}, {"description": "Advanced multi-factor strategy", "final_balance": 1682.5, "is_real_strategy": true, "losing_trades": 55, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 13.65, "strategy_name": "Advanced Strategy", "test_date": "2025-05-26T13:30:34", "timestamp": "2025-05-26T13:30:34", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5, "winning_trades": 145}, {"description": "Advanced multi-factor strategy", "final_balance": 1682.5, "is_real_strategy": true, "losing_trades": 55, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 13.65, "strategy_name": "Advanced Strategy", "test_date": "2025-05-26T13:30:34", "timestamp": "2025-05-26T13:30:35", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5, "winning_trades": 145}, {"description": "Advanced multi-factor strategy", "final_balance": 1460.5, "is_real_strategy": true, "losing_trades": 67, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 11.5125, "strategy_name": "Advanced Strategy", "test_date": "2025-05-26T13:30:58", "timestamp": "2025-05-26T13:30:58", "total_profit": 460.5, "total_trades": 200, "win_rate": 66.5, "winning_trades": 133}, {"description": "Advanced multi-factor strategy", "final_balance": 1460.5, "is_real_strategy": true, "losing_trades": 67, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 11.5125, "strategy_name": "Advanced Strategy", "test_date": "2025-05-26T13:30:58", "timestamp": "2025-05-26T13:30:59", "total_profit": 460.5, "total_trades": 200, "win_rate": 66.5, "winning_trades": 133}, {"description": "Advanced multi-factor strategy", "final_balance": 1553, "is_real_strategy": true, "losing_trades": 62, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 18.433333333333334, "strategy_name": "Advanced Strategy", "test_date": "2025-05-26T13:41:24", "timestamp": "2025-05-26T13:41:24", "total_profit": 553, "total_trades": 200, "win_rate": 69, "winning_trades": 138}, {"description": "Advanced multi-factor strategy", "final_balance": 1553, "is_real_strategy": true, "losing_trades": 62, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 18.433333333333334, "strategy_name": "Advanced Strategy", "test_date": "2025-05-26T13:41:24", "timestamp": "2025-05-26T13:41:25", "total_profit": 553, "total_trades": 200, "win_rate": 69, "winning_trades": 138}, {"description": "Advanced multi-factor strategy", "final_balance": 1553, "is_real_strategy": true, "losing_trades": 62, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 13.825, "strategy_name": "Advanced Strategy", "test_date": "2025-05-27T10:30:18", "timestamp": "2025-05-27T10:30:18", "total_profit": 553, "total_trades": 200, "win_rate": 69, "winning_trades": 138}, {"description": "Advanced multi-factor strategy", "final_balance": 1553, "is_real_strategy": true, "losing_trades": 62, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 13.825, "strategy_name": "Advanced Strategy", "test_date": "2025-05-27T10:30:18", "timestamp": "2025-05-27T10:30:19", "total_profit": 553, "total_trades": 200, "win_rate": 69, "winning_trades": 138}, {"description": "Advanced multi-factor strategy", "final_balance": 1534.5, "is_real_strategy": true, "losing_trades": 63, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 13.3625, "strategy_name": "Advanced Strategy", "test_date": "2025-05-27T11:12:58", "timestamp": "2025-05-27T11:12:58", "total_profit": 534.5, "total_trades": 200, "win_rate": 68.5, "winning_trades": 137}, {"description": "Advanced multi-factor strategy", "final_balance": 1534.5, "is_real_strategy": true, "losing_trades": 63, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 13.3625, "strategy_name": "Advanced Strategy", "test_date": "2025-05-27T11:12:58", "timestamp": "2025-05-27T11:12:59", "total_profit": 534.5, "total_trades": 200, "win_rate": 68.5, "winning_trades": 137}, {"description": "Advanced multi-factor strategy", "final_balance": 1516, "is_real_strategy": true, "losing_trades": 64, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 12.9, "strategy_name": "Advanced Strategy", "test_date": "2025-05-27T11:25:33", "timestamp": "2025-05-27T11:25:33", "total_profit": 516, "total_trades": 200, "win_rate": 68, "winning_trades": 136}, {"description": "Advanced multi-factor strategy", "final_balance": 1516, "is_real_strategy": true, "losing_trades": 64, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 12.9, "strategy_name": "Advanced Strategy", "test_date": "2025-05-27T11:25:33", "timestamp": "2025-05-27T11:25:35", "total_profit": 516, "total_trades": 200, "win_rate": 68, "winning_trades": 136}, {"description": "Advanced multi-factor strategy", "final_balance": 1627, "is_real_strategy": true, "losing_trades": 58, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 12.54, "strategy_name": "Advanced Strategy", "test_date": "2025-05-27T12:00:30", "timestamp": "2025-05-27T12:00:30", "total_profit": 627, "total_trades": 200, "win_rate": 71, "winning_trades": 142}, {"description": "Advanced multi-factor strategy", "final_balance": 1627, "is_real_strategy": true, "losing_trades": 58, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 12.54, "strategy_name": "Advanced Strategy", "test_date": "2025-05-27T12:00:30", "timestamp": "2025-05-27T12:00:31", "total_profit": 627, "total_trades": 200, "win_rate": 71, "winning_trades": 142}, {"description": "Advanced multi-factor strategy", "final_balance": 1534.5, "is_real_strategy": true, "losing_trades": 63, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 13.3625, "strategy_name": "Advanced Strategy", "test_date": "2025-05-27T12:39:02", "timestamp": "2025-05-27T12:39:02", "total_profit": 534.5, "total_trades": 200, "win_rate": 68.5, "winning_trades": 137}, {"description": "Advanced multi-factor strategy", "final_balance": 1534.5, "is_real_strategy": true, "losing_trades": 63, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 13.3625, "strategy_name": "Advanced Strategy", "test_date": "2025-05-27T12:39:02", "timestamp": "2025-05-27T12:39:04", "total_profit": 534.5, "total_trades": 200, "win_rate": 68.5, "winning_trades": 137}, {"description": "Advanced multi-factor strategy", "final_balance": 1701, "is_real_strategy": true, "losing_trades": 54, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 17.525, "strategy_name": "Advanced Strategy", "test_date": "2025-05-27T12:41:01", "timestamp": "2025-05-27T12:41:01", "total_profit": 701, "total_trades": 200, "win_rate": 73, "winning_trades": 146}, {"description": "Advanced multi-factor strategy", "final_balance": 1701, "is_real_strategy": true, "losing_trades": 54, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 17.525, "strategy_name": "Advanced Strategy", "test_date": "2025-05-27T12:41:01", "timestamp": "2025-05-27T12:41:02", "total_profit": 701, "total_trades": 200, "win_rate": 73, "winning_trades": 146}, {"description": "Advanced multi-factor strategy", "final_balance": 1590, "is_real_strategy": true, "losing_trades": 60, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 14.75, "strategy_name": "Advanced Strategy", "test_date": "2025-05-29T15:58:20", "timestamp": "2025-05-29T15:58:20", "total_profit": 590, "total_trades": 200, "win_rate": 70, "winning_trades": 140}, {"description": "Advanced multi-factor strategy", "final_balance": 1590, "is_real_strategy": true, "losing_trades": 60, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 14.75, "strategy_name": "Advanced Strategy", "test_date": "2025-05-29T15:58:20", "timestamp": "2025-05-29T15:58:21", "total_profit": 590, "total_trades": 200, "win_rate": 70, "winning_trades": 140}, {"description": "Advanced multi-factor strategy", "final_balance": 1627, "is_real_strategy": true, "losing_trades": 58, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 15.675, "strategy_name": "Advanced Strategy", "test_date": "2025-05-29T15:58:57", "timestamp": "2025-05-29T15:58:57", "total_profit": 627, "total_trades": 200, "win_rate": 71, "winning_trades": 142}, {"description": "Advanced multi-factor strategy", "final_balance": 1627, "is_real_strategy": true, "losing_trades": 58, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 15.675, "strategy_name": "Advanced Strategy", "test_date": "2025-05-29T15:58:57", "timestamp": "2025-05-29T15:58:58", "total_profit": 627, "total_trades": 200, "win_rate": 71, "winning_trades": 142}]
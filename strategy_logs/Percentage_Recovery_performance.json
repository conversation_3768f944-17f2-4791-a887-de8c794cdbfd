[{"description": "Percentage-based recovery strategy", "final_balance": 1035, "is_real_strategy": true, "losing_trades": 90, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 0.875, "strategy_name": "Percentage Recovery", "test_date": "2025-05-26T13:02:22", "timestamp": "2025-05-26T13:02:22", "total_profit": 35, "total_trades": 200, "win_rate": 55.00000000000001, "winning_trades": 110}, {"description": "Percentage-based recovery strategy", "final_balance": 1035, "is_real_strategy": true, "losing_trades": 90, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 0.875, "strategy_name": "Percentage Recovery", "test_date": "2025-05-26T13:02:22", "timestamp": "2025-05-26T13:02:28", "total_profit": 35, "total_trades": 200, "win_rate": 55.00000000000001, "winning_trades": 110}, {"description": "Percentage-based recovery strategy", "final_balance": 1368, "is_real_strategy": true, "losing_trades": 72, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 7.36, "strategy_name": "Percentage Recovery", "test_date": "2025-05-26T13:06:00", "timestamp": "2025-05-26T13:06:00", "total_profit": 368, "total_trades": 200, "win_rate": 64, "winning_trades": 128}, {"description": "Percentage-based recovery strategy", "final_balance": 1368, "is_real_strategy": true, "losing_trades": 72, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 7.36, "strategy_name": "Percentage Recovery", "test_date": "2025-05-26T13:06:00", "timestamp": "2025-05-26T13:06:06", "total_profit": 368, "total_trades": 200, "win_rate": 64, "winning_trades": 128}, {"description": "Percentage-based recovery strategy", "final_balance": 1460.5, "is_real_strategy": true, "losing_trades": 67, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 9.21, "strategy_name": "Percentage Recovery", "test_date": "2025-05-26T13:07:31", "timestamp": "2025-05-26T13:07:31", "total_profit": 460.5, "total_trades": 200, "win_rate": 66.5, "winning_trades": 133}, {"description": "Percentage-based recovery strategy", "final_balance": 1460.5, "is_real_strategy": true, "losing_trades": 67, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 9.21, "strategy_name": "Percentage Recovery", "test_date": "2025-05-26T13:07:31", "timestamp": "2025-05-26T13:07:38", "total_profit": 460.5, "total_trades": 200, "win_rate": 66.5, "winning_trades": 133}, {"description": "Percentage-based recovery strategy", "final_balance": 1183, "is_real_strategy": true, "losing_trades": 82, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 3.05, "strategy_name": "Percentage Recovery", "test_date": "2025-05-26T13:11:26", "timestamp": "2025-05-26T13:11:26", "total_profit": 183, "total_trades": 200, "win_rate": 59, "winning_trades": 118}, {"description": "Percentage-based recovery strategy", "final_balance": 1183, "is_real_strategy": true, "losing_trades": 82, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 3.05, "strategy_name": "Percentage Recovery", "test_date": "2025-05-26T13:11:26", "timestamp": "2025-05-26T13:11:32", "total_profit": 183, "total_trades": 200, "win_rate": 59, "winning_trades": 118}, {"description": "Percentage-based recovery strategy", "final_balance": 1497.5, "is_real_strategy": true, "losing_trades": 65, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 12.4375, "strategy_name": "Percentage Recovery", "test_date": "2025-05-26T13:14:01", "timestamp": "2025-05-26T13:14:01", "total_profit": 497.5, "total_trades": 200, "win_rate": 67.5, "winning_trades": 135}, {"description": "Percentage-based recovery strategy", "final_balance": 1497.5, "is_real_strategy": true, "losing_trades": 65, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 12.4375, "strategy_name": "Percentage Recovery", "test_date": "2025-05-26T13:14:01", "timestamp": "2025-05-26T13:14:07", "total_profit": 497.5, "total_trades": 200, "win_rate": 67.5, "winning_trades": 135}, {"description": "Percentage-based recovery strategy", "final_balance": 1423.5, "is_real_strategy": true, "losing_trades": 69, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 10.5875, "strategy_name": "Percentage Recovery", "test_date": "2025-05-26T13:15:42", "timestamp": "2025-05-26T13:15:42", "total_profit": 423.5, "total_trades": 200, "win_rate": 65.5, "winning_trades": 131}, {"description": "Percentage-based recovery strategy", "final_balance": 1423.5, "is_real_strategy": true, "losing_trades": 69, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 10.5875, "strategy_name": "Percentage Recovery", "test_date": "2025-05-26T13:15:42", "timestamp": "2025-05-26T13:15:46", "total_profit": 423.5, "total_trades": 200, "win_rate": 65.5, "winning_trades": 131}, {"description": "Percentage-based recovery strategy", "final_balance": 1294, "is_real_strategy": true, "losing_trades": 76, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 4.9, "strategy_name": "Percentage Recovery", "test_date": "2025-05-26T13:30:29", "timestamp": "2025-05-26T13:30:29", "total_profit": 294, "total_trades": 200, "win_rate": 62, "winning_trades": 124}, {"description": "Percentage-based recovery strategy", "final_balance": 1294, "is_real_strategy": true, "losing_trades": 76, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 4.9, "strategy_name": "Percentage Recovery", "test_date": "2025-05-26T13:30:29", "timestamp": "2025-05-26T13:30:35", "total_profit": 294, "total_trades": 200, "win_rate": 62, "winning_trades": 124}, {"description": "Percentage-based recovery strategy", "final_balance": 1534.5, "is_real_strategy": true, "losing_trades": 63, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 8.908333333333333, "strategy_name": "Percentage Recovery", "test_date": "2025-05-26T13:30:55", "timestamp": "2025-05-26T13:30:55", "total_profit": 534.5, "total_trades": 200, "win_rate": 68.5, "winning_trades": 137}, {"description": "Percentage-based recovery strategy", "final_balance": 1534.5, "is_real_strategy": true, "losing_trades": 63, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 8.908333333333333, "strategy_name": "Percentage Recovery", "test_date": "2025-05-26T13:30:55", "timestamp": "2025-05-26T13:30:59", "total_profit": 534.5, "total_trades": 200, "win_rate": 68.5, "winning_trades": 137}, {"description": "Percentage-based recovery strategy", "final_balance": 1442, "is_real_strategy": true, "losing_trades": 68, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 8.84, "strategy_name": "Percentage Recovery", "test_date": "2025-05-26T13:41:21", "timestamp": "2025-05-26T13:41:21", "total_profit": 442, "total_trades": 200, "win_rate": 66, "winning_trades": 132}, {"description": "Percentage-based recovery strategy", "final_balance": 1442, "is_real_strategy": true, "losing_trades": 68, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 8.84, "strategy_name": "Percentage Recovery", "test_date": "2025-05-26T13:41:21", "timestamp": "2025-05-26T13:41:25", "total_profit": 442, "total_trades": 200, "win_rate": 66, "winning_trades": 132}, {"description": "Percentage-based recovery strategy", "final_balance": 1257, "is_real_strategy": true, "losing_trades": 78, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 4.283333333333333, "strategy_name": "Percentage Recovery", "test_date": "2025-05-27T10:05:29", "timestamp": "2025-05-27T10:05:29", "total_profit": 257, "total_trades": 200, "win_rate": 61, "winning_trades": 122}, {"description": "Percentage-based recovery strategy", "final_balance": 1405, "is_real_strategy": true, "losing_trades": 70, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 8.1, "strategy_name": "Percentage Recovery", "test_date": "2025-05-27T10:30:15", "timestamp": "2025-05-27T10:30:15", "total_profit": 405, "total_trades": 200, "win_rate": 65, "winning_trades": 130}, {"description": "Percentage-based recovery strategy", "final_balance": 1405, "is_real_strategy": true, "losing_trades": 70, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 8.1, "strategy_name": "Percentage Recovery", "test_date": "2025-05-27T10:30:15", "timestamp": "2025-05-27T10:30:19", "total_profit": 405, "total_trades": 200, "win_rate": 65, "winning_trades": 130}, {"description": "Percentage-based recovery strategy", "final_balance": 1497.5, "is_real_strategy": true, "losing_trades": 65, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 9.95, "strategy_name": "Percentage Recovery", "test_date": "2025-05-27T11:12:55", "timestamp": "2025-05-27T11:12:55", "total_profit": 497.5, "total_trades": 200, "win_rate": 67.5, "winning_trades": 135}, {"description": "Percentage-based recovery strategy", "final_balance": 1497.5, "is_real_strategy": true, "losing_trades": 65, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 9.95, "strategy_name": "Percentage Recovery", "test_date": "2025-05-27T11:12:55", "timestamp": "2025-05-27T11:12:59", "total_profit": 497.5, "total_trades": 200, "win_rate": 67.5, "winning_trades": 135}, {"description": "Percentage-based recovery strategy", "final_balance": 1238.5, "is_real_strategy": true, "losing_trades": 79, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 3.975, "strategy_name": "Percentage Recovery", "test_date": "2025-05-27T11:25:30", "timestamp": "2025-05-27T11:25:30", "total_profit": 238.5, "total_trades": 200, "win_rate": 60.5, "winning_trades": 121}, {"description": "Percentage-based recovery strategy", "final_balance": 1238.5, "is_real_strategy": true, "losing_trades": 79, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 3.975, "strategy_name": "Percentage Recovery", "test_date": "2025-05-27T11:25:30", "timestamp": "2025-05-27T11:25:35", "total_profit": 238.5, "total_trades": 200, "win_rate": 60.5, "winning_trades": 121}, {"description": "Percentage-based recovery strategy", "final_balance": 1294, "is_real_strategy": true, "losing_trades": 76, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 7.35, "strategy_name": "Percentage Recovery", "test_date": "2025-05-27T12:00:27", "timestamp": "2025-05-27T12:00:27", "total_profit": 294, "total_trades": 200, "win_rate": 62, "winning_trades": 124}, {"description": "Percentage-based recovery strategy", "final_balance": 1294, "is_real_strategy": true, "losing_trades": 76, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 7.35, "strategy_name": "Percentage Recovery", "test_date": "2025-05-27T12:00:27", "timestamp": "2025-05-27T12:00:31", "total_profit": 294, "total_trades": 200, "win_rate": 62, "winning_trades": 124}, {"description": "Percentage-based recovery strategy", "final_balance": 1442, "is_real_strategy": true, "losing_trades": 68, "max_drawdown": 80, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 5.525, "strategy_name": "Percentage Recovery", "test_date": "2025-05-27T12:38:59", "timestamp": "2025-05-27T12:38:59", "total_profit": 442, "total_trades": 200, "win_rate": 66, "winning_trades": 132}, {"description": "Percentage-based recovery strategy", "final_balance": 1442, "is_real_strategy": true, "losing_trades": 68, "max_drawdown": 80, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 5.525, "strategy_name": "Percentage Recovery", "test_date": "2025-05-27T12:38:59", "timestamp": "2025-05-27T12:39:04", "total_profit": 442, "total_trades": 200, "win_rate": 66, "winning_trades": 132}, {"description": "Percentage-based recovery strategy", "final_balance": 1331, "is_real_strategy": true, "losing_trades": 74, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 8.275, "strategy_name": "Percentage Recovery", "test_date": "2025-05-27T12:40:58", "timestamp": "2025-05-27T12:40:58", "total_profit": 331, "total_trades": 200, "win_rate": 63, "winning_trades": 126}, {"description": "Percentage-based recovery strategy", "final_balance": 1331, "is_real_strategy": true, "losing_trades": 74, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 8.275, "strategy_name": "Percentage Recovery", "test_date": "2025-05-27T12:40:58", "timestamp": "2025-05-27T12:41:02", "total_profit": 331, "total_trades": 200, "win_rate": 63, "winning_trades": 126}, {"description": "Percentage-based recovery strategy", "final_balance": 1331, "is_real_strategy": true, "losing_trades": 74, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 8.275, "strategy_name": "Percentage Recovery", "test_date": "2025-05-29T15:58:17", "timestamp": "2025-05-29T15:58:17", "total_profit": 331, "total_trades": 200, "win_rate": 63, "winning_trades": 126}, {"description": "Percentage-based recovery strategy", "final_balance": 1331, "is_real_strategy": true, "losing_trades": 74, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 8.275, "strategy_name": "Percentage Recovery", "test_date": "2025-05-29T15:58:17", "timestamp": "2025-05-29T15:58:21", "total_profit": 331, "total_trades": 200, "win_rate": 63, "winning_trades": 126}, {"description": "Percentage-based recovery strategy", "final_balance": 1312.5, "is_real_strategy": true, "losing_trades": 75, "max_drawdown": 90, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 3.4722222222222223, "strategy_name": "Percentage Recovery", "test_date": "2025-05-29T15:58:54", "timestamp": "2025-05-29T15:58:54", "total_profit": 312.5, "total_trades": 200, "win_rate": 62.5, "winning_trades": 125}, {"description": "Percentage-based recovery strategy", "final_balance": 1312.5, "is_real_strategy": true, "losing_trades": 75, "max_drawdown": 90, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 3.4722222222222223, "strategy_name": "Percentage Recovery", "test_date": "2025-05-29T15:58:54", "timestamp": "2025-05-29T15:58:58", "total_profit": 312.5, "total_trades": 200, "win_rate": 62.5, "winning_trades": 125}]
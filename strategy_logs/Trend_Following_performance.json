[{"description": "Trend following strategy using technical indicators", "final_balance": 1812, "is_real_strategy": true, "losing_trades": 48, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 27.066666666666666, "strategy_name": "Trend Following", "test_date": "2025-05-26T13:02:23", "timestamp": "2025-05-26T13:02:23", "total_profit": 812, "total_trades": 200, "win_rate": 76, "winning_trades": 152}, {"description": "Trend following strategy using technical indicators", "final_balance": 1812, "is_real_strategy": true, "losing_trades": 48, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 27.066666666666666, "strategy_name": "Trend Following", "test_date": "2025-05-26T13:02:23", "timestamp": "2025-05-26T13:02:28", "total_profit": 812, "total_trades": 200, "win_rate": 76, "winning_trades": 152}, {"description": "Trend following strategy using technical indicators", "final_balance": 1738, "is_real_strategy": true, "losing_trades": 52, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 24.6, "strategy_name": "Trend Following", "test_date": "2025-05-26T13:06:01", "timestamp": "2025-05-26T13:06:01", "total_profit": 738, "total_trades": 200, "win_rate": 74, "winning_trades": 148}, {"description": "Trend following strategy using technical indicators", "final_balance": 1738, "is_real_strategy": true, "losing_trades": 52, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 24.6, "strategy_name": "Trend Following", "test_date": "2025-05-26T13:06:01", "timestamp": "2025-05-26T13:06:06", "total_profit": 738, "total_trades": 200, "win_rate": 74, "winning_trades": 148}, {"description": "Trend following strategy using technical indicators", "final_balance": 1756.5, "is_real_strategy": true, "losing_trades": 51, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 25.216666666666665, "strategy_name": "Trend Following", "test_date": "2025-05-26T13:07:32", "timestamp": "2025-05-26T13:07:32", "total_profit": 756.5, "total_trades": 200, "win_rate": 74.5, "winning_trades": 149}, {"description": "Trend following strategy using technical indicators", "final_balance": 1756.5, "is_real_strategy": true, "losing_trades": 51, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 25.216666666666665, "strategy_name": "Trend Following", "test_date": "2025-05-26T13:07:32", "timestamp": "2025-05-26T13:07:38", "total_profit": 756.5, "total_trades": 200, "win_rate": 74.5, "winning_trades": 149}, {"description": "Trend following strategy using technical indicators", "final_balance": 1664, "is_real_strategy": true, "losing_trades": 56, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 13.28, "strategy_name": "Trend Following", "test_date": "2025-05-26T13:11:27", "timestamp": "2025-05-26T13:11:27", "total_profit": 664, "total_trades": 200, "win_rate": 72, "winning_trades": 144}, {"description": "Trend following strategy using technical indicators", "final_balance": 1664, "is_real_strategy": true, "losing_trades": 56, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 13.28, "strategy_name": "Trend Following", "test_date": "2025-05-26T13:11:27", "timestamp": "2025-05-26T13:11:32", "total_profit": 664, "total_trades": 200, "win_rate": 72, "winning_trades": 144}, {"description": "Trend following strategy using technical indicators", "final_balance": 1867.5, "is_real_strategy": true, "losing_trades": 45, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 28.916666666666668, "strategy_name": "Trend Following", "test_date": "2025-05-26T13:14:02", "timestamp": "2025-05-26T13:14:02", "total_profit": 867.5, "total_trades": 200, "win_rate": 77.5, "winning_trades": 155}, {"description": "Trend following strategy using technical indicators", "final_balance": 1867.5, "is_real_strategy": true, "losing_trades": 45, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 28.916666666666668, "strategy_name": "Trend Following", "test_date": "2025-05-26T13:14:02", "timestamp": "2025-05-26T13:14:07", "total_profit": 867.5, "total_trades": 200, "win_rate": 77.5, "winning_trades": 155}, {"description": "Trend following strategy using technical indicators", "final_balance": 1812, "is_real_strategy": true, "losing_trades": 48, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 27.066666666666666, "strategy_name": "Trend Following", "test_date": "2025-05-26T13:15:43", "timestamp": "2025-05-26T13:15:43", "total_profit": 812, "total_trades": 200, "win_rate": 76, "winning_trades": 152}, {"description": "Trend following strategy using technical indicators", "final_balance": 1812, "is_real_strategy": true, "losing_trades": 48, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 27.066666666666666, "strategy_name": "Trend Following", "test_date": "2025-05-26T13:15:43", "timestamp": "2025-05-26T13:15:46", "total_profit": 812, "total_trades": 200, "win_rate": 76, "winning_trades": 152}, {"description": "Trend following strategy using technical indicators", "final_balance": 1775, "is_real_strategy": true, "losing_trades": 50, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 15.5, "strategy_name": "Trend Following", "test_date": "2025-05-26T13:30:30", "timestamp": "2025-05-26T13:30:30", "total_profit": 775, "total_trades": 200, "win_rate": 75, "winning_trades": 150}, {"description": "Trend following strategy using technical indicators", "final_balance": 1775, "is_real_strategy": true, "losing_trades": 50, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 15.5, "strategy_name": "Trend Following", "test_date": "2025-05-26T13:30:30", "timestamp": "2025-05-26T13:30:35", "total_profit": 775, "total_trades": 200, "win_rate": 75, "winning_trades": 150}, {"description": "Trend following strategy using technical indicators", "final_balance": 1793.5, "is_real_strategy": true, "losing_trades": 49, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 19.8375, "strategy_name": "Trend Following", "test_date": "2025-05-26T13:30:56", "timestamp": "2025-05-26T13:30:56", "total_profit": 793.5, "total_trades": 200, "win_rate": 75.5, "winning_trades": 151}, {"description": "Trend following strategy using technical indicators", "final_balance": 1793.5, "is_real_strategy": true, "losing_trades": 49, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 19.8375, "strategy_name": "Trend Following", "test_date": "2025-05-26T13:30:56", "timestamp": "2025-05-26T13:30:59", "total_profit": 793.5, "total_trades": 200, "win_rate": 75.5, "winning_trades": 151}, {"description": "Trend following strategy using technical indicators", "final_balance": 1904.5, "is_real_strategy": true, "losing_trades": 43, "max_drawdown": 20, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 45.225, "strategy_name": "Trend Following", "test_date": "2025-05-26T13:41:22", "timestamp": "2025-05-26T13:41:22", "total_profit": 904.5, "total_trades": 200, "win_rate": 78.5, "winning_trades": 157}, {"description": "Trend following strategy using technical indicators", "final_balance": 1904.5, "is_real_strategy": true, "losing_trades": 43, "max_drawdown": 20, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 45.225, "strategy_name": "Trend Following", "test_date": "2025-05-26T13:41:22", "timestamp": "2025-05-26T13:41:25", "total_profit": 904.5, "total_trades": 200, "win_rate": 78.5, "winning_trades": 157}, {"description": "Trend following strategy using technical indicators", "final_balance": 1719.5, "is_real_strategy": true, "losing_trades": 53, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 17.9875, "strategy_name": "Trend Following", "test_date": "2025-05-27T10:30:16", "timestamp": "2025-05-27T10:30:16", "total_profit": 719.5, "total_trades": 200, "win_rate": 73.5, "winning_trades": 147}, {"description": "Trend following strategy using technical indicators", "final_balance": 1719.5, "is_real_strategy": true, "losing_trades": 53, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 17.9875, "strategy_name": "Trend Following", "test_date": "2025-05-27T10:30:16", "timestamp": "2025-05-27T10:30:19", "total_profit": 719.5, "total_trades": 200, "win_rate": 73.5, "winning_trades": 147}, {"description": "Trend following strategy using technical indicators", "final_balance": 1793.5, "is_real_strategy": true, "losing_trades": 49, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 19.8375, "strategy_name": "Trend Following", "test_date": "2025-05-27T11:12:56", "timestamp": "2025-05-27T11:12:56", "total_profit": 793.5, "total_trades": 200, "win_rate": 75.5, "winning_trades": 151}, {"description": "Trend following strategy using technical indicators", "final_balance": 1793.5, "is_real_strategy": true, "losing_trades": 49, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 19.8375, "strategy_name": "Trend Following", "test_date": "2025-05-27T11:12:56", "timestamp": "2025-05-27T11:12:59", "total_profit": 793.5, "total_trades": 200, "win_rate": 75.5, "winning_trades": 151}, {"description": "Trend following strategy using technical indicators", "final_balance": 1627, "is_real_strategy": true, "losing_trades": 58, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 20.9, "strategy_name": "Trend Following", "test_date": "2025-05-27T11:25:31", "timestamp": "2025-05-27T11:25:31", "total_profit": 627, "total_trades": 200, "win_rate": 71, "winning_trades": 142}, {"description": "Trend following strategy using technical indicators", "final_balance": 1627, "is_real_strategy": true, "losing_trades": 58, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 20.9, "strategy_name": "Trend Following", "test_date": "2025-05-27T11:25:31", "timestamp": "2025-05-27T11:25:35", "total_profit": 627, "total_trades": 200, "win_rate": 71, "winning_trades": 142}, {"description": "Trend following strategy using technical indicators", "final_balance": 1904.5, "is_real_strategy": true, "losing_trades": 43, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 30.15, "strategy_name": "Trend Following", "test_date": "2025-05-27T12:00:28", "timestamp": "2025-05-27T12:00:28", "total_profit": 904.5, "total_trades": 200, "win_rate": 78.5, "winning_trades": 157}, {"description": "Trend following strategy using technical indicators", "final_balance": 1904.5, "is_real_strategy": true, "losing_trades": 43, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 30.15, "strategy_name": "Trend Following", "test_date": "2025-05-27T12:00:28", "timestamp": "2025-05-27T12:00:31", "total_profit": 904.5, "total_trades": 200, "win_rate": 78.5, "winning_trades": 157}, {"description": "Trend following strategy using technical indicators", "final_balance": 1756.5, "is_real_strategy": true, "losing_trades": 51, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 25.216666666666665, "strategy_name": "Trend Following", "test_date": "2025-05-27T12:39:00", "timestamp": "2025-05-27T12:39:00", "total_profit": 756.5, "total_trades": 200, "win_rate": 74.5, "winning_trades": 149}, {"description": "Trend following strategy using technical indicators", "final_balance": 1756.5, "is_real_strategy": true, "losing_trades": 51, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 25.216666666666665, "strategy_name": "Trend Following", "test_date": "2025-05-27T12:39:00", "timestamp": "2025-05-27T12:39:04", "total_profit": 756.5, "total_trades": 200, "win_rate": 74.5, "winning_trades": 149}, {"description": "Trend following strategy using technical indicators", "final_balance": 1849, "is_real_strategy": true, "losing_trades": 46, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 21.225, "strategy_name": "Trend Following", "test_date": "2025-05-27T12:40:59", "timestamp": "2025-05-27T12:40:59", "total_profit": 849, "total_trades": 200, "win_rate": 77, "winning_trades": 154}, {"description": "Trend following strategy using technical indicators", "final_balance": 1849, "is_real_strategy": true, "losing_trades": 46, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 21.225, "strategy_name": "Trend Following", "test_date": "2025-05-27T12:40:59", "timestamp": "2025-05-27T12:41:02", "total_profit": 849, "total_trades": 200, "win_rate": 77, "winning_trades": 154}, {"description": "Trend following strategy using technical indicators", "final_balance": 1830.5, "is_real_strategy": true, "losing_trades": 47, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 16.61, "strategy_name": "Trend Following", "test_date": "2025-05-29T15:58:18", "timestamp": "2025-05-29T15:58:18", "total_profit": 830.5, "total_trades": 200, "win_rate": 76.5, "winning_trades": 153}, {"description": "Trend following strategy using technical indicators", "final_balance": 1830.5, "is_real_strategy": true, "losing_trades": 47, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 16.61, "strategy_name": "Trend Following", "test_date": "2025-05-29T15:58:18", "timestamp": "2025-05-29T15:58:21", "total_profit": 830.5, "total_trades": 200, "win_rate": 76.5, "winning_trades": 153}, {"description": "Trend following strategy using technical indicators", "final_balance": 1830.5, "is_real_strategy": true, "losing_trades": 47, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 27.683333333333334, "strategy_name": "Trend Following", "test_date": "2025-05-29T15:58:55", "timestamp": "2025-05-29T15:58:55", "total_profit": 830.5, "total_trades": 200, "win_rate": 76.5, "winning_trades": 153}, {"description": "Trend following strategy using technical indicators", "final_balance": 1830.5, "is_real_strategy": true, "losing_trades": 47, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 27.683333333333334, "strategy_name": "Trend Following", "test_date": "2025-05-29T15:58:55", "timestamp": "2025-05-29T15:58:58", "total_profit": 830.5, "total_trades": 200, "win_rate": 76.5, "winning_trades": 153}]
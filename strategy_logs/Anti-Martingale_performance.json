[{"description": "Anti-Martingale recovery strategy", "final_balance": 1312.5, "is_real_strategy": true, "losing_trades": 75, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 5.208333333333333, "strategy_name": "Anti-Martingale", "test_date": "2025-05-26T13:02:21", "timestamp": "2025-05-26T13:02:21", "total_profit": 312.5, "total_trades": 200, "win_rate": 62.5, "winning_trades": 125}, {"description": "Anti-Martingale recovery strategy", "final_balance": 1312.5, "is_real_strategy": true, "losing_trades": 75, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 5.208333333333333, "strategy_name": "Anti-Martingale", "test_date": "2025-05-26T13:02:21", "timestamp": "2025-05-26T13:02:28", "total_profit": 312.5, "total_trades": 200, "win_rate": 62.5, "winning_trades": 125}, {"description": "Anti-Martingale recovery strategy", "final_balance": 1386.5, "is_real_strategy": true, "losing_trades": 71, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 7.73, "strategy_name": "Anti-Martingale", "test_date": "2025-05-26T13:05:59", "timestamp": "2025-05-26T13:05:59", "total_profit": 386.5, "total_trades": 200, "win_rate": 64.5, "winning_trades": 129}, {"description": "Anti-Martingale recovery strategy", "final_balance": 1386.5, "is_real_strategy": true, "losing_trades": 71, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 7.73, "strategy_name": "Anti-Martingale", "test_date": "2025-05-26T13:05:59", "timestamp": "2025-05-26T13:06:06", "total_profit": 386.5, "total_trades": 200, "win_rate": 64.5, "winning_trades": 129}, {"description": "Anti-Martingale recovery strategy", "final_balance": 1201.5, "is_real_strategy": true, "losing_trades": 81, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 3.3583333333333334, "strategy_name": "Anti-Martingale", "test_date": "2025-05-26T13:07:30", "timestamp": "2025-05-26T13:07:30", "total_profit": 201.5, "total_trades": 200, "win_rate": 59.5, "winning_trades": 119}, {"description": "Anti-Martingale recovery strategy", "final_balance": 1201.5, "is_real_strategy": true, "losing_trades": 81, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 3.3583333333333334, "strategy_name": "Anti-Martingale", "test_date": "2025-05-26T13:07:30", "timestamp": "2025-05-26T13:07:38", "total_profit": 201.5, "total_trades": 200, "win_rate": 59.5, "winning_trades": 119}, {"description": "Anti-Martingale recovery strategy", "final_balance": 1479, "is_real_strategy": true, "losing_trades": 66, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 15.966666666666667, "strategy_name": "Anti-Martingale", "test_date": "2025-05-26T13:11:25", "timestamp": "2025-05-26T13:11:25", "total_profit": 479, "total_trades": 200, "win_rate": 67, "winning_trades": 134}, {"description": "Anti-Martingale recovery strategy", "final_balance": 1479, "is_real_strategy": true, "losing_trades": 66, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 15.966666666666667, "strategy_name": "Anti-Martingale", "test_date": "2025-05-26T13:11:25", "timestamp": "2025-05-26T13:11:32", "total_profit": 479, "total_trades": 200, "win_rate": 67, "winning_trades": 134}, {"description": "Anti-Martingale recovery strategy", "final_balance": 1294, "is_real_strategy": true, "losing_trades": 76, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 5.88, "strategy_name": "Anti-Martingale", "test_date": "2025-05-26T13:14:00", "timestamp": "2025-05-26T13:14:00", "total_profit": 294, "total_trades": 200, "win_rate": 62, "winning_trades": 124}, {"description": "Anti-Martingale recovery strategy", "final_balance": 1294, "is_real_strategy": true, "losing_trades": 76, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 5.88, "strategy_name": "Anti-Martingale", "test_date": "2025-05-26T13:14:00", "timestamp": "2025-05-26T13:14:07", "total_profit": 294, "total_trades": 200, "win_rate": 62, "winning_trades": 124}, {"description": "Anti-Martingale recovery strategy", "final_balance": 1312.5, "is_real_strategy": true, "losing_trades": 75, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 5.208333333333333, "strategy_name": "Anti-Martingale", "test_date": "2025-05-26T13:15:41", "timestamp": "2025-05-26T13:15:41", "total_profit": 312.5, "total_trades": 200, "win_rate": 62.5, "winning_trades": 125}, {"description": "Anti-Martingale recovery strategy", "final_balance": 1312.5, "is_real_strategy": true, "losing_trades": 75, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 5.208333333333333, "strategy_name": "Anti-Martingale", "test_date": "2025-05-26T13:15:41", "timestamp": "2025-05-26T13:15:46", "total_profit": 312.5, "total_trades": 200, "win_rate": 62.5, "winning_trades": 125}, {"description": "Anti-Martingale recovery strategy", "final_balance": 1183, "is_real_strategy": true, "losing_trades": 82, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 3.05, "strategy_name": "Anti-Martingale", "test_date": "2025-05-26T13:30:28", "timestamp": "2025-05-26T13:30:28", "total_profit": 183, "total_trades": 200, "win_rate": 59, "winning_trades": 118}, {"description": "Anti-Martingale recovery strategy", "final_balance": 1183, "is_real_strategy": true, "losing_trades": 82, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 3.05, "strategy_name": "Anti-Martingale", "test_date": "2025-05-26T13:30:28", "timestamp": "2025-05-26T13:30:35", "total_profit": 183, "total_trades": 200, "win_rate": 59, "winning_trades": 118}, {"description": "Anti-Martingale recovery strategy", "final_balance": 1386.5, "is_real_strategy": true, "losing_trades": 71, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 9.6625, "strategy_name": "Anti-Martingale", "test_date": "2025-05-26T13:30:52", "timestamp": "2025-05-26T13:30:52", "total_profit": 386.5, "total_trades": 200, "win_rate": 64.5, "winning_trades": 129}, {"description": "Anti-Martingale recovery strategy", "final_balance": 1386.5, "is_real_strategy": true, "losing_trades": 71, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 9.6625, "strategy_name": "Anti-Martingale", "test_date": "2025-05-26T13:30:52", "timestamp": "2025-05-26T13:30:59", "total_profit": 386.5, "total_trades": 200, "win_rate": 64.5, "winning_trades": 129}, {"description": "Anti-Martingale recovery strategy", "final_balance": 1368, "is_real_strategy": true, "losing_trades": 72, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 9.2, "strategy_name": "Anti-Martingale", "test_date": "2025-05-26T13:41:20", "timestamp": "2025-05-26T13:41:20", "total_profit": 368, "total_trades": 200, "win_rate": 64, "winning_trades": 128}, {"description": "Anti-Martingale recovery strategy", "final_balance": 1368, "is_real_strategy": true, "losing_trades": 72, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 9.2, "strategy_name": "Anti-Martingale", "test_date": "2025-05-26T13:41:20", "timestamp": "2025-05-26T13:41:25", "total_profit": 368, "total_trades": 200, "win_rate": 64, "winning_trades": 128}, {"description": "Anti-Martingale recovery strategy", "final_balance": 1331, "is_real_strategy": true, "losing_trades": 74, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 6.62, "strategy_name": "Anti-Martingale", "test_date": "2025-05-27T10:05:15", "timestamp": "2025-05-27T10:05:15", "total_profit": 331, "total_trades": 200, "win_rate": 63, "winning_trades": 126}, {"description": "Anti-Martingale recovery strategy", "final_balance": 1294, "is_real_strategy": true, "losing_trades": 76, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 4.9, "strategy_name": "Anti-Martingale", "test_date": "2025-05-27T10:30:14", "timestamp": "2025-05-27T10:30:14", "total_profit": 294, "total_trades": 200, "win_rate": 62, "winning_trades": 124}, {"description": "Anti-Martingale recovery strategy", "final_balance": 1294, "is_real_strategy": true, "losing_trades": 76, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 4.9, "strategy_name": "Anti-Martingale", "test_date": "2025-05-27T10:30:14", "timestamp": "2025-05-27T10:30:19", "total_profit": 294, "total_trades": 200, "win_rate": 62, "winning_trades": 124}, {"description": "Anti-Martingale recovery strategy", "final_balance": 1238.5, "is_real_strategy": true, "losing_trades": 79, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 4.77, "strategy_name": "Anti-Martingale", "test_date": "2025-05-27T11:12:54", "timestamp": "2025-05-27T11:12:54", "total_profit": 238.5, "total_trades": 200, "win_rate": 60.5, "winning_trades": 121}, {"description": "Anti-Martingale recovery strategy", "final_balance": 1238.5, "is_real_strategy": true, "losing_trades": 79, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 4.77, "strategy_name": "Anti-Martingale", "test_date": "2025-05-27T11:12:54", "timestamp": "2025-05-27T11:12:59", "total_profit": 238.5, "total_trades": 200, "win_rate": 60.5, "winning_trades": 121}, {"description": "Anti-Martingale recovery strategy", "final_balance": 1331, "is_real_strategy": true, "losing_trades": 74, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 6.62, "strategy_name": "Anti-Martingale", "test_date": "2025-05-27T11:25:29", "timestamp": "2025-05-27T11:25:29", "total_profit": 331, "total_trades": 200, "win_rate": 63, "winning_trades": 126}, {"description": "Anti-Martingale recovery strategy", "final_balance": 1331, "is_real_strategy": true, "losing_trades": 74, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 6.62, "strategy_name": "Anti-Martingale", "test_date": "2025-05-27T11:25:29", "timestamp": "2025-05-27T11:25:35", "total_profit": 331, "total_trades": 200, "win_rate": 63, "winning_trades": 126}, {"description": "Anti-Martingale recovery strategy", "final_balance": 1349.5, "is_real_strategy": true, "losing_trades": 73, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 8.7375, "strategy_name": "Anti-Martingale", "test_date": "2025-05-27T12:00:25", "timestamp": "2025-05-27T12:00:25", "total_profit": 349.5, "total_trades": 200, "win_rate": 63.5, "winning_trades": 127}, {"description": "Anti-Martingale recovery strategy", "final_balance": 1349.5, "is_real_strategy": true, "losing_trades": 73, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 8.7375, "strategy_name": "Anti-Martingale", "test_date": "2025-05-27T12:00:25", "timestamp": "2025-05-27T12:00:31", "total_profit": 349.5, "total_trades": 200, "win_rate": 63.5, "winning_trades": 127}, {"description": "Anti-Martingale recovery strategy", "final_balance": 1257, "is_real_strategy": true, "losing_trades": 78, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 5.14, "strategy_name": "Anti-Martingale", "test_date": "2025-05-27T12:38:58", "timestamp": "2025-05-27T12:38:58", "total_profit": 257, "total_trades": 200, "win_rate": 61, "winning_trades": 122}, {"description": "Anti-Martingale recovery strategy", "final_balance": 1257, "is_real_strategy": true, "losing_trades": 78, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 5.14, "strategy_name": "Anti-Martingale", "test_date": "2025-05-27T12:38:58", "timestamp": "2025-05-27T12:39:04", "total_profit": 257, "total_trades": 200, "win_rate": 61, "winning_trades": 122}, {"description": "Anti-Martingale recovery strategy", "final_balance": 1275.5, "is_real_strategy": true, "losing_trades": 77, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 5.51, "strategy_name": "Anti-Martingale", "test_date": "2025-05-27T12:40:57", "timestamp": "2025-05-27T12:40:57", "total_profit": 275.5, "total_trades": 200, "win_rate": 61.5, "winning_trades": 123}, {"description": "Anti-Martingale recovery strategy", "final_balance": 1275.5, "is_real_strategy": true, "losing_trades": 77, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 5.51, "strategy_name": "Anti-Martingale", "test_date": "2025-05-27T12:40:57", "timestamp": "2025-05-27T12:41:02", "total_profit": 275.5, "total_trades": 200, "win_rate": 61.5, "winning_trades": 123}, {"description": "Anti-Martingale recovery strategy", "final_balance": 1201.5, "is_real_strategy": true, "losing_trades": 81, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 4.03, "strategy_name": "Anti-Martingale", "test_date": "2025-05-29T15:58:16", "timestamp": "2025-05-29T15:58:16", "total_profit": 201.5, "total_trades": 200, "win_rate": 59.5, "winning_trades": 119}, {"description": "Anti-Martingale recovery strategy", "final_balance": 1201.5, "is_real_strategy": true, "losing_trades": 81, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 4.03, "strategy_name": "Anti-Martingale", "test_date": "2025-05-29T15:58:16", "timestamp": "2025-05-29T15:58:21", "total_profit": 201.5, "total_trades": 200, "win_rate": 59.5, "winning_trades": 119}, {"description": "Anti-Martingale recovery strategy", "final_balance": 1553, "is_real_strategy": true, "losing_trades": 62, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 13.825, "strategy_name": "Anti-Martingale", "test_date": "2025-05-29T15:58:53", "timestamp": "2025-05-29T15:58:53", "total_profit": 553, "total_trades": 200, "win_rate": 69, "winning_trades": 138}, {"description": "Anti-Martingale recovery strategy", "final_balance": 1553, "is_real_strategy": true, "losing_trades": 62, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 13.825, "strategy_name": "Anti-Martingale", "test_date": "2025-05-29T15:58:53", "timestamp": "2025-05-29T15:58:58", "total_profit": 553, "total_trades": 200, "win_rate": 69, "winning_trades": 138}]
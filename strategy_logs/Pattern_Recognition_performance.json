[{"description": "AI-powered pattern recognition strategy", "final_balance": 1812, "is_real_strategy": true, "losing_trades": 48, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 27.066666666666666, "strategy_name": "Pattern Recognition", "test_date": "2025-05-26T13:02:27", "timestamp": "2025-05-26T13:02:27", "total_profit": 812, "total_trades": 200, "win_rate": 76, "winning_trades": 152}, {"description": "AI-powered pattern recognition strategy", "final_balance": 1812, "is_real_strategy": true, "losing_trades": 48, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 27.066666666666666, "strategy_name": "Pattern Recognition", "test_date": "2025-05-26T13:02:27", "timestamp": "2025-05-26T13:02:28", "total_profit": 812, "total_trades": 200, "win_rate": 76, "winning_trades": 152}, {"description": "AI-powered pattern recognition strategy", "final_balance": 2145, "is_real_strategy": true, "losing_trades": 30, "max_drawdown": 30, "meets_target": true, "profit_factor": 2, "recommendation": "Meets target range - ready for live trading", "risk_level": "Low", "sharpe_ratio": 38.166666666666664, "strategy_name": "Pattern Recognition", "test_date": "2025-05-26T13:06:05", "timestamp": "2025-05-26T13:06:05", "total_profit": 1145, "total_trades": 200, "win_rate": 85, "winning_trades": 170}, {"description": "AI-powered pattern recognition strategy", "final_balance": 2145, "is_real_strategy": true, "losing_trades": 30, "max_drawdown": 30, "meets_target": true, "profit_factor": 2, "recommendation": "Meets target range - ready for live trading", "risk_level": "Low", "sharpe_ratio": 38.166666666666664, "strategy_name": "Pattern Recognition", "test_date": "2025-05-26T13:06:05", "timestamp": "2025-05-26T13:06:06", "total_profit": 1145, "total_trades": 200, "win_rate": 85, "winning_trades": 170}, {"description": "AI-powered pattern recognition strategy", "final_balance": 1904.5, "is_real_strategy": true, "losing_trades": 43, "max_drawdown": 20, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 45.225, "strategy_name": "Pattern Recognition", "test_date": "2025-05-26T13:07:35", "timestamp": "2025-05-26T13:07:35", "total_profit": 904.5, "total_trades": 200, "win_rate": 78.5, "winning_trades": 157}, {"description": "AI-powered pattern recognition strategy", "final_balance": 1904.5, "is_real_strategy": true, "losing_trades": 43, "max_drawdown": 20, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 45.225, "strategy_name": "Pattern Recognition", "test_date": "2025-05-26T13:07:35", "timestamp": "2025-05-26T13:07:38", "total_profit": 904.5, "total_trades": 200, "win_rate": 78.5, "winning_trades": 157}, {"description": "AI-powered pattern recognition strategy", "final_balance": 2015.5, "is_real_strategy": true, "losing_trades": 37, "max_drawdown": 30, "meets_target": true, "profit_factor": 2, "recommendation": "Meets target range - ready for live trading", "risk_level": "Low", "sharpe_ratio": 33.85, "strategy_name": "Pattern Recognition", "test_date": "2025-05-26T13:11:31", "timestamp": "2025-05-26T13:11:31", "total_profit": 1015.5, "total_trades": 200, "win_rate": 81.5, "winning_trades": 163}, {"description": "AI-powered pattern recognition strategy", "final_balance": 2015.5, "is_real_strategy": true, "losing_trades": 37, "max_drawdown": 30, "meets_target": true, "profit_factor": 2, "recommendation": "Meets target range - ready for live trading", "risk_level": "Low", "sharpe_ratio": 33.85, "strategy_name": "Pattern Recognition", "test_date": "2025-05-26T13:11:31", "timestamp": "2025-05-26T13:11:32", "total_profit": 1015.5, "total_trades": 200, "win_rate": 81.5, "winning_trades": 163}, {"description": "AI-powered pattern recognition strategy", "final_balance": 1904.5, "is_real_strategy": true, "losing_trades": 43, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 30.15, "strategy_name": "Pattern Recognition", "test_date": "2025-05-26T13:14:04", "timestamp": "2025-05-26T13:14:04", "total_profit": 904.5, "total_trades": 200, "win_rate": 78.5, "winning_trades": 157}, {"description": "AI-powered pattern recognition strategy", "final_balance": 1904.5, "is_real_strategy": true, "losing_trades": 43, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 30.15, "strategy_name": "Pattern Recognition", "test_date": "2025-05-26T13:14:04", "timestamp": "2025-05-26T13:14:07", "total_profit": 904.5, "total_trades": 200, "win_rate": 78.5, "winning_trades": 157}, {"description": "AI-powered pattern recognition strategy", "final_balance": 1904.5, "is_real_strategy": true, "losing_trades": 43, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 22.6125, "strategy_name": "Pattern Recognition", "test_date": "2025-05-26T13:15:45", "timestamp": "2025-05-26T13:15:45", "total_profit": 904.5, "total_trades": 200, "win_rate": 78.5, "winning_trades": 157}, {"description": "AI-powered pattern recognition strategy", "final_balance": 1904.5, "is_real_strategy": true, "losing_trades": 43, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 22.6125, "strategy_name": "Pattern Recognition", "test_date": "2025-05-26T13:15:45", "timestamp": "2025-05-26T13:15:46", "total_profit": 904.5, "total_trades": 200, "win_rate": 78.5, "winning_trades": 157}, {"description": "AI-powered pattern recognition strategy", "final_balance": 1830.5, "is_real_strategy": true, "losing_trades": 47, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 20.7625, "strategy_name": "Pattern Recognition", "test_date": "2025-05-26T13:30:34", "timestamp": "2025-05-26T13:30:34", "total_profit": 830.5, "total_trades": 200, "win_rate": 76.5, "winning_trades": 153}, {"description": "AI-powered pattern recognition strategy", "final_balance": 1830.5, "is_real_strategy": true, "losing_trades": 47, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 20.7625, "strategy_name": "Pattern Recognition", "test_date": "2025-05-26T13:30:34", "timestamp": "2025-05-26T13:30:35", "total_profit": 830.5, "total_trades": 200, "win_rate": 76.5, "winning_trades": 153}, {"description": "AI-powered pattern recognition strategy", "final_balance": 1719.5, "is_real_strategy": true, "losing_trades": 53, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 17.9875, "strategy_name": "Pattern Recognition", "test_date": "2025-05-26T13:30:58", "timestamp": "2025-05-26T13:30:58", "total_profit": 719.5, "total_trades": 200, "win_rate": 73.5, "winning_trades": 147}, {"description": "AI-powered pattern recognition strategy", "final_balance": 1719.5, "is_real_strategy": true, "losing_trades": 53, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 17.9875, "strategy_name": "Pattern Recognition", "test_date": "2025-05-26T13:30:58", "timestamp": "2025-05-26T13:30:59", "total_profit": 719.5, "total_trades": 200, "win_rate": 73.5, "winning_trades": 147}, {"description": "AI-powered pattern recognition strategy", "final_balance": 1867.5, "is_real_strategy": true, "losing_trades": 45, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 21.6875, "strategy_name": "Pattern Recognition", "test_date": "2025-05-26T13:41:24", "timestamp": "2025-05-26T13:41:24", "total_profit": 867.5, "total_trades": 200, "win_rate": 77.5, "winning_trades": 155}, {"description": "AI-powered pattern recognition strategy", "final_balance": 1867.5, "is_real_strategy": true, "losing_trades": 45, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 21.6875, "strategy_name": "Pattern Recognition", "test_date": "2025-05-26T13:41:24", "timestamp": "2025-05-26T13:41:25", "total_profit": 867.5, "total_trades": 200, "win_rate": 77.5, "winning_trades": 155}, {"description": "AI-powered pattern recognition strategy", "final_balance": 1923, "is_real_strategy": true, "losing_trades": 42, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 23.075, "strategy_name": "Pattern Recognition", "test_date": "2025-05-27T10:30:18", "timestamp": "2025-05-27T10:30:18", "total_profit": 923, "total_trades": 200, "win_rate": 79, "winning_trades": 158}, {"description": "AI-powered pattern recognition strategy", "final_balance": 1923, "is_real_strategy": true, "losing_trades": 42, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 23.075, "strategy_name": "Pattern Recognition", "test_date": "2025-05-27T10:30:18", "timestamp": "2025-05-27T10:30:19", "total_profit": 923, "total_trades": 200, "win_rate": 79, "winning_trades": 158}, {"description": "AI-powered pattern recognition strategy", "final_balance": 1886, "is_real_strategy": true, "losing_trades": 44, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 29.533333333333335, "strategy_name": "Pattern Recognition", "test_date": "2025-05-27T11:12:58", "timestamp": "2025-05-27T11:12:58", "total_profit": 886, "total_trades": 200, "win_rate": 78, "winning_trades": 156}, {"description": "AI-powered pattern recognition strategy", "final_balance": 1886, "is_real_strategy": true, "losing_trades": 44, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 29.533333333333335, "strategy_name": "Pattern Recognition", "test_date": "2025-05-27T11:12:58", "timestamp": "2025-05-27T11:12:59", "total_profit": 886, "total_trades": 200, "win_rate": 78, "winning_trades": 156}, {"description": "AI-powered pattern recognition strategy", "final_balance": 1830.5, "is_real_strategy": true, "losing_trades": 47, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 27.683333333333334, "strategy_name": "Pattern Recognition", "test_date": "2025-05-27T11:25:34", "timestamp": "2025-05-27T11:25:34", "total_profit": 830.5, "total_trades": 200, "win_rate": 76.5, "winning_trades": 153}, {"description": "AI-powered pattern recognition strategy", "final_balance": 1830.5, "is_real_strategy": true, "losing_trades": 47, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 27.683333333333334, "strategy_name": "Pattern Recognition", "test_date": "2025-05-27T11:25:34", "timestamp": "2025-05-27T11:25:35", "total_profit": 830.5, "total_trades": 200, "win_rate": 76.5, "winning_trades": 153}, {"description": "AI-powered pattern recognition strategy", "final_balance": 1923, "is_real_strategy": true, "losing_trades": 42, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 23.075, "strategy_name": "Pattern Recognition", "test_date": "2025-05-27T12:00:30", "timestamp": "2025-05-27T12:00:30", "total_profit": 923, "total_trades": 200, "win_rate": 79, "winning_trades": 158}, {"description": "AI-powered pattern recognition strategy", "final_balance": 1923, "is_real_strategy": true, "losing_trades": 42, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 23.075, "strategy_name": "Pattern Recognition", "test_date": "2025-05-27T12:00:30", "timestamp": "2025-05-27T12:00:31", "total_profit": 923, "total_trades": 200, "win_rate": 79, "winning_trades": 158}, {"description": "AI-powered pattern recognition strategy", "final_balance": 1997, "is_real_strategy": true, "losing_trades": 38, "max_drawdown": 40, "meets_target": true, "profit_factor": 2, "recommendation": "Meets target range - ready for live trading", "risk_level": "Low", "sharpe_ratio": 24.925, "strategy_name": "Pattern Recognition", "test_date": "2025-05-27T12:39:03", "timestamp": "2025-05-27T12:39:03", "total_profit": 997, "total_trades": 200, "win_rate": 81, "winning_trades": 162}, {"description": "AI-powered pattern recognition strategy", "final_balance": 1997, "is_real_strategy": true, "losing_trades": 38, "max_drawdown": 40, "meets_target": true, "profit_factor": 2, "recommendation": "Meets target range - ready for live trading", "risk_level": "Low", "sharpe_ratio": 24.925, "strategy_name": "Pattern Recognition", "test_date": "2025-05-27T12:39:03", "timestamp": "2025-05-27T12:39:04", "total_profit": 997, "total_trades": 200, "win_rate": 81, "winning_trades": 162}, {"description": "AI-powered pattern recognition strategy", "final_balance": 1756.5, "is_real_strategy": true, "losing_trades": 51, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 15.13, "strategy_name": "Pattern Recognition", "test_date": "2025-05-27T12:41:01", "timestamp": "2025-05-27T12:41:01", "total_profit": 756.5, "total_trades": 200, "win_rate": 74.5, "winning_trades": 149}, {"description": "AI-powered pattern recognition strategy", "final_balance": 1756.5, "is_real_strategy": true, "losing_trades": 51, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 15.13, "strategy_name": "Pattern Recognition", "test_date": "2025-05-27T12:41:01", "timestamp": "2025-05-27T12:41:02", "total_profit": 756.5, "total_trades": 200, "win_rate": 74.5, "winning_trades": 149}, {"description": "AI-powered pattern recognition strategy", "final_balance": 1756.5, "is_real_strategy": true, "losing_trades": 51, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 25.216666666666665, "strategy_name": "Pattern Recognition", "test_date": "2025-05-29T15:58:20", "timestamp": "2025-05-29T15:58:20", "total_profit": 756.5, "total_trades": 200, "win_rate": 74.5, "winning_trades": 149}, {"description": "AI-powered pattern recognition strategy", "final_balance": 1756.5, "is_real_strategy": true, "losing_trades": 51, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Low", "sharpe_ratio": 25.216666666666665, "strategy_name": "Pattern Recognition", "test_date": "2025-05-29T15:58:20", "timestamp": "2025-05-29T15:58:21", "total_profit": 756.5, "total_trades": 200, "win_rate": 74.5, "winning_trades": 149}, {"description": "AI-powered pattern recognition strategy", "final_balance": 1978.5, "is_real_strategy": true, "losing_trades": 39, "max_drawdown": 30, "meets_target": true, "profit_factor": 2, "recommendation": "Meets target range - ready for live trading", "risk_level": "Low", "sharpe_ratio": 32.61666666666667, "strategy_name": "Pattern Recognition", "test_date": "2025-05-29T15:58:57", "timestamp": "2025-05-29T15:58:57", "total_profit": 978.5, "total_trades": 200, "win_rate": 80.5, "winning_trades": 161}, {"description": "AI-powered pattern recognition strategy", "final_balance": 1978.5, "is_real_strategy": true, "losing_trades": 39, "max_drawdown": 30, "meets_target": true, "profit_factor": 2, "recommendation": "Meets target range - ready for live trading", "risk_level": "Low", "sharpe_ratio": 32.61666666666667, "strategy_name": "Pattern Recognition", "test_date": "2025-05-29T15:58:57", "timestamp": "2025-05-29T15:58:58", "total_profit": 978.5, "total_trades": 200, "win_rate": 80.5, "winning_trades": 161}]
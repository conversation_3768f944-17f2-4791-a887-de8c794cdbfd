[{"description": "Market reversal detection strategy", "final_balance": 1627, "is_real_strategy": true, "losing_trades": 58, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 12.54, "strategy_name": "Reversal", "test_date": "2025-05-26T13:02:25", "timestamp": "2025-05-26T13:02:25", "total_profit": 627, "total_trades": 200, "win_rate": 71, "winning_trades": 142}, {"description": "Market reversal detection strategy", "final_balance": 1627, "is_real_strategy": true, "losing_trades": 58, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 12.54, "strategy_name": "Reversal", "test_date": "2025-05-26T13:02:25", "timestamp": "2025-05-26T13:02:28", "total_profit": 627, "total_trades": 200, "win_rate": 71, "winning_trades": 142}, {"description": "Market reversal detection strategy", "final_balance": 1682.5, "is_real_strategy": true, "losing_trades": 55, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 13.65, "strategy_name": "Reversal", "test_date": "2025-05-26T13:06:01", "timestamp": "2025-05-26T13:06:01", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5, "winning_trades": 145}, {"description": "Market reversal detection strategy", "final_balance": 1682.5, "is_real_strategy": true, "losing_trades": 55, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 13.65, "strategy_name": "Reversal", "test_date": "2025-05-26T13:06:01", "timestamp": "2025-05-26T13:06:06", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5, "winning_trades": 145}, {"description": "Market reversal detection strategy", "final_balance": 1516, "is_real_strategy": true, "losing_trades": 64, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 8.6, "strategy_name": "Reversal", "test_date": "2025-05-26T13:07:33", "timestamp": "2025-05-26T13:07:33", "total_profit": 516, "total_trades": 200, "win_rate": 68, "winning_trades": 136}, {"description": "Market reversal detection strategy", "final_balance": 1516, "is_real_strategy": true, "losing_trades": 64, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 8.6, "strategy_name": "Reversal", "test_date": "2025-05-26T13:07:33", "timestamp": "2025-05-26T13:07:38", "total_profit": 516, "total_trades": 200, "win_rate": 68, "winning_trades": 136}, {"description": "Market reversal detection strategy", "final_balance": 1645.5, "is_real_strategy": true, "losing_trades": 57, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 16.1375, "strategy_name": "Reversal", "test_date": "2025-05-26T13:11:27", "timestamp": "2025-05-26T13:11:27", "total_profit": 645.5, "total_trades": 200, "win_rate": 71.5, "winning_trades": 143}, {"description": "Market reversal detection strategy", "final_balance": 1645.5, "is_real_strategy": true, "losing_trades": 57, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 16.1375, "strategy_name": "Reversal", "test_date": "2025-05-26T13:11:27", "timestamp": "2025-05-26T13:11:32", "total_profit": 645.5, "total_trades": 200, "win_rate": 71.5, "winning_trades": 143}, {"description": "Market reversal detection strategy", "final_balance": 1645.5, "is_real_strategy": true, "losing_trades": 57, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 16.1375, "strategy_name": "Reversal", "test_date": "2025-05-26T13:14:02", "timestamp": "2025-05-26T13:14:02", "total_profit": 645.5, "total_trades": 200, "win_rate": 71.5, "winning_trades": 143}, {"description": "Market reversal detection strategy", "final_balance": 1645.5, "is_real_strategy": true, "losing_trades": 57, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 16.1375, "strategy_name": "Reversal", "test_date": "2025-05-26T13:14:02", "timestamp": "2025-05-26T13:14:07", "total_profit": 645.5, "total_trades": 200, "win_rate": 71.5, "winning_trades": 143}, {"description": "Market reversal detection strategy", "final_balance": 1682.5, "is_real_strategy": true, "losing_trades": 55, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 17.0625, "strategy_name": "Reversal", "test_date": "2025-05-26T13:15:43", "timestamp": "2025-05-26T13:15:43", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5, "winning_trades": 145}, {"description": "Market reversal detection strategy", "final_balance": 1682.5, "is_real_strategy": true, "losing_trades": 55, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 17.0625, "strategy_name": "Reversal", "test_date": "2025-05-26T13:15:43", "timestamp": "2025-05-26T13:15:46", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5, "winning_trades": 145}, {"description": "Market reversal detection strategy", "final_balance": 1682.5, "is_real_strategy": true, "losing_trades": 55, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 17.0625, "strategy_name": "Reversal", "test_date": "2025-05-26T13:30:30", "timestamp": "2025-05-26T13:30:30", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5, "winning_trades": 145}, {"description": "Market reversal detection strategy", "final_balance": 1682.5, "is_real_strategy": true, "losing_trades": 55, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 17.0625, "strategy_name": "Reversal", "test_date": "2025-05-26T13:30:30", "timestamp": "2025-05-26T13:30:35", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5, "winning_trades": 145}, {"description": "Market reversal detection strategy", "final_balance": 1571.5, "is_real_strategy": true, "losing_trades": 61, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 19.05, "strategy_name": "Reversal", "test_date": "2025-05-26T13:30:56", "timestamp": "2025-05-26T13:30:56", "total_profit": 571.5, "total_trades": 200, "win_rate": 69.5, "winning_trades": 139}, {"description": "Market reversal detection strategy", "final_balance": 1571.5, "is_real_strategy": true, "losing_trades": 61, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 19.05, "strategy_name": "Reversal", "test_date": "2025-05-26T13:30:56", "timestamp": "2025-05-26T13:30:59", "total_profit": 571.5, "total_trades": 200, "win_rate": 69.5, "winning_trades": 139}, {"description": "Market reversal detection strategy", "final_balance": 1701, "is_real_strategy": true, "losing_trades": 54, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 23.366666666666667, "strategy_name": "Reversal", "test_date": "2025-05-26T13:41:22", "timestamp": "2025-05-26T13:41:22", "total_profit": 701, "total_trades": 200, "win_rate": 73, "winning_trades": 146}, {"description": "Market reversal detection strategy", "final_balance": 1701, "is_real_strategy": true, "losing_trades": 54, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 23.366666666666667, "strategy_name": "Reversal", "test_date": "2025-05-26T13:41:22", "timestamp": "2025-05-26T13:41:25", "total_profit": 701, "total_trades": 200, "win_rate": 73, "winning_trades": 146}, {"description": "Market reversal detection strategy", "final_balance": 1701, "is_real_strategy": true, "losing_trades": 54, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 17.525, "strategy_name": "Reversal", "test_date": "2025-05-27T10:30:16", "timestamp": "2025-05-27T10:30:16", "total_profit": 701, "total_trades": 200, "win_rate": 73, "winning_trades": 146}, {"description": "Market reversal detection strategy", "final_balance": 1701, "is_real_strategy": true, "losing_trades": 54, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 17.525, "strategy_name": "Reversal", "test_date": "2025-05-27T10:30:16", "timestamp": "2025-05-27T10:30:19", "total_profit": 701, "total_trades": 200, "win_rate": 73, "winning_trades": 146}, {"description": "Market reversal detection strategy", "final_balance": 1682.5, "is_real_strategy": true, "losing_trades": 55, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 17.0625, "strategy_name": "Reversal", "test_date": "2025-05-27T11:12:56", "timestamp": "2025-05-27T11:12:56", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5, "winning_trades": 145}, {"description": "Market reversal detection strategy", "final_balance": 1682.5, "is_real_strategy": true, "losing_trades": 55, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 17.0625, "strategy_name": "Reversal", "test_date": "2025-05-27T11:12:56", "timestamp": "2025-05-27T11:12:59", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5, "winning_trades": 145}, {"description": "Market reversal detection strategy", "final_balance": 1682.5, "is_real_strategy": true, "losing_trades": 55, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 22.75, "strategy_name": "Reversal", "test_date": "2025-05-27T11:25:32", "timestamp": "2025-05-27T11:25:32", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5, "winning_trades": 145}, {"description": "Market reversal detection strategy", "final_balance": 1682.5, "is_real_strategy": true, "losing_trades": 55, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 22.75, "strategy_name": "Reversal", "test_date": "2025-05-27T11:25:32", "timestamp": "2025-05-27T11:25:35", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5, "winning_trades": 145}, {"description": "Market reversal detection strategy", "final_balance": 1590, "is_real_strategy": true, "losing_trades": 60, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 14.75, "strategy_name": "Reversal", "test_date": "2025-05-27T12:00:28", "timestamp": "2025-05-27T12:00:28", "total_profit": 590, "total_trades": 200, "win_rate": 70, "winning_trades": 140}, {"description": "Market reversal detection strategy", "final_balance": 1590, "is_real_strategy": true, "losing_trades": 60, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 14.75, "strategy_name": "Reversal", "test_date": "2025-05-27T12:00:28", "timestamp": "2025-05-27T12:00:31", "total_profit": 590, "total_trades": 200, "win_rate": 70, "winning_trades": 140}, {"description": "Market reversal detection strategy", "final_balance": 1349.5, "is_real_strategy": true, "losing_trades": 73, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 5.825, "strategy_name": "Reversal", "test_date": "2025-05-27T12:39:01", "timestamp": "2025-05-27T12:39:01", "total_profit": 349.5, "total_trades": 200, "win_rate": 63.5, "winning_trades": 127}, {"description": "Market reversal detection strategy", "final_balance": 1349.5, "is_real_strategy": true, "losing_trades": 73, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 5.825, "strategy_name": "Reversal", "test_date": "2025-05-27T12:39:01", "timestamp": "2025-05-27T12:39:04", "total_profit": 349.5, "total_trades": 200, "win_rate": 63.5, "winning_trades": 127}, {"description": "Market reversal detection strategy", "final_balance": 1571.5, "is_real_strategy": true, "losing_trades": 61, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 9.525, "strategy_name": "Reversal", "test_date": "2025-05-27T12:40:59", "timestamp": "2025-05-27T12:40:59", "total_profit": 571.5, "total_trades": 200, "win_rate": 69.5, "winning_trades": 139}, {"description": "Market reversal detection strategy", "final_balance": 1571.5, "is_real_strategy": true, "losing_trades": 61, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 9.525, "strategy_name": "Reversal", "test_date": "2025-05-27T12:40:59", "timestamp": "2025-05-27T12:41:02", "total_profit": 571.5, "total_trades": 200, "win_rate": 69.5, "winning_trades": 139}, {"description": "Market reversal detection strategy", "final_balance": 1645.5, "is_real_strategy": true, "losing_trades": 57, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 21.516666666666666, "strategy_name": "Reversal", "test_date": "2025-05-29T15:58:18", "timestamp": "2025-05-29T15:58:18", "total_profit": 645.5, "total_trades": 200, "win_rate": 71.5, "winning_trades": 143}, {"description": "Market reversal detection strategy", "final_balance": 1645.5, "is_real_strategy": true, "losing_trades": 57, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 21.516666666666666, "strategy_name": "Reversal", "test_date": "2025-05-29T15:58:18", "timestamp": "2025-05-29T15:58:21", "total_profit": 645.5, "total_trades": 200, "win_rate": 71.5, "winning_trades": 143}, {"description": "Market reversal detection strategy", "final_balance": 1627, "is_real_strategy": true, "losing_trades": 58, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 20.9, "strategy_name": "Reversal", "test_date": "2025-05-29T15:58:55", "timestamp": "2025-05-29T15:58:55", "total_profit": 627, "total_trades": 200, "win_rate": 71, "winning_trades": 142}, {"description": "Market reversal detection strategy", "final_balance": 1627, "is_real_strategy": true, "losing_trades": 58, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 20.9, "strategy_name": "Reversal", "test_date": "2025-05-29T15:58:55", "timestamp": "2025-05-29T15:58:58", "total_profit": 627, "total_trades": 200, "win_rate": 71, "winning_trades": 142}]
[{"description": "Breakout trading strategy", "final_balance": 1886, "is_real_strategy": true, "losing_trades": 44, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 22.15, "strategy_name": "Breakout", "test_date": "2025-05-26T13:02:25", "timestamp": "2025-05-26T13:02:25", "total_profit": 886, "total_trades": 200, "win_rate": 78, "winning_trades": 156}, {"description": "Breakout trading strategy", "final_balance": 1886, "is_real_strategy": true, "losing_trades": 44, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 22.15, "strategy_name": "Breakout", "test_date": "2025-05-26T13:02:25", "timestamp": "2025-05-26T13:02:28", "total_profit": 886, "total_trades": 200, "win_rate": 78, "winning_trades": 156}, {"description": "Breakout trading strategy", "final_balance": 1682.5, "is_real_strategy": true, "losing_trades": 55, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 22.75, "strategy_name": "Breakout", "test_date": "2025-05-26T13:06:02", "timestamp": "2025-05-26T13:06:02", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5, "winning_trades": 145}, {"description": "Breakout trading strategy", "final_balance": 1682.5, "is_real_strategy": true, "losing_trades": 55, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 22.75, "strategy_name": "Breakout", "test_date": "2025-05-26T13:06:02", "timestamp": "2025-05-26T13:06:06", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5, "winning_trades": 145}, {"description": "Breakout trading strategy", "final_balance": 1775, "is_real_strategy": true, "losing_trades": 50, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 12.916666666666666, "strategy_name": "Breakout", "test_date": "2025-05-26T13:07:33", "timestamp": "2025-05-26T13:07:33", "total_profit": 775, "total_trades": 200, "win_rate": 75, "winning_trades": 150}, {"description": "Breakout trading strategy", "final_balance": 1775, "is_real_strategy": true, "losing_trades": 50, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 12.916666666666666, "strategy_name": "Breakout", "test_date": "2025-05-26T13:07:33", "timestamp": "2025-05-26T13:07:38", "total_profit": 775, "total_trades": 200, "win_rate": 75, "winning_trades": 150}, {"description": "Breakout trading strategy", "final_balance": 1664, "is_real_strategy": true, "losing_trades": 56, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 13.28, "strategy_name": "Breakout", "test_date": "2025-05-26T13:11:29", "timestamp": "2025-05-26T13:11:29", "total_profit": 664, "total_trades": 200, "win_rate": 72, "winning_trades": 144}, {"description": "Breakout trading strategy", "final_balance": 1664, "is_real_strategy": true, "losing_trades": 56, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 13.28, "strategy_name": "Breakout", "test_date": "2025-05-26T13:11:29", "timestamp": "2025-05-26T13:11:32", "total_profit": 664, "total_trades": 200, "win_rate": 72, "winning_trades": 144}, {"description": "Breakout trading strategy", "final_balance": 1701, "is_real_strategy": true, "losing_trades": 54, "max_drawdown": 20, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 35.05, "strategy_name": "Breakout", "test_date": "2025-05-26T13:14:02", "timestamp": "2025-05-26T13:14:02", "total_profit": 701, "total_trades": 200, "win_rate": 73, "winning_trades": 146}, {"description": "Breakout trading strategy", "final_balance": 1701, "is_real_strategy": true, "losing_trades": 54, "max_drawdown": 20, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 35.05, "strategy_name": "Breakout", "test_date": "2025-05-26T13:14:02", "timestamp": "2025-05-26T13:14:07", "total_profit": 701, "total_trades": 200, "win_rate": 73, "winning_trades": 146}, {"description": "Breakout trading strategy", "final_balance": 1682.5, "is_real_strategy": true, "losing_trades": 55, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 17.0625, "strategy_name": "Breakout", "test_date": "2025-05-26T13:15:43", "timestamp": "2025-05-26T13:15:43", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5, "winning_trades": 145}, {"description": "Breakout trading strategy", "final_balance": 1682.5, "is_real_strategy": true, "losing_trades": 55, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 17.0625, "strategy_name": "Breakout", "test_date": "2025-05-26T13:15:43", "timestamp": "2025-05-26T13:15:46", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5, "winning_trades": 145}, {"description": "Breakout trading strategy", "final_balance": 1719.5, "is_real_strategy": true, "losing_trades": 53, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 23.983333333333334, "strategy_name": "Breakout", "test_date": "2025-05-26T13:30:31", "timestamp": "2025-05-26T13:30:31", "total_profit": 719.5, "total_trades": 200, "win_rate": 73.5, "winning_trades": 147}, {"description": "Breakout trading strategy", "final_balance": 1719.5, "is_real_strategy": true, "losing_trades": 53, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 23.983333333333334, "strategy_name": "Breakout", "test_date": "2025-05-26T13:30:31", "timestamp": "2025-05-26T13:30:35", "total_profit": 719.5, "total_trades": 200, "win_rate": 73.5, "winning_trades": 147}, {"description": "Breakout trading strategy", "final_balance": 1719.5, "is_real_strategy": true, "losing_trades": 53, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 23.983333333333334, "strategy_name": "Breakout", "test_date": "2025-05-26T13:30:57", "timestamp": "2025-05-26T13:30:57", "total_profit": 719.5, "total_trades": 200, "win_rate": 73.5, "winning_trades": 147}, {"description": "Breakout trading strategy", "final_balance": 1719.5, "is_real_strategy": true, "losing_trades": 53, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 23.983333333333334, "strategy_name": "Breakout", "test_date": "2025-05-26T13:30:57", "timestamp": "2025-05-26T13:30:59", "total_profit": 719.5, "total_trades": 200, "win_rate": 73.5, "winning_trades": 147}, {"description": "Breakout trading strategy", "final_balance": 1682.5, "is_real_strategy": true, "losing_trades": 55, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 17.0625, "strategy_name": "Breakout", "test_date": "2025-05-26T13:41:23", "timestamp": "2025-05-26T13:41:23", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5, "winning_trades": 145}, {"description": "Breakout trading strategy", "final_balance": 1682.5, "is_real_strategy": true, "losing_trades": 55, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 17.0625, "strategy_name": "Breakout", "test_date": "2025-05-26T13:41:23", "timestamp": "2025-05-26T13:41:25", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5, "winning_trades": 145}, {"description": "Breakout trading strategy", "final_balance": 1793.5, "is_real_strategy": true, "losing_trades": 49, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 26.45, "strategy_name": "Breakout", "test_date": "2025-05-27T10:30:17", "timestamp": "2025-05-27T10:30:17", "total_profit": 793.5, "total_trades": 200, "win_rate": 75.5, "winning_trades": 151}, {"description": "Breakout trading strategy", "final_balance": 1793.5, "is_real_strategy": true, "losing_trades": 49, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 26.45, "strategy_name": "Breakout", "test_date": "2025-05-27T10:30:17", "timestamp": "2025-05-27T10:30:19", "total_profit": 793.5, "total_trades": 200, "win_rate": 75.5, "winning_trades": 151}, {"description": "Breakout trading strategy", "final_balance": 1719.5, "is_real_strategy": true, "losing_trades": 53, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 23.983333333333334, "strategy_name": "Breakout", "test_date": "2025-05-27T11:12:57", "timestamp": "2025-05-27T11:12:57", "total_profit": 719.5, "total_trades": 200, "win_rate": 73.5, "winning_trades": 147}, {"description": "Breakout trading strategy", "final_balance": 1719.5, "is_real_strategy": true, "losing_trades": 53, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 23.983333333333334, "strategy_name": "Breakout", "test_date": "2025-05-27T11:12:57", "timestamp": "2025-05-27T11:12:59", "total_profit": 719.5, "total_trades": 200, "win_rate": 73.5, "winning_trades": 147}, {"description": "Breakout trading strategy", "final_balance": 1590, "is_real_strategy": true, "losing_trades": 60, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 11.8, "strategy_name": "Breakout", "test_date": "2025-05-27T11:25:32", "timestamp": "2025-05-27T11:25:32", "total_profit": 590, "total_trades": 200, "win_rate": 70, "winning_trades": 140}, {"description": "Breakout trading strategy", "final_balance": 1590, "is_real_strategy": true, "losing_trades": 60, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 11.8, "strategy_name": "Breakout", "test_date": "2025-05-27T11:25:32", "timestamp": "2025-05-27T11:25:35", "total_profit": 590, "total_trades": 200, "win_rate": 70, "winning_trades": 140}, {"description": "Breakout trading strategy", "final_balance": 1460.5, "is_real_strategy": true, "losing_trades": 67, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 11.5125, "strategy_name": "Breakout", "test_date": "2025-05-27T12:00:29", "timestamp": "2025-05-27T12:00:29", "total_profit": 460.5, "total_trades": 200, "win_rate": 66.5, "winning_trades": 133}, {"description": "Breakout trading strategy", "final_balance": 1460.5, "is_real_strategy": true, "losing_trades": 67, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 11.5125, "strategy_name": "Breakout", "test_date": "2025-05-27T12:00:29", "timestamp": "2025-05-27T12:00:31", "total_profit": 460.5, "total_trades": 200, "win_rate": 66.5, "winning_trades": 133}, {"description": "Breakout trading strategy", "final_balance": 1534.5, "is_real_strategy": true, "losing_trades": 63, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 10.69, "strategy_name": "Breakout", "test_date": "2025-05-27T12:39:01", "timestamp": "2025-05-27T12:39:01", "total_profit": 534.5, "total_trades": 200, "win_rate": 68.5, "winning_trades": 137}, {"description": "Breakout trading strategy", "final_balance": 1534.5, "is_real_strategy": true, "losing_trades": 63, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 10.69, "strategy_name": "Breakout", "test_date": "2025-05-27T12:39:01", "timestamp": "2025-05-27T12:39:04", "total_profit": 534.5, "total_trades": 200, "win_rate": 68.5, "winning_trades": 137}, {"description": "Breakout trading strategy", "final_balance": 1553, "is_real_strategy": true, "losing_trades": 62, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 11.06, "strategy_name": "Breakout", "test_date": "2025-05-27T12:41:00", "timestamp": "2025-05-27T12:41:00", "total_profit": 553, "total_trades": 200, "win_rate": 69, "winning_trades": 138}, {"description": "Breakout trading strategy", "final_balance": 1553, "is_real_strategy": true, "losing_trades": 62, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 11.06, "strategy_name": "Breakout", "test_date": "2025-05-27T12:41:00", "timestamp": "2025-05-27T12:41:02", "total_profit": 553, "total_trades": 200, "win_rate": 69, "winning_trades": 138}, {"description": "Breakout trading strategy", "final_balance": 1775, "is_real_strategy": true, "losing_trades": 50, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 25.833333333333332, "strategy_name": "Breakout", "test_date": "2025-05-29T15:58:19", "timestamp": "2025-05-29T15:58:19", "total_profit": 775, "total_trades": 200, "win_rate": 75, "winning_trades": 150}, {"description": "Breakout trading strategy", "final_balance": 1775, "is_real_strategy": true, "losing_trades": 50, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 25.833333333333332, "strategy_name": "Breakout", "test_date": "2025-05-29T15:58:19", "timestamp": "2025-05-29T15:58:21", "total_profit": 775, "total_trades": 200, "win_rate": 75, "winning_trades": 150}, {"description": "Breakout trading strategy", "final_balance": 1645.5, "is_real_strategy": true, "losing_trades": 57, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 21.516666666666666, "strategy_name": "Breakout", "test_date": "2025-05-29T15:58:56", "timestamp": "2025-05-29T15:58:56", "total_profit": 645.5, "total_trades": 200, "win_rate": 71.5, "winning_trades": 143}, {"description": "Breakout trading strategy", "final_balance": 1645.5, "is_real_strategy": true, "losing_trades": 57, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 21.516666666666666, "strategy_name": "Breakout", "test_date": "2025-05-29T15:58:56", "timestamp": "2025-05-29T15:58:58", "total_profit": 645.5, "total_trades": 200, "win_rate": 71.5, "winning_trades": 143}]
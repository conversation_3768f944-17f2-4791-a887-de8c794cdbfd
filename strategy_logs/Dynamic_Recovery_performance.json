[{"description": "Dynamic adaptive recovery strategy", "final_balance": 1534.5, "is_real_strategy": true, "losing_trades": 63, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 13.3625, "strategy_name": "Dynamic Recovery", "test_date": "2025-05-26T13:02:22", "timestamp": "2025-05-26T13:02:22", "total_profit": 534.5, "total_trades": 200, "win_rate": 68.5, "winning_trades": 137}, {"description": "Dynamic adaptive recovery strategy", "final_balance": 1534.5, "is_real_strategy": true, "losing_trades": 63, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 13.3625, "strategy_name": "Dynamic Recovery", "test_date": "2025-05-26T13:02:22", "timestamp": "2025-05-26T13:02:28", "total_profit": 534.5, "total_trades": 200, "win_rate": 68.5, "winning_trades": 137}, {"description": "Dynamic adaptive recovery strategy", "final_balance": 1627, "is_real_strategy": true, "losing_trades": 58, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 15.675, "strategy_name": "Dynamic Recovery", "test_date": "2025-05-26T13:06:00", "timestamp": "2025-05-26T13:06:00", "total_profit": 627, "total_trades": 200, "win_rate": 71, "winning_trades": 142}, {"description": "Dynamic adaptive recovery strategy", "final_balance": 1627, "is_real_strategy": true, "losing_trades": 58, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 15.675, "strategy_name": "Dynamic Recovery", "test_date": "2025-05-26T13:06:00", "timestamp": "2025-05-26T13:06:06", "total_profit": 627, "total_trades": 200, "win_rate": 71, "winning_trades": 142}, {"description": "Dynamic adaptive recovery strategy", "final_balance": 1756.5, "is_real_strategy": true, "losing_trades": 51, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 15.13, "strategy_name": "Dynamic Recovery", "test_date": "2025-05-26T13:07:32", "timestamp": "2025-05-26T13:07:32", "total_profit": 756.5, "total_trades": 200, "win_rate": 74.5, "winning_trades": 149}, {"description": "Dynamic adaptive recovery strategy", "final_balance": 1756.5, "is_real_strategy": true, "losing_trades": 51, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 15.13, "strategy_name": "Dynamic Recovery", "test_date": "2025-05-26T13:07:32", "timestamp": "2025-05-26T13:07:38", "total_profit": 756.5, "total_trades": 200, "win_rate": 74.5, "winning_trades": 149}, {"description": "Dynamic adaptive recovery strategy", "final_balance": 1516, "is_real_strategy": true, "losing_trades": 64, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 17.2, "strategy_name": "Dynamic Recovery", "test_date": "2025-05-26T13:11:26", "timestamp": "2025-05-26T13:11:26", "total_profit": 516, "total_trades": 200, "win_rate": 68, "winning_trades": 136}, {"description": "Dynamic adaptive recovery strategy", "final_balance": 1516, "is_real_strategy": true, "losing_trades": 64, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 17.2, "strategy_name": "Dynamic Recovery", "test_date": "2025-05-26T13:11:26", "timestamp": "2025-05-26T13:11:32", "total_profit": 516, "total_trades": 200, "win_rate": 68, "winning_trades": 136}, {"description": "Dynamic adaptive recovery strategy", "final_balance": 1608.5, "is_real_strategy": true, "losing_trades": 59, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 12.17, "strategy_name": "Dynamic Recovery", "test_date": "2025-05-26T13:14:01", "timestamp": "2025-05-26T13:14:01", "total_profit": 608.5, "total_trades": 200, "win_rate": 70.5, "winning_trades": 141}, {"description": "Dynamic adaptive recovery strategy", "final_balance": 1608.5, "is_real_strategy": true, "losing_trades": 59, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 12.17, "strategy_name": "Dynamic Recovery", "test_date": "2025-05-26T13:14:01", "timestamp": "2025-05-26T13:14:07", "total_profit": 608.5, "total_trades": 200, "win_rate": 70.5, "winning_trades": 141}, {"description": "Dynamic adaptive recovery strategy", "final_balance": 1738, "is_real_strategy": true, "losing_trades": 52, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 24.6, "strategy_name": "Dynamic Recovery", "test_date": "2025-05-26T13:15:42", "timestamp": "2025-05-26T13:15:42", "total_profit": 738, "total_trades": 200, "win_rate": 74, "winning_trades": 148}, {"description": "Dynamic adaptive recovery strategy", "final_balance": 1738, "is_real_strategy": true, "losing_trades": 52, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 24.6, "strategy_name": "Dynamic Recovery", "test_date": "2025-05-26T13:15:42", "timestamp": "2025-05-26T13:15:46", "total_profit": 738, "total_trades": 200, "win_rate": 74, "winning_trades": 148}, {"description": "Dynamic adaptive recovery strategy", "final_balance": 1571.5, "is_real_strategy": true, "losing_trades": 61, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 14.2875, "strategy_name": "Dynamic Recovery", "test_date": "2025-05-26T13:30:29", "timestamp": "2025-05-26T13:30:29", "total_profit": 571.5, "total_trades": 200, "win_rate": 69.5, "winning_trades": 139}, {"description": "Dynamic adaptive recovery strategy", "final_balance": 1571.5, "is_real_strategy": true, "losing_trades": 61, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 14.2875, "strategy_name": "Dynamic Recovery", "test_date": "2025-05-26T13:30:29", "timestamp": "2025-05-26T13:30:35", "total_profit": 571.5, "total_trades": 200, "win_rate": 69.5, "winning_trades": 139}, {"description": "Dynamic adaptive recovery strategy", "final_balance": 1571.5, "is_real_strategy": true, "losing_trades": 61, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 14.2875, "strategy_name": "Dynamic Recovery", "test_date": "2025-05-26T13:30:55", "timestamp": "2025-05-26T13:30:55", "total_profit": 571.5, "total_trades": 200, "win_rate": 69.5, "winning_trades": 139}, {"description": "Dynamic adaptive recovery strategy", "final_balance": 1571.5, "is_real_strategy": true, "losing_trades": 61, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 14.2875, "strategy_name": "Dynamic Recovery", "test_date": "2025-05-26T13:30:55", "timestamp": "2025-05-26T13:30:59", "total_profit": 571.5, "total_trades": 200, "win_rate": 69.5, "winning_trades": 139}, {"description": "Dynamic adaptive recovery strategy", "final_balance": 1497.5, "is_real_strategy": true, "losing_trades": 65, "max_drawdown": 80, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 6.21875, "strategy_name": "Dynamic Recovery", "test_date": "2025-05-26T13:41:21", "timestamp": "2025-05-26T13:41:21", "total_profit": 497.5, "total_trades": 200, "win_rate": 67.5, "winning_trades": 135}, {"description": "Dynamic adaptive recovery strategy", "final_balance": 1497.5, "is_real_strategy": true, "losing_trades": 65, "max_drawdown": 80, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 6.21875, "strategy_name": "Dynamic Recovery", "test_date": "2025-05-26T13:41:21", "timestamp": "2025-05-26T13:41:25", "total_profit": 497.5, "total_trades": 200, "win_rate": 67.5, "winning_trades": 135}, {"description": "Dynamic adaptive recovery strategy", "final_balance": 1682.5, "is_real_strategy": true, "losing_trades": 55, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 17.0625, "strategy_name": "Dynamic Recovery", "test_date": "2025-05-27T10:30:15", "timestamp": "2025-05-27T10:30:15", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5, "winning_trades": 145}, {"description": "Dynamic adaptive recovery strategy", "final_balance": 1682.5, "is_real_strategy": true, "losing_trades": 55, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 17.0625, "strategy_name": "Dynamic Recovery", "test_date": "2025-05-27T10:30:15", "timestamp": "2025-05-27T10:30:19", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5, "winning_trades": 145}, {"description": "Dynamic adaptive recovery strategy", "final_balance": 1590, "is_real_strategy": true, "losing_trades": 60, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 14.75, "strategy_name": "Dynamic Recovery", "test_date": "2025-05-27T11:12:55", "timestamp": "2025-05-27T11:12:55", "total_profit": 590, "total_trades": 200, "win_rate": 70, "winning_trades": 140}, {"description": "Dynamic adaptive recovery strategy", "final_balance": 1590, "is_real_strategy": true, "losing_trades": 60, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 14.75, "strategy_name": "Dynamic Recovery", "test_date": "2025-05-27T11:12:55", "timestamp": "2025-05-27T11:12:59", "total_profit": 590, "total_trades": 200, "win_rate": 70, "winning_trades": 140}, {"description": "Dynamic adaptive recovery strategy", "final_balance": 1793.5, "is_real_strategy": true, "losing_trades": 49, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 26.45, "strategy_name": "Dynamic Recovery", "test_date": "2025-05-27T11:25:31", "timestamp": "2025-05-27T11:25:31", "total_profit": 793.5, "total_trades": 200, "win_rate": 75.5, "winning_trades": 151}, {"description": "Dynamic adaptive recovery strategy", "final_balance": 1793.5, "is_real_strategy": true, "losing_trades": 49, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 26.45, "strategy_name": "Dynamic Recovery", "test_date": "2025-05-27T11:25:31", "timestamp": "2025-05-27T11:25:35", "total_profit": 793.5, "total_trades": 200, "win_rate": 75.5, "winning_trades": 151}, {"description": "Dynamic adaptive recovery strategy", "final_balance": 1738, "is_real_strategy": true, "losing_trades": 52, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 12.3, "strategy_name": "Dynamic Recovery", "test_date": "2025-05-27T12:00:27", "timestamp": "2025-05-27T12:00:27", "total_profit": 738, "total_trades": 200, "win_rate": 74, "winning_trades": 148}, {"description": "Dynamic adaptive recovery strategy", "final_balance": 1738, "is_real_strategy": true, "losing_trades": 52, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 12.3, "strategy_name": "Dynamic Recovery", "test_date": "2025-05-27T12:00:27", "timestamp": "2025-05-27T12:00:31", "total_profit": 738, "total_trades": 200, "win_rate": 74, "winning_trades": 148}, {"description": "Dynamic adaptive recovery strategy", "final_balance": 1719.5, "is_real_strategy": true, "losing_trades": 53, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 14.39, "strategy_name": "Dynamic Recovery", "test_date": "2025-05-27T12:39:00", "timestamp": "2025-05-27T12:39:00", "total_profit": 719.5, "total_trades": 200, "win_rate": 73.5, "winning_trades": 147}, {"description": "Dynamic adaptive recovery strategy", "final_balance": 1719.5, "is_real_strategy": true, "losing_trades": 53, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 14.39, "strategy_name": "Dynamic Recovery", "test_date": "2025-05-27T12:39:00", "timestamp": "2025-05-27T12:39:04", "total_profit": 719.5, "total_trades": 200, "win_rate": 73.5, "winning_trades": 147}, {"description": "Dynamic adaptive recovery strategy", "final_balance": 1534.5, "is_real_strategy": true, "losing_trades": 63, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 13.3625, "strategy_name": "Dynamic Recovery", "test_date": "2025-05-27T12:40:58", "timestamp": "2025-05-27T12:40:58", "total_profit": 534.5, "total_trades": 200, "win_rate": 68.5, "winning_trades": 137}, {"description": "Dynamic adaptive recovery strategy", "final_balance": 1534.5, "is_real_strategy": true, "losing_trades": 63, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 13.3625, "strategy_name": "Dynamic Recovery", "test_date": "2025-05-27T12:40:58", "timestamp": "2025-05-27T12:41:02", "total_profit": 534.5, "total_trades": 200, "win_rate": 68.5, "winning_trades": 137}, {"description": "Dynamic adaptive recovery strategy", "final_balance": 1738, "is_real_strategy": true, "losing_trades": 52, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 12.3, "strategy_name": "Dynamic Recovery", "test_date": "2025-05-29T15:58:17", "timestamp": "2025-05-29T15:58:17", "total_profit": 738, "total_trades": 200, "win_rate": 74, "winning_trades": 148}, {"description": "Dynamic adaptive recovery strategy", "final_balance": 1738, "is_real_strategy": true, "losing_trades": 52, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 12.3, "strategy_name": "Dynamic Recovery", "test_date": "2025-05-29T15:58:17", "timestamp": "2025-05-29T15:58:21", "total_profit": 738, "total_trades": 200, "win_rate": 74, "winning_trades": 148}, {"description": "Dynamic adaptive recovery strategy", "final_balance": 1793.5, "is_real_strategy": true, "losing_trades": 49, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 26.45, "strategy_name": "Dynamic Recovery", "test_date": "2025-05-29T15:58:54", "timestamp": "2025-05-29T15:58:54", "total_profit": 793.5, "total_trades": 200, "win_rate": 75.5, "winning_trades": 151}, {"description": "Dynamic adaptive recovery strategy", "final_balance": 1793.5, "is_real_strategy": true, "losing_trades": 49, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 26.45, "strategy_name": "Dynamic Recovery", "test_date": "2025-05-29T15:58:54", "timestamp": "2025-05-29T15:58:58", "total_profit": 793.5, "total_trades": 200, "win_rate": 75.5, "winning_trades": 151}]
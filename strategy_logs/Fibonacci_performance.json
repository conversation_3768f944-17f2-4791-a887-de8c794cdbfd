[{"description": "<PERSON>bon<PERSON><PERSON> sequence recovery strategy", "final_balance": 1571.5, "is_real_strategy": true, "losing_trades": 61, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 14.2875, "strategy_name": "<PERSON><PERSON><PERSON><PERSON>", "test_date": "2025-05-26T13:02:21", "timestamp": "2025-05-26T13:02:21", "total_profit": 571.5, "total_trades": 200, "win_rate": 69.5, "winning_trades": 139}, {"description": "<PERSON>bon<PERSON><PERSON> sequence recovery strategy", "final_balance": 1571.5, "is_real_strategy": true, "losing_trades": 61, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 14.2875, "strategy_name": "<PERSON><PERSON><PERSON><PERSON>", "test_date": "2025-05-26T13:02:21", "timestamp": "2025-05-26T13:02:28", "total_profit": 571.5, "total_trades": 200, "win_rate": 69.5, "winning_trades": 139}, {"description": "<PERSON>bon<PERSON><PERSON> sequence recovery strategy", "final_balance": 1275.5, "is_real_strategy": true, "losing_trades": 77, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 6.8875, "strategy_name": "<PERSON><PERSON><PERSON><PERSON>", "test_date": "2025-05-26T13:06:00", "timestamp": "2025-05-26T13:06:00", "total_profit": 275.5, "total_trades": 200, "win_rate": 61.5, "winning_trades": 123}, {"description": "<PERSON>bon<PERSON><PERSON> sequence recovery strategy", "final_balance": 1275.5, "is_real_strategy": true, "losing_trades": 77, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 6.8875, "strategy_name": "<PERSON><PERSON><PERSON><PERSON>", "test_date": "2025-05-26T13:06:00", "timestamp": "2025-05-26T13:06:06", "total_profit": 275.5, "total_trades": 200, "win_rate": 61.5, "winning_trades": 123}, {"description": "<PERSON>bon<PERSON><PERSON> sequence recovery strategy", "final_balance": 1571.5, "is_real_strategy": true, "losing_trades": 61, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 19.05, "strategy_name": "<PERSON><PERSON><PERSON><PERSON>", "test_date": "2025-05-26T13:07:31", "timestamp": "2025-05-26T13:07:31", "total_profit": 571.5, "total_trades": 200, "win_rate": 69.5, "winning_trades": 139}, {"description": "<PERSON>bon<PERSON><PERSON> sequence recovery strategy", "final_balance": 1571.5, "is_real_strategy": true, "losing_trades": 61, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 19.05, "strategy_name": "<PERSON><PERSON><PERSON><PERSON>", "test_date": "2025-05-26T13:07:31", "timestamp": "2025-05-26T13:07:38", "total_profit": 571.5, "total_trades": 200, "win_rate": 69.5, "winning_trades": 139}, {"description": "<PERSON>bon<PERSON><PERSON> sequence recovery strategy", "final_balance": 1294, "is_real_strategy": true, "losing_trades": 76, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 7.35, "strategy_name": "<PERSON><PERSON><PERSON><PERSON>", "test_date": "2025-05-26T13:11:25", "timestamp": "2025-05-26T13:11:25", "total_profit": 294, "total_trades": 200, "win_rate": 62, "winning_trades": 124}, {"description": "<PERSON>bon<PERSON><PERSON> sequence recovery strategy", "final_balance": 1294, "is_real_strategy": true, "losing_trades": 76, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 7.35, "strategy_name": "<PERSON><PERSON><PERSON><PERSON>", "test_date": "2025-05-26T13:11:25", "timestamp": "2025-05-26T13:11:32", "total_profit": 294, "total_trades": 200, "win_rate": 62, "winning_trades": 124}, {"description": "<PERSON>bon<PERSON><PERSON> sequence recovery strategy", "final_balance": 1423.5, "is_real_strategy": true, "losing_trades": 69, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 10.5875, "strategy_name": "<PERSON><PERSON><PERSON><PERSON>", "test_date": "2025-05-26T13:14:00", "timestamp": "2025-05-26T13:14:00", "total_profit": 423.5, "total_trades": 200, "win_rate": 65.5, "winning_trades": 131}, {"description": "<PERSON>bon<PERSON><PERSON> sequence recovery strategy", "final_balance": 1423.5, "is_real_strategy": true, "losing_trades": 69, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 10.5875, "strategy_name": "<PERSON><PERSON><PERSON><PERSON>", "test_date": "2025-05-26T13:14:00", "timestamp": "2025-05-26T13:14:07", "total_profit": 423.5, "total_trades": 200, "win_rate": 65.5, "winning_trades": 131}, {"description": "<PERSON>bon<PERSON><PERSON> sequence recovery strategy", "final_balance": 1386.5, "is_real_strategy": true, "losing_trades": 71, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 7.73, "strategy_name": "<PERSON><PERSON><PERSON><PERSON>", "test_date": "2025-05-26T13:15:41", "timestamp": "2025-05-26T13:15:41", "total_profit": 386.5, "total_trades": 200, "win_rate": 64.5, "winning_trades": 129}, {"description": "<PERSON>bon<PERSON><PERSON> sequence recovery strategy", "final_balance": 1386.5, "is_real_strategy": true, "losing_trades": 71, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 7.73, "strategy_name": "<PERSON><PERSON><PERSON><PERSON>", "test_date": "2025-05-26T13:15:41", "timestamp": "2025-05-26T13:15:46", "total_profit": 386.5, "total_trades": 200, "win_rate": 64.5, "winning_trades": 129}, {"description": "<PERSON>bon<PERSON><PERSON> sequence recovery strategy", "final_balance": 1312.5, "is_real_strategy": true, "losing_trades": 75, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 7.8125, "strategy_name": "<PERSON><PERSON><PERSON><PERSON>", "test_date": "2025-05-26T13:30:29", "timestamp": "2025-05-26T13:30:29", "total_profit": 312.5, "total_trades": 200, "win_rate": 62.5, "winning_trades": 125}, {"description": "<PERSON>bon<PERSON><PERSON> sequence recovery strategy", "final_balance": 1312.5, "is_real_strategy": true, "losing_trades": 75, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 7.8125, "strategy_name": "<PERSON><PERSON><PERSON><PERSON>", "test_date": "2025-05-26T13:30:29", "timestamp": "2025-05-26T13:30:35", "total_profit": 312.5, "total_trades": 200, "win_rate": 62.5, "winning_trades": 125}, {"description": "<PERSON>bon<PERSON><PERSON> sequence recovery strategy", "final_balance": 1590, "is_real_strategy": true, "losing_trades": 60, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 11.8, "strategy_name": "<PERSON><PERSON><PERSON><PERSON>", "test_date": "2025-05-26T13:30:52", "timestamp": "2025-05-26T13:30:52", "total_profit": 590, "total_trades": 200, "win_rate": 70, "winning_trades": 140}, {"description": "<PERSON>bon<PERSON><PERSON> sequence recovery strategy", "final_balance": 1590, "is_real_strategy": true, "losing_trades": 60, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 11.8, "strategy_name": "<PERSON><PERSON><PERSON><PERSON>", "test_date": "2025-05-26T13:30:52", "timestamp": "2025-05-26T13:30:59", "total_profit": 590, "total_trades": 200, "win_rate": 70, "winning_trades": 140}, {"description": "<PERSON>bon<PERSON><PERSON> sequence recovery strategy", "final_balance": 1275.5, "is_real_strategy": true, "losing_trades": 77, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 5.51, "strategy_name": "<PERSON><PERSON><PERSON><PERSON>", "test_date": "2025-05-26T13:41:21", "timestamp": "2025-05-26T13:41:21", "total_profit": 275.5, "total_trades": 200, "win_rate": 61.5, "winning_trades": 123}, {"description": "<PERSON>bon<PERSON><PERSON> sequence recovery strategy", "final_balance": 1275.5, "is_real_strategy": true, "losing_trades": 77, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 5.51, "strategy_name": "<PERSON><PERSON><PERSON><PERSON>", "test_date": "2025-05-26T13:41:21", "timestamp": "2025-05-26T13:41:25", "total_profit": 275.5, "total_trades": 200, "win_rate": 61.5, "winning_trades": 123}, {"description": "<PERSON>bon<PERSON><PERSON> sequence recovery strategy", "final_balance": 1238.5, "is_real_strategy": true, "losing_trades": 79, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 5.9625, "strategy_name": "<PERSON><PERSON><PERSON><PERSON>", "test_date": "2025-05-27T10:05:23", "timestamp": "2025-05-27T10:05:23", "total_profit": 238.5, "total_trades": 200, "win_rate": 60.5, "winning_trades": 121}, {"description": "<PERSON>bon<PERSON><PERSON> sequence recovery strategy", "final_balance": 1423.5, "is_real_strategy": true, "losing_trades": 69, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 10.5875, "strategy_name": "<PERSON><PERSON><PERSON><PERSON>", "test_date": "2025-05-27T10:30:14", "timestamp": "2025-05-27T10:30:14", "total_profit": 423.5, "total_trades": 200, "win_rate": 65.5, "winning_trades": 131}, {"description": "<PERSON>bon<PERSON><PERSON> sequence recovery strategy", "final_balance": 1423.5, "is_real_strategy": true, "losing_trades": 69, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 10.5875, "strategy_name": "<PERSON><PERSON><PERSON><PERSON>", "test_date": "2025-05-27T10:30:14", "timestamp": "2025-05-27T10:30:19", "total_profit": 423.5, "total_trades": 200, "win_rate": 65.5, "winning_trades": 131}, {"description": "<PERSON>bon<PERSON><PERSON> sequence recovery strategy", "final_balance": 1516, "is_real_strategy": true, "losing_trades": 64, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 17.2, "strategy_name": "<PERSON><PERSON><PERSON><PERSON>", "test_date": "2025-05-27T11:12:54", "timestamp": "2025-05-27T11:12:54", "total_profit": 516, "total_trades": 200, "win_rate": 68, "winning_trades": 136}, {"description": "<PERSON>bon<PERSON><PERSON> sequence recovery strategy", "final_balance": 1516, "is_real_strategy": true, "losing_trades": 64, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 17.2, "strategy_name": "<PERSON><PERSON><PERSON><PERSON>", "test_date": "2025-05-27T11:12:54", "timestamp": "2025-05-27T11:12:59", "total_profit": 516, "total_trades": 200, "win_rate": 68, "winning_trades": 136}, {"description": "<PERSON>bon<PERSON><PERSON> sequence recovery strategy", "final_balance": 1294, "is_real_strategy": true, "losing_trades": 76, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 4.9, "strategy_name": "<PERSON><PERSON><PERSON><PERSON>", "test_date": "2025-05-27T11:25:30", "timestamp": "2025-05-27T11:25:30", "total_profit": 294, "total_trades": 200, "win_rate": 62, "winning_trades": 124}, {"description": "<PERSON>bon<PERSON><PERSON> sequence recovery strategy", "final_balance": 1294, "is_real_strategy": true, "losing_trades": 76, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 4.9, "strategy_name": "<PERSON><PERSON><PERSON><PERSON>", "test_date": "2025-05-27T11:25:30", "timestamp": "2025-05-27T11:25:35", "total_profit": 294, "total_trades": 200, "win_rate": 62, "winning_trades": 124}, {"description": "<PERSON>bon<PERSON><PERSON> sequence recovery strategy", "final_balance": 1238.5, "is_real_strategy": true, "losing_trades": 79, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 5.9625, "strategy_name": "<PERSON><PERSON><PERSON><PERSON>", "test_date": "2025-05-27T12:00:26", "timestamp": "2025-05-27T12:00:26", "total_profit": 238.5, "total_trades": 200, "win_rate": 60.5, "winning_trades": 121}, {"description": "<PERSON>bon<PERSON><PERSON> sequence recovery strategy", "final_balance": 1238.5, "is_real_strategy": true, "losing_trades": 79, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 5.9625, "strategy_name": "<PERSON><PERSON><PERSON><PERSON>", "test_date": "2025-05-27T12:00:26", "timestamp": "2025-05-27T12:00:31", "total_profit": 238.5, "total_trades": 200, "win_rate": 60.5, "winning_trades": 121}, {"description": "<PERSON>bon<PERSON><PERSON> sequence recovery strategy", "final_balance": 1201.5, "is_real_strategy": true, "losing_trades": 81, "max_drawdown": 80, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 2.51875, "strategy_name": "<PERSON><PERSON><PERSON><PERSON>", "test_date": "2025-05-27T12:38:59", "timestamp": "2025-05-27T12:38:59", "total_profit": 201.5, "total_trades": 200, "win_rate": 59.5, "winning_trades": 119}, {"description": "<PERSON>bon<PERSON><PERSON> sequence recovery strategy", "final_balance": 1201.5, "is_real_strategy": true, "losing_trades": 81, "max_drawdown": 80, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 2.51875, "strategy_name": "<PERSON><PERSON><PERSON><PERSON>", "test_date": "2025-05-27T12:38:59", "timestamp": "2025-05-27T12:39:04", "total_profit": 201.5, "total_trades": 200, "win_rate": 59.5, "winning_trades": 119}, {"description": "<PERSON>bon<PERSON><PERSON> sequence recovery strategy", "final_balance": 1238.5, "is_real_strategy": true, "losing_trades": 79, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 3.975, "strategy_name": "<PERSON><PERSON><PERSON><PERSON>", "test_date": "2025-05-27T12:40:57", "timestamp": "2025-05-27T12:40:57", "total_profit": 238.5, "total_trades": 200, "win_rate": 60.5, "winning_trades": 121}, {"description": "<PERSON>bon<PERSON><PERSON> sequence recovery strategy", "final_balance": 1238.5, "is_real_strategy": true, "losing_trades": 79, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 3.975, "strategy_name": "<PERSON><PERSON><PERSON><PERSON>", "test_date": "2025-05-27T12:40:57", "timestamp": "2025-05-27T12:41:02", "total_profit": 238.5, "total_trades": 200, "win_rate": 60.5, "winning_trades": 121}, {"description": "<PERSON>bon<PERSON><PERSON> sequence recovery strategy", "final_balance": 1386.5, "is_real_strategy": true, "losing_trades": 71, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 7.73, "strategy_name": "<PERSON><PERSON><PERSON><PERSON>", "test_date": "2025-05-29T15:58:16", "timestamp": "2025-05-29T15:58:16", "total_profit": 386.5, "total_trades": 200, "win_rate": 64.5, "winning_trades": 129}, {"description": "<PERSON>bon<PERSON><PERSON> sequence recovery strategy", "final_balance": 1386.5, "is_real_strategy": true, "losing_trades": 71, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 7.73, "strategy_name": "<PERSON><PERSON><PERSON><PERSON>", "test_date": "2025-05-29T15:58:16", "timestamp": "2025-05-29T15:58:21", "total_profit": 386.5, "total_trades": 200, "win_rate": 64.5, "winning_trades": 129}, {"description": "<PERSON>bon<PERSON><PERSON> sequence recovery strategy", "final_balance": 1423.5, "is_real_strategy": true, "losing_trades": 69, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 7.058333333333334, "strategy_name": "<PERSON><PERSON><PERSON><PERSON>", "test_date": "2025-05-29T15:58:53", "timestamp": "2025-05-29T15:58:53", "total_profit": 423.5, "total_trades": 200, "win_rate": 65.5, "winning_trades": 131}, {"description": "<PERSON>bon<PERSON><PERSON> sequence recovery strategy", "final_balance": 1423.5, "is_real_strategy": true, "losing_trades": 69, "max_drawdown": 60, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 7.058333333333334, "strategy_name": "<PERSON><PERSON><PERSON><PERSON>", "test_date": "2025-05-29T15:58:53", "timestamp": "2025-05-29T15:58:58", "total_profit": 423.5, "total_trades": 200, "win_rate": 65.5, "winning_trades": 131}]
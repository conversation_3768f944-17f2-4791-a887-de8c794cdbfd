[{"description": "Classic Martingale recovery strategy - doubles bet after loss", "final_balance": 1423.5, "is_real_strategy": true, "losing_trades": 69, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 8.47, "strategy_name": "<PERSON><PERSON><PERSON>", "test_date": "2025-05-26T13:02:20", "timestamp": "2025-05-26T13:02:20", "total_profit": 423.5, "total_trades": 200, "win_rate": 65.5, "winning_trades": 131}, {"description": "Classic Martingale recovery strategy - doubles bet after loss", "final_balance": 1423.5, "is_real_strategy": true, "losing_trades": 69, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 8.47, "strategy_name": "<PERSON><PERSON><PERSON>", "test_date": "2025-05-26T13:02:20", "timestamp": "2025-05-26T13:02:28", "total_profit": 423.5, "total_trades": 200, "win_rate": 65.5, "winning_trades": 131}, {"description": "Classic Martingale recovery strategy - doubles bet after loss", "final_balance": 1516, "is_real_strategy": true, "losing_trades": 64, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 12.9, "strategy_name": "<PERSON><PERSON><PERSON>", "test_date": "2025-05-26T13:05:59", "timestamp": "2025-05-26T13:05:59", "total_profit": 516, "total_trades": 200, "win_rate": 68, "winning_trades": 136}, {"description": "Classic Martingale recovery strategy - doubles bet after loss", "final_balance": 1516, "is_real_strategy": true, "losing_trades": 64, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 12.9, "strategy_name": "<PERSON><PERSON><PERSON>", "test_date": "2025-05-26T13:05:59", "timestamp": "2025-05-26T13:06:06", "total_profit": 516, "total_trades": 200, "win_rate": 68, "winning_trades": 136}, {"description": "Classic Martingale recovery strategy - doubles bet after loss", "final_balance": 1682.5, "is_real_strategy": true, "losing_trades": 55, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 22.75, "strategy_name": "<PERSON><PERSON><PERSON>", "test_date": "2025-05-26T13:07:30", "timestamp": "2025-05-26T13:07:30", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5, "winning_trades": 145}, {"description": "Classic Martingale recovery strategy - doubles bet after loss", "final_balance": 1682.5, "is_real_strategy": true, "losing_trades": 55, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 22.75, "strategy_name": "<PERSON><PERSON><PERSON>", "test_date": "2025-05-26T13:07:30", "timestamp": "2025-05-26T13:07:38", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5, "winning_trades": 145}, {"description": "Classic Martingale recovery strategy - doubles bet after loss", "final_balance": 1756.5, "is_real_strategy": true, "losing_trades": 51, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 25.216666666666665, "strategy_name": "<PERSON><PERSON><PERSON>", "test_date": "2025-05-26T13:11:24", "timestamp": "2025-05-26T13:11:24", "total_profit": 756.5, "total_trades": 200, "win_rate": 74.5, "winning_trades": 149}, {"description": "Classic Martingale recovery strategy - doubles bet after loss", "final_balance": 1756.5, "is_real_strategy": true, "losing_trades": 51, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 25.216666666666665, "strategy_name": "<PERSON><PERSON><PERSON>", "test_date": "2025-05-26T13:11:24", "timestamp": "2025-05-26T13:11:32", "total_profit": 756.5, "total_trades": 200, "win_rate": 74.5, "winning_trades": 149}, {"description": "Classic Martingale recovery strategy - doubles bet after loss", "final_balance": 1738, "is_real_strategy": true, "losing_trades": 52, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 18.45, "strategy_name": "<PERSON><PERSON><PERSON>", "test_date": "2025-05-26T13:13:59", "timestamp": "2025-05-26T13:13:59", "total_profit": 738, "total_trades": 200, "win_rate": 74, "winning_trades": 148}, {"description": "Classic Martingale recovery strategy - doubles bet after loss", "final_balance": 1738, "is_real_strategy": true, "losing_trades": 52, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 18.45, "strategy_name": "<PERSON><PERSON><PERSON>", "test_date": "2025-05-26T13:13:59", "timestamp": "2025-05-26T13:14:07", "total_profit": 738, "total_trades": 200, "win_rate": 74, "winning_trades": 148}, {"description": "Classic Martingale recovery strategy - doubles bet after loss", "final_balance": 1608.5, "is_real_strategy": true, "losing_trades": 59, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 15.2125, "strategy_name": "<PERSON><PERSON><PERSON>", "test_date": "2025-05-26T13:15:40", "timestamp": "2025-05-26T13:15:40", "total_profit": 608.5, "total_trades": 200, "win_rate": 70.5, "winning_trades": 141}, {"description": "Classic Martingale recovery strategy - doubles bet after loss", "final_balance": 1608.5, "is_real_strategy": true, "losing_trades": 59, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 15.2125, "strategy_name": "<PERSON><PERSON><PERSON>", "test_date": "2025-05-26T13:15:40", "timestamp": "2025-05-26T13:15:46", "total_profit": 608.5, "total_trades": 200, "win_rate": 70.5, "winning_trades": 141}, {"description": "Classic Martingale recovery strategy - doubles bet after loss", "final_balance": 1701, "is_real_strategy": true, "losing_trades": 54, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 23.366666666666667, "strategy_name": "<PERSON><PERSON><PERSON>", "test_date": "2025-05-26T13:30:28", "timestamp": "2025-05-26T13:30:28", "total_profit": 701, "total_trades": 200, "win_rate": 73, "winning_trades": 146}, {"description": "Classic Martingale recovery strategy - doubles bet after loss", "final_balance": 1701, "is_real_strategy": true, "losing_trades": 54, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 23.366666666666667, "strategy_name": "<PERSON><PERSON><PERSON>", "test_date": "2025-05-26T13:30:28", "timestamp": "2025-05-26T13:30:35", "total_profit": 701, "total_trades": 200, "win_rate": 73, "winning_trades": 146}, {"description": "Classic Martingale recovery strategy - doubles bet after loss", "final_balance": 1571.5, "is_real_strategy": true, "losing_trades": 61, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 11.43, "strategy_name": "<PERSON><PERSON><PERSON>", "test_date": "2025-05-26T13:30:51", "timestamp": "2025-05-26T13:30:51", "total_profit": 571.5, "total_trades": 200, "win_rate": 69.5, "winning_trades": 139}, {"description": "Classic Martingale recovery strategy - doubles bet after loss", "final_balance": 1571.5, "is_real_strategy": true, "losing_trades": 61, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 11.43, "strategy_name": "<PERSON><PERSON><PERSON>", "test_date": "2025-05-26T13:30:51", "timestamp": "2025-05-26T13:30:59", "total_profit": 571.5, "total_trades": 200, "win_rate": 69.5, "winning_trades": 139}, {"description": "Classic Martingale recovery strategy - doubles bet after loss", "final_balance": 1682.5, "is_real_strategy": true, "losing_trades": 55, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 17.0625, "strategy_name": "<PERSON><PERSON><PERSON>", "test_date": "2025-05-26T13:41:20", "timestamp": "2025-05-26T13:41:20", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5, "winning_trades": 145}, {"description": "Classic Martingale recovery strategy - doubles bet after loss", "final_balance": 1682.5, "is_real_strategy": true, "losing_trades": 55, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 17.0625, "strategy_name": "<PERSON><PERSON><PERSON>", "test_date": "2025-05-26T13:41:20", "timestamp": "2025-05-26T13:41:25", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5, "winning_trades": 145}, {"description": "Classic Martingale recovery strategy - doubles bet after loss", "final_balance": 1497.5, "is_real_strategy": true, "losing_trades": 65, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 9.95, "strategy_name": "<PERSON><PERSON><PERSON>", "test_date": "2025-05-27T10:05:03", "timestamp": "2025-05-27T10:05:03", "total_profit": 497.5, "total_trades": 200, "win_rate": 67.5, "winning_trades": 135}, {"description": "Classic Martingale recovery strategy - doubles bet after loss", "final_balance": 1682.5, "is_real_strategy": true, "losing_trades": 55, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 22.75, "strategy_name": "<PERSON><PERSON><PERSON>", "test_date": "2025-05-27T10:30:13", "timestamp": "2025-05-27T10:30:13", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5, "winning_trades": 145}, {"description": "Classic Martingale recovery strategy - doubles bet after loss", "final_balance": 1682.5, "is_real_strategy": true, "losing_trades": 55, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 22.75, "strategy_name": "<PERSON><PERSON><PERSON>", "test_date": "2025-05-27T10:30:13", "timestamp": "2025-05-27T10:30:19", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5, "winning_trades": 145}, {"description": "Classic Martingale recovery strategy - doubles bet after loss", "final_balance": 1331, "is_real_strategy": true, "losing_trades": 74, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 6.62, "strategy_name": "<PERSON><PERSON><PERSON>", "test_date": "2025-05-27T11:12:53", "timestamp": "2025-05-27T11:12:53", "total_profit": 331, "total_trades": 200, "win_rate": 63, "winning_trades": 126}, {"description": "Classic Martingale recovery strategy - doubles bet after loss", "final_balance": 1331, "is_real_strategy": true, "losing_trades": 74, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 6.62, "strategy_name": "<PERSON><PERSON><PERSON>", "test_date": "2025-05-27T11:12:53", "timestamp": "2025-05-27T11:12:59", "total_profit": 331, "total_trades": 200, "win_rate": 63, "winning_trades": 126}, {"description": "Classic Martingale recovery strategy - doubles bet after loss", "final_balance": 1257, "is_real_strategy": true, "losing_trades": 78, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 6.425, "strategy_name": "<PERSON><PERSON><PERSON>", "test_date": "2025-05-27T11:25:29", "timestamp": "2025-05-27T11:25:29", "total_profit": 257, "total_trades": 200, "win_rate": 61, "winning_trades": 122}, {"description": "Classic Martingale recovery strategy - doubles bet after loss", "final_balance": 1257, "is_real_strategy": true, "losing_trades": 78, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 6.425, "strategy_name": "<PERSON><PERSON><PERSON>", "test_date": "2025-05-27T11:25:29", "timestamp": "2025-05-27T11:25:35", "total_profit": 257, "total_trades": 200, "win_rate": 61, "winning_trades": 122}, {"description": "Classic Martingale recovery strategy - doubles bet after loss", "final_balance": 1516, "is_real_strategy": true, "losing_trades": 64, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 17.2, "strategy_name": "<PERSON><PERSON><PERSON>", "test_date": "2025-05-27T12:00:25", "timestamp": "2025-05-27T12:00:25", "total_profit": 516, "total_trades": 200, "win_rate": 68, "winning_trades": 136}, {"description": "Classic Martingale recovery strategy - doubles bet after loss", "final_balance": 1516, "is_real_strategy": true, "losing_trades": 64, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 17.2, "strategy_name": "<PERSON><PERSON><PERSON>", "test_date": "2025-05-27T12:00:25", "timestamp": "2025-05-27T12:00:31", "total_profit": 516, "total_trades": 200, "win_rate": 68, "winning_trades": 136}, {"description": "Classic Martingale recovery strategy - doubles bet after loss", "final_balance": 1719.5, "is_real_strategy": true, "losing_trades": 53, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 17.9875, "strategy_name": "<PERSON><PERSON><PERSON>", "test_date": "2025-05-27T12:38:57", "timestamp": "2025-05-27T12:38:57", "total_profit": 719.5, "total_trades": 200, "win_rate": 73.5, "winning_trades": 147}, {"description": "Classic Martingale recovery strategy - doubles bet after loss", "final_balance": 1719.5, "is_real_strategy": true, "losing_trades": 53, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 17.9875, "strategy_name": "<PERSON><PERSON><PERSON>", "test_date": "2025-05-27T12:38:57", "timestamp": "2025-05-27T12:39:04", "total_profit": 719.5, "total_trades": 200, "win_rate": 73.5, "winning_trades": 147}, {"description": "Classic Martingale recovery strategy - doubles bet after loss", "final_balance": 1590, "is_real_strategy": true, "losing_trades": 60, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 11.8, "strategy_name": "<PERSON><PERSON><PERSON>", "test_date": "2025-05-27T12:40:56", "timestamp": "2025-05-27T12:40:56", "total_profit": 590, "total_trades": 200, "win_rate": 70, "winning_trades": 140}, {"description": "Classic Martingale recovery strategy - doubles bet after loss", "final_balance": 1590, "is_real_strategy": true, "losing_trades": 60, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 11.8, "strategy_name": "<PERSON><PERSON><PERSON>", "test_date": "2025-05-27T12:40:56", "timestamp": "2025-05-27T12:41:02", "total_profit": 590, "total_trades": 200, "win_rate": 70, "winning_trades": 140}, {"description": "Classic Martingale recovery strategy - doubles bet after loss", "final_balance": 1405, "is_real_strategy": true, "losing_trades": 70, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 8.1, "strategy_name": "<PERSON><PERSON><PERSON>", "test_date": "2025-05-29T15:58:15", "timestamp": "2025-05-29T15:58:15", "total_profit": 405, "total_trades": 200, "win_rate": 65, "winning_trades": 130}, {"description": "Classic Martingale recovery strategy - doubles bet after loss", "final_balance": 1405, "is_real_strategy": true, "losing_trades": 70, "max_drawdown": 50, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 8.1, "strategy_name": "<PERSON><PERSON><PERSON>", "test_date": "2025-05-29T15:58:15", "timestamp": "2025-05-29T15:58:21", "total_profit": 405, "total_trades": 200, "win_rate": 65, "winning_trades": 130}, {"description": "Classic Martingale recovery strategy - doubles bet after loss", "final_balance": 1719.5, "is_real_strategy": true, "losing_trades": 53, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 23.983333333333334, "strategy_name": "<PERSON><PERSON><PERSON>", "test_date": "2025-05-29T15:58:52", "timestamp": "2025-05-29T15:58:52", "total_profit": 719.5, "total_trades": 200, "win_rate": 73.5, "winning_trades": 147}, {"description": "Classic Martingale recovery strategy - doubles bet after loss", "final_balance": 1719.5, "is_real_strategy": true, "losing_trades": 53, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "High", "sharpe_ratio": 23.983333333333334, "strategy_name": "<PERSON><PERSON><PERSON>", "test_date": "2025-05-29T15:58:52", "timestamp": "2025-05-29T15:58:58", "total_profit": 719.5, "total_trades": 200, "win_rate": 73.5, "winning_trades": 147}]
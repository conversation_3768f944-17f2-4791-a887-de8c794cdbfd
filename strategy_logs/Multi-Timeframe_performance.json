[{"description": "Multi-timeframe analysis strategy", "final_balance": 1904.5, "is_real_strategy": true, "losing_trades": 43, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 30.15, "strategy_name": "Multi-Timeframe", "test_date": "2025-05-26T13:02:27", "timestamp": "2025-05-26T13:02:27", "total_profit": 904.5, "total_trades": 200, "win_rate": 78.5, "winning_trades": 157}, {"description": "Multi-timeframe analysis strategy", "final_balance": 1904.5, "is_real_strategy": true, "losing_trades": 43, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 30.15, "strategy_name": "Multi-Timeframe", "test_date": "2025-05-26T13:02:27", "timestamp": "2025-05-26T13:02:28", "total_profit": 904.5, "total_trades": 200, "win_rate": 78.5, "winning_trades": 157}, {"description": "Multi-timeframe analysis strategy", "final_balance": 1867.5, "is_real_strategy": true, "losing_trades": 45, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 28.916666666666668, "strategy_name": "Multi-Timeframe", "test_date": "2025-05-26T13:06:06", "timestamp": "2025-05-26T13:06:06", "total_profit": 867.5, "total_trades": 200, "win_rate": 77.5, "winning_trades": 155}, {"description": "Multi-timeframe analysis strategy", "final_balance": 1867.5, "is_real_strategy": true, "losing_trades": 45, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 28.916666666666668, "strategy_name": "Multi-Timeframe", "test_date": "2025-05-26T13:06:06", "timestamp": "2025-05-26T13:06:06", "total_profit": 867.5, "total_trades": 200, "win_rate": 77.5, "winning_trades": 155}, {"description": "Multi-timeframe analysis strategy", "final_balance": 1756.5, "is_real_strategy": true, "losing_trades": 51, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 18.9125, "strategy_name": "Multi-Timeframe", "test_date": "2025-05-26T13:07:37", "timestamp": "2025-05-26T13:07:37", "total_profit": 756.5, "total_trades": 200, "win_rate": 74.5, "winning_trades": 149}, {"description": "Multi-timeframe analysis strategy", "final_balance": 1756.5, "is_real_strategy": true, "losing_trades": 51, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 18.9125, "strategy_name": "Multi-Timeframe", "test_date": "2025-05-26T13:07:37", "timestamp": "2025-05-26T13:07:38", "total_profit": 756.5, "total_trades": 200, "win_rate": 74.5, "winning_trades": 149}, {"description": "Multi-timeframe analysis strategy", "final_balance": 1923, "is_real_strategy": true, "losing_trades": 42, "max_drawdown": 20, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 46.15, "strategy_name": "Multi-Timeframe", "test_date": "2025-05-26T13:11:31", "timestamp": "2025-05-26T13:11:31", "total_profit": 923, "total_trades": 200, "win_rate": 79, "winning_trades": 158}, {"description": "Multi-timeframe analysis strategy", "final_balance": 1923, "is_real_strategy": true, "losing_trades": 42, "max_drawdown": 20, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 46.15, "strategy_name": "Multi-Timeframe", "test_date": "2025-05-26T13:11:31", "timestamp": "2025-05-26T13:11:32", "total_profit": 923, "total_trades": 200, "win_rate": 79, "winning_trades": 158}, {"description": "Multi-timeframe analysis strategy", "final_balance": 1849, "is_real_strategy": true, "losing_trades": 46, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 28.3, "strategy_name": "Multi-Timeframe", "test_date": "2025-05-26T13:14:06", "timestamp": "2025-05-26T13:14:06", "total_profit": 849, "total_trades": 200, "win_rate": 77, "winning_trades": 154}, {"description": "Multi-timeframe analysis strategy", "final_balance": 1849, "is_real_strategy": true, "losing_trades": 46, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 28.3, "strategy_name": "Multi-Timeframe", "test_date": "2025-05-26T13:14:06", "timestamp": "2025-05-26T13:14:07", "total_profit": 849, "total_trades": 200, "win_rate": 77, "winning_trades": 154}, {"description": "Multi-timeframe analysis strategy", "final_balance": 1886, "is_real_strategy": true, "losing_trades": 44, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 29.533333333333335, "strategy_name": "Multi-Timeframe", "test_date": "2025-05-26T13:15:45", "timestamp": "2025-05-26T13:15:45", "total_profit": 886, "total_trades": 200, "win_rate": 78, "winning_trades": 156}, {"description": "Multi-timeframe analysis strategy", "final_balance": 1886, "is_real_strategy": true, "losing_trades": 44, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 29.533333333333335, "strategy_name": "Multi-Timeframe", "test_date": "2025-05-26T13:15:45", "timestamp": "2025-05-26T13:15:46", "total_profit": 886, "total_trades": 200, "win_rate": 78, "winning_trades": 156}, {"description": "Multi-timeframe analysis strategy", "final_balance": 1719.5, "is_real_strategy": true, "losing_trades": 53, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 23.983333333333334, "strategy_name": "Multi-Timeframe", "test_date": "2025-05-26T13:30:35", "timestamp": "2025-05-26T13:30:35", "total_profit": 719.5, "total_trades": 200, "win_rate": 73.5, "winning_trades": 147}, {"description": "Multi-timeframe analysis strategy", "final_balance": 1719.5, "is_real_strategy": true, "losing_trades": 53, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 23.983333333333334, "strategy_name": "Multi-Timeframe", "test_date": "2025-05-26T13:30:35", "timestamp": "2025-05-26T13:30:35", "total_profit": 719.5, "total_trades": 200, "win_rate": 73.5, "winning_trades": 147}, {"description": "Multi-timeframe analysis strategy", "final_balance": 1812, "is_real_strategy": true, "losing_trades": 48, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 20.3, "strategy_name": "Multi-Timeframe", "test_date": "2025-05-26T13:30:58", "timestamp": "2025-05-26T13:30:58", "total_profit": 812, "total_trades": 200, "win_rate": 76, "winning_trades": 152}, {"description": "Multi-timeframe analysis strategy", "final_balance": 1812, "is_real_strategy": true, "losing_trades": 48, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 20.3, "strategy_name": "Multi-Timeframe", "test_date": "2025-05-26T13:30:58", "timestamp": "2025-05-26T13:30:59", "total_profit": 812, "total_trades": 200, "win_rate": 76, "winning_trades": 152}, {"description": "Multi-timeframe analysis strategy", "final_balance": 1867.5, "is_real_strategy": true, "losing_trades": 45, "max_drawdown": 20, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 43.375, "strategy_name": "Multi-Timeframe", "test_date": "2025-05-26T13:41:25", "timestamp": "2025-05-26T13:41:25", "total_profit": 867.5, "total_trades": 200, "win_rate": 77.5, "winning_trades": 155}, {"description": "Multi-timeframe analysis strategy", "final_balance": 1867.5, "is_real_strategy": true, "losing_trades": 45, "max_drawdown": 20, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 43.375, "strategy_name": "Multi-Timeframe", "test_date": "2025-05-26T13:41:25", "timestamp": "2025-05-26T13:41:25", "total_profit": 867.5, "total_trades": 200, "win_rate": 77.5, "winning_trades": 155}, {"description": "Multi-timeframe analysis strategy", "final_balance": 1867.5, "is_real_strategy": true, "losing_trades": 45, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 21.6875, "strategy_name": "Multi-Timeframe", "test_date": "2025-05-27T10:30:19", "timestamp": "2025-05-27T10:30:19", "total_profit": 867.5, "total_trades": 200, "win_rate": 77.5, "winning_trades": 155}, {"description": "Multi-timeframe analysis strategy", "final_balance": 1867.5, "is_real_strategy": true, "losing_trades": 45, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 21.6875, "strategy_name": "Multi-Timeframe", "test_date": "2025-05-27T10:30:19", "timestamp": "2025-05-27T10:30:19", "total_profit": 867.5, "total_trades": 200, "win_rate": 77.5, "winning_trades": 155}, {"description": "Multi-timeframe analysis strategy", "final_balance": 1719.5, "is_real_strategy": true, "losing_trades": 53, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 23.983333333333334, "strategy_name": "Multi-Timeframe", "test_date": "2025-05-27T11:12:59", "timestamp": "2025-05-27T11:12:59", "total_profit": 719.5, "total_trades": 200, "win_rate": 73.5, "winning_trades": 147}, {"description": "Multi-timeframe analysis strategy", "final_balance": 1719.5, "is_real_strategy": true, "losing_trades": 53, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 23.983333333333334, "strategy_name": "Multi-Timeframe", "test_date": "2025-05-27T11:12:59", "timestamp": "2025-05-27T11:12:59", "total_profit": 719.5, "total_trades": 200, "win_rate": 73.5, "winning_trades": 147}, {"description": "Multi-timeframe analysis strategy", "final_balance": 1830.5, "is_real_strategy": true, "losing_trades": 47, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 27.683333333333334, "strategy_name": "Multi-Timeframe", "test_date": "2025-05-27T11:25:34", "timestamp": "2025-05-27T11:25:34", "total_profit": 830.5, "total_trades": 200, "win_rate": 76.5, "winning_trades": 153}, {"description": "Multi-timeframe analysis strategy", "final_balance": 1830.5, "is_real_strategy": true, "losing_trades": 47, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 27.683333333333334, "strategy_name": "Multi-Timeframe", "test_date": "2025-05-27T11:25:34", "timestamp": "2025-05-27T11:25:35", "total_profit": 830.5, "total_trades": 200, "win_rate": 76.5, "winning_trades": 153}, {"description": "Multi-timeframe analysis strategy", "final_balance": 1812, "is_real_strategy": true, "losing_trades": 48, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 27.066666666666666, "strategy_name": "Multi-Timeframe", "test_date": "2025-05-27T12:00:31", "timestamp": "2025-05-27T12:00:31", "total_profit": 812, "total_trades": 200, "win_rate": 76, "winning_trades": 152}, {"description": "Multi-timeframe analysis strategy", "final_balance": 1812, "is_real_strategy": true, "losing_trades": 48, "max_drawdown": 30, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 27.066666666666666, "strategy_name": "Multi-Timeframe", "test_date": "2025-05-27T12:00:31", "timestamp": "2025-05-27T12:00:31", "total_profit": 812, "total_trades": 200, "win_rate": 76, "winning_trades": 152}, {"description": "Multi-timeframe analysis strategy", "final_balance": 1960, "is_real_strategy": true, "losing_trades": 40, "max_drawdown": 20, "meets_target": true, "profit_factor": 2, "recommendation": "Meets target range - ready for live trading", "risk_level": "Medium", "sharpe_ratio": 48, "strategy_name": "Multi-Timeframe", "test_date": "2025-05-27T12:39:03", "timestamp": "2025-05-27T12:39:03", "total_profit": 960, "total_trades": 200, "win_rate": 80, "winning_trades": 160}, {"description": "Multi-timeframe analysis strategy", "final_balance": 1960, "is_real_strategy": true, "losing_trades": 40, "max_drawdown": 20, "meets_target": true, "profit_factor": 2, "recommendation": "Meets target range - ready for live trading", "risk_level": "Medium", "sharpe_ratio": 48, "strategy_name": "Multi-Timeframe", "test_date": "2025-05-27T12:39:03", "timestamp": "2025-05-27T12:39:04", "total_profit": 960, "total_trades": 200, "win_rate": 80, "winning_trades": 160}, {"description": "Multi-timeframe analysis strategy", "final_balance": 1812, "is_real_strategy": true, "losing_trades": 48, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 20.3, "strategy_name": "Multi-Timeframe", "test_date": "2025-05-27T12:41:02", "timestamp": "2025-05-27T12:41:02", "total_profit": 812, "total_trades": 200, "win_rate": 76, "winning_trades": 152}, {"description": "Multi-timeframe analysis strategy", "final_balance": 1812, "is_real_strategy": true, "losing_trades": 48, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 20.3, "strategy_name": "Multi-Timeframe", "test_date": "2025-05-27T12:41:02", "timestamp": "2025-05-27T12:41:02", "total_profit": 812, "total_trades": 200, "win_rate": 76, "winning_trades": 152}, {"description": "Multi-timeframe analysis strategy", "final_balance": 2015.5, "is_real_strategy": true, "losing_trades": 37, "max_drawdown": 30, "meets_target": true, "profit_factor": 2, "recommendation": "Meets target range - ready for live trading", "risk_level": "Medium", "sharpe_ratio": 33.85, "strategy_name": "Multi-Timeframe", "test_date": "2025-05-29T15:58:21", "timestamp": "2025-05-29T15:58:21", "total_profit": 1015.5, "total_trades": 200, "win_rate": 81.5, "winning_trades": 163}, {"description": "Multi-timeframe analysis strategy", "final_balance": 2015.5, "is_real_strategy": true, "losing_trades": 37, "max_drawdown": 30, "meets_target": true, "profit_factor": 2, "recommendation": "Meets target range - ready for live trading", "risk_level": "Medium", "sharpe_ratio": 33.85, "strategy_name": "Multi-Timeframe", "test_date": "2025-05-29T15:58:21", "timestamp": "2025-05-29T15:58:21", "total_profit": 1015.5, "total_trades": 200, "win_rate": 81.5, "winning_trades": 163}, {"description": "Multi-timeframe analysis strategy", "final_balance": 1775, "is_real_strategy": true, "losing_trades": 50, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 19.375, "strategy_name": "Multi-Timeframe", "test_date": "2025-05-29T15:58:58", "timestamp": "2025-05-29T15:58:58", "total_profit": 775, "total_trades": 200, "win_rate": 75, "winning_trades": 150}, {"description": "Multi-timeframe analysis strategy", "final_balance": 1775, "is_real_strategy": true, "losing_trades": 50, "max_drawdown": 40, "meets_target": false, "profit_factor": 2, "recommendation": "Needs optimization - win rate below target", "risk_level": "Medium", "sharpe_ratio": 19.375, "strategy_name": "Multi-Timeframe", "test_date": "2025-05-29T15:58:58", "timestamp": "2025-05-29T15:58:58", "total_profit": 775, "total_trades": 200, "win_rate": 75, "winning_trades": 150}]
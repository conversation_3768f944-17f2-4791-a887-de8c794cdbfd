[{"overall_win_rate": 69.70833333333333, "session_type": "full_strategy_test", "strategies": [{"meets_target": false, "name": "<PERSON><PERSON><PERSON>", "risk_level": "High", "total_profit": 423.5, "total_trades": 200, "win_rate": 65.5}, {"meets_target": false, "name": "Anti-Martingale", "risk_level": "Medium", "total_profit": 312.5, "total_trades": 200, "win_rate": 62.5}, {"meets_target": false, "name": "<PERSON><PERSON><PERSON><PERSON>", "risk_level": "Medium", "total_profit": 571.5, "total_trades": 200, "win_rate": 69.5}, {"meets_target": false, "name": "Percentage Recovery", "risk_level": "Low", "total_profit": 35, "total_trades": 200, "win_rate": 55.00000000000001}, {"meets_target": false, "name": "Dynamic Recovery", "risk_level": "Medium", "total_profit": 534.5, "total_trades": 200, "win_rate": 68.5}, {"meets_target": false, "name": "Trend Following", "risk_level": "Medium", "total_profit": 812, "total_trades": 200, "win_rate": 76}, {"meets_target": false, "name": "Reversal", "risk_level": "Medium", "total_profit": 627, "total_trades": 200, "win_rate": 71}, {"meets_target": false, "name": "Breakout", "risk_level": "High", "total_profit": 886, "total_trades": 200, "win_rate": 78}, {"meets_target": false, "name": "Momentum", "risk_level": "Medium", "total_profit": 608.5, "total_trades": 200, "win_rate": 70.5}, {"meets_target": false, "name": "Advanced Strategy", "risk_level": "Medium", "total_profit": 423.5, "total_trades": 200, "win_rate": 65.5}, {"meets_target": false, "name": "Pattern Recognition", "risk_level": "Low", "total_profit": 812, "total_trades": 200, "win_rate": 76}, {"meets_target": false, "name": "Multi-Timeframe", "risk_level": "Medium", "total_profit": 904.5, "total_trades": 200, "win_rate": 78.5}], "strategies_tested": 12, "timestamp": "2025-05-26T13:02:28"}, {"overall_win_rate": 70.875, "session_type": "full_strategy_test", "strategies": [{"meets_target": false, "name": "<PERSON><PERSON><PERSON>", "risk_level": "High", "total_profit": 516, "total_trades": 200, "win_rate": 68}, {"meets_target": false, "name": "Anti-Martingale", "risk_level": "Medium", "total_profit": 386.5, "total_trades": 200, "win_rate": 64.5}, {"meets_target": false, "name": "<PERSON><PERSON><PERSON><PERSON>", "risk_level": "Medium", "total_profit": 275.5, "total_trades": 200, "win_rate": 61.5}, {"meets_target": false, "name": "Percentage Recovery", "risk_level": "Low", "total_profit": 368, "total_trades": 200, "win_rate": 64}, {"meets_target": false, "name": "Dynamic Recovery", "risk_level": "Medium", "total_profit": 627, "total_trades": 200, "win_rate": 71}, {"meets_target": false, "name": "Trend Following", "risk_level": "Medium", "total_profit": 738, "total_trades": 200, "win_rate": 74}, {"meets_target": false, "name": "Reversal", "risk_level": "Medium", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5}, {"meets_target": false, "name": "Breakout", "risk_level": "High", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5}, {"meets_target": false, "name": "Momentum", "risk_level": "Medium", "total_profit": 516, "total_trades": 200, "win_rate": 68}, {"meets_target": false, "name": "Advanced Strategy", "risk_level": "Medium", "total_profit": 664, "total_trades": 200, "win_rate": 72}, {"meets_target": true, "name": "Pattern Recognition", "risk_level": "Low", "total_profit": 1145, "total_trades": 200, "win_rate": 85}, {"meets_target": false, "name": "Multi-Timeframe", "risk_level": "Medium", "total_profit": 867.5, "total_trades": 200, "win_rate": 77.5}], "strategies_tested": 12, "timestamp": "2025-05-26T13:06:06"}, {"overall_win_rate": 70.83333333333333, "session_type": "full_strategy_test", "strategies": [{"meets_target": false, "name": "<PERSON><PERSON><PERSON>", "risk_level": "High", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5}, {"meets_target": false, "name": "Anti-Martingale", "risk_level": "Medium", "total_profit": 201.5, "total_trades": 200, "win_rate": 59.5}, {"meets_target": false, "name": "<PERSON><PERSON><PERSON><PERSON>", "risk_level": "Medium", "total_profit": 571.5, "total_trades": 200, "win_rate": 69.5}, {"meets_target": false, "name": "Percentage Recovery", "risk_level": "Low", "total_profit": 460.5, "total_trades": 200, "win_rate": 66.5}, {"meets_target": false, "name": "Dynamic Recovery", "risk_level": "Medium", "total_profit": 756.5, "total_trades": 200, "win_rate": 74.5}, {"meets_target": false, "name": "Trend Following", "risk_level": "Medium", "total_profit": 756.5, "total_trades": 200, "win_rate": 74.5}, {"meets_target": false, "name": "Reversal", "risk_level": "Medium", "total_profit": 516, "total_trades": 200, "win_rate": 68}, {"meets_target": false, "name": "Breakout", "risk_level": "High", "total_profit": 775, "total_trades": 200, "win_rate": 75}, {"meets_target": false, "name": "Momentum", "risk_level": "Medium", "total_profit": 571.5, "total_trades": 200, "win_rate": 69.5}, {"meets_target": false, "name": "Advanced Strategy", "risk_level": "Medium", "total_profit": 497.5, "total_trades": 200, "win_rate": 67.5}, {"meets_target": false, "name": "Pattern Recognition", "risk_level": "Low", "total_profit": 904.5, "total_trades": 200, "win_rate": 78.5}, {"meets_target": false, "name": "Multi-Timeframe", "risk_level": "Medium", "total_profit": 756.5, "total_trades": 200, "win_rate": 74.5}], "strategies_tested": 12, "timestamp": "2025-05-26T13:07:38"}, {"overall_win_rate": 70.91666666666667, "session_type": "full_strategy_test", "strategies": [{"meets_target": false, "name": "<PERSON><PERSON><PERSON>", "risk_level": "High", "total_profit": 756.5, "total_trades": 200, "win_rate": 74.5}, {"meets_target": false, "name": "Anti-Martingale", "risk_level": "Medium", "total_profit": 479, "total_trades": 200, "win_rate": 67}, {"meets_target": false, "name": "<PERSON><PERSON><PERSON><PERSON>", "risk_level": "Medium", "total_profit": 294, "total_trades": 200, "win_rate": 62}, {"meets_target": false, "name": "Percentage Recovery", "risk_level": "Low", "total_profit": 183, "total_trades": 200, "win_rate": 59}, {"meets_target": false, "name": "Dynamic Recovery", "risk_level": "Medium", "total_profit": 516, "total_trades": 200, "win_rate": 68}, {"meets_target": false, "name": "Trend Following", "risk_level": "Medium", "total_profit": 664, "total_trades": 200, "win_rate": 72}, {"meets_target": false, "name": "Reversal", "risk_level": "Medium", "total_profit": 645.5, "total_trades": 200, "win_rate": 71.5}, {"meets_target": false, "name": "Breakout", "risk_level": "High", "total_profit": 664, "total_trades": 200, "win_rate": 72}, {"meets_target": false, "name": "Momentum", "risk_level": "Medium", "total_profit": 627, "total_trades": 200, "win_rate": 71}, {"meets_target": false, "name": "Advanced Strategy", "risk_level": "Medium", "total_profit": 719.5, "total_trades": 200, "win_rate": 73.5}, {"meets_target": true, "name": "Pattern Recognition", "risk_level": "Low", "total_profit": 1015.5, "total_trades": 200, "win_rate": 81.5}, {"meets_target": false, "name": "Multi-Timeframe", "risk_level": "Medium", "total_profit": 923, "total_trades": 200, "win_rate": 79}], "strategies_tested": 12, "timestamp": "2025-05-26T13:11:32"}, {"overall_win_rate": 71.25, "session_type": "full_strategy_test", "strategies": [{"meets_target": false, "name": "<PERSON><PERSON><PERSON>", "risk_level": "High", "total_profit": 738, "total_trades": 200, "win_rate": 74}, {"meets_target": false, "name": "Anti-Martingale", "risk_level": "Medium", "total_profit": 294, "total_trades": 200, "win_rate": 62}, {"meets_target": false, "name": "<PERSON><PERSON><PERSON><PERSON>", "risk_level": "Medium", "total_profit": 423.5, "total_trades": 200, "win_rate": 65.5}, {"meets_target": false, "name": "Percentage Recovery", "risk_level": "Low", "total_profit": 497.5, "total_trades": 200, "win_rate": 67.5}, {"meets_target": false, "name": "Dynamic Recovery", "risk_level": "Medium", "total_profit": 608.5, "total_trades": 200, "win_rate": 70.5}, {"meets_target": false, "name": "Trend Following", "risk_level": "Medium", "total_profit": 867.5, "total_trades": 200, "win_rate": 77.5}, {"meets_target": false, "name": "Reversal", "risk_level": "Medium", "total_profit": 645.5, "total_trades": 200, "win_rate": 71.5}, {"meets_target": false, "name": "Breakout", "risk_level": "High", "total_profit": 701, "total_trades": 200, "win_rate": 73}, {"meets_target": false, "name": "Momentum", "risk_level": "Medium", "total_profit": 553, "total_trades": 200, "win_rate": 69}, {"meets_target": false, "name": "Advanced Strategy", "risk_level": "Medium", "total_profit": 553, "total_trades": 200, "win_rate": 69}, {"meets_target": false, "name": "Pattern Recognition", "risk_level": "Low", "total_profit": 904.5, "total_trades": 200, "win_rate": 78.5}, {"meets_target": false, "name": "Multi-Timeframe", "risk_level": "Medium", "total_profit": 849, "total_trades": 200, "win_rate": 77}], "strategies_tested": 12, "timestamp": "2025-05-26T13:14:07"}, {"overall_win_rate": 70.125, "session_type": "full_strategy_test", "strategies": [{"meets_target": false, "name": "<PERSON><PERSON><PERSON>", "risk_level": "High", "total_profit": 608.5, "total_trades": 200, "win_rate": 70.5}, {"meets_target": false, "name": "Anti-Martingale", "risk_level": "Medium", "total_profit": 312.5, "total_trades": 200, "win_rate": 62.5}, {"meets_target": false, "name": "<PERSON><PERSON><PERSON><PERSON>", "risk_level": "Medium", "total_profit": 386.5, "total_trades": 200, "win_rate": 64.5}, {"meets_target": false, "name": "Percentage Recovery", "risk_level": "Low", "total_profit": 423.5, "total_trades": 200, "win_rate": 65.5}, {"meets_target": false, "name": "Dynamic Recovery", "risk_level": "Medium", "total_profit": 738, "total_trades": 200, "win_rate": 74}, {"meets_target": false, "name": "Trend Following", "risk_level": "Medium", "total_profit": 812, "total_trades": 200, "win_rate": 76}, {"meets_target": false, "name": "Reversal", "risk_level": "Medium", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5}, {"meets_target": false, "name": "Breakout", "risk_level": "High", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5}, {"meets_target": false, "name": "Momentum", "risk_level": "Medium", "total_profit": 553, "total_trades": 200, "win_rate": 69}, {"meets_target": false, "name": "Advanced Strategy", "risk_level": "Medium", "total_profit": 146, "total_trades": 200, "win_rate": 57.99999999999999}, {"meets_target": false, "name": "Pattern Recognition", "risk_level": "Low", "total_profit": 904.5, "total_trades": 200, "win_rate": 78.5}, {"meets_target": false, "name": "Multi-Timeframe", "risk_level": "Medium", "total_profit": 886, "total_trades": 200, "win_rate": 78}], "strategies_tested": 12, "timestamp": "2025-05-26T13:15:46"}, {"overall_win_rate": 69.5, "session_type": "full_strategy_test", "strategies": [{"meets_target": false, "name": "<PERSON><PERSON><PERSON>", "risk_level": "High", "total_profit": 701, "total_trades": 200, "win_rate": 73}, {"meets_target": false, "name": "Anti-Martingale", "risk_level": "Medium", "total_profit": 183, "total_trades": 200, "win_rate": 59}, {"meets_target": false, "name": "<PERSON><PERSON><PERSON><PERSON>", "risk_level": "Medium", "total_profit": 312.5, "total_trades": 200, "win_rate": 62.5}, {"meets_target": false, "name": "Percentage Recovery", "risk_level": "Low", "total_profit": 294, "total_trades": 200, "win_rate": 62}, {"meets_target": false, "name": "Dynamic Recovery", "risk_level": "Medium", "total_profit": 571.5, "total_trades": 200, "win_rate": 69.5}, {"meets_target": false, "name": "Trend Following", "risk_level": "Medium", "total_profit": 775, "total_trades": 200, "win_rate": 75}, {"meets_target": false, "name": "Reversal", "risk_level": "Medium", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5}, {"meets_target": false, "name": "Breakout", "risk_level": "High", "total_profit": 719.5, "total_trades": 200, "win_rate": 73.5}, {"meets_target": false, "name": "Momentum", "risk_level": "Medium", "total_profit": 386.5, "total_trades": 200, "win_rate": 64.5}, {"meets_target": false, "name": "Advanced Strategy", "risk_level": "Medium", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5}, {"meets_target": false, "name": "Pattern Recognition", "risk_level": "Low", "total_profit": 830.5, "total_trades": 200, "win_rate": 76.5}, {"meets_target": false, "name": "Multi-Timeframe", "risk_level": "Medium", "total_profit": 719.5, "total_trades": 200, "win_rate": 73.5}], "strategies_tested": 12, "timestamp": "2025-05-26T13:30:35"}, {"overall_win_rate": 69.91666666666667, "session_type": "full_strategy_test", "strategies": [{"meets_target": false, "name": "<PERSON><PERSON><PERSON>", "risk_level": "High", "total_profit": 571.5, "total_trades": 200, "win_rate": 69.5}, {"meets_target": false, "name": "Anti-Martingale", "risk_level": "Medium", "total_profit": 386.5, "total_trades": 200, "win_rate": 64.5}, {"meets_target": false, "name": "<PERSON><PERSON><PERSON><PERSON>", "risk_level": "Medium", "total_profit": 590, "total_trades": 200, "win_rate": 70}, {"meets_target": false, "name": "Percentage Recovery", "risk_level": "Low", "total_profit": 534.5, "total_trades": 200, "win_rate": 68.5}, {"meets_target": false, "name": "Dynamic Recovery", "risk_level": "Medium", "total_profit": 571.5, "total_trades": 200, "win_rate": 69.5}, {"meets_target": false, "name": "Trend Following", "risk_level": "Medium", "total_profit": 793.5, "total_trades": 200, "win_rate": 75.5}, {"meets_target": false, "name": "Reversal", "risk_level": "Medium", "total_profit": 571.5, "total_trades": 200, "win_rate": 69.5}, {"meets_target": false, "name": "Breakout", "risk_level": "High", "total_profit": 719.5, "total_trades": 200, "win_rate": 73.5}, {"meets_target": false, "name": "Momentum", "risk_level": "Medium", "total_profit": 312.5, "total_trades": 200, "win_rate": 62.5}, {"meets_target": false, "name": "Advanced Strategy", "risk_level": "Medium", "total_profit": 460.5, "total_trades": 200, "win_rate": 66.5}, {"meets_target": false, "name": "Pattern Recognition", "risk_level": "Low", "total_profit": 719.5, "total_trades": 200, "win_rate": 73.5}, {"meets_target": false, "name": "Multi-Timeframe", "risk_level": "Medium", "total_profit": 812, "total_trades": 200, "win_rate": 76}], "strategies_tested": 12, "timestamp": "2025-05-26T13:30:59"}, {"overall_win_rate": 70.75, "session_type": "full_strategy_test", "strategies": [{"meets_target": false, "name": "<PERSON><PERSON><PERSON>", "risk_level": "High", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5}, {"meets_target": false, "name": "Anti-Martingale", "risk_level": "Medium", "total_profit": 368, "total_trades": 200, "win_rate": 64}, {"meets_target": false, "name": "<PERSON><PERSON><PERSON><PERSON>", "risk_level": "Medium", "total_profit": 275.5, "total_trades": 200, "win_rate": 61.5}, {"meets_target": false, "name": "Percentage Recovery", "risk_level": "Low", "total_profit": 442, "total_trades": 200, "win_rate": 66}, {"meets_target": false, "name": "Dynamic Recovery", "risk_level": "Medium", "total_profit": 497.5, "total_trades": 200, "win_rate": 67.5}, {"meets_target": false, "name": "Trend Following", "risk_level": "Medium", "total_profit": 904.5, "total_trades": 200, "win_rate": 78.5}, {"meets_target": false, "name": "Reversal", "risk_level": "Medium", "total_profit": 701, "total_trades": 200, "win_rate": 73}, {"meets_target": false, "name": "Breakout", "risk_level": "High", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5}, {"meets_target": false, "name": "Momentum", "risk_level": "Medium", "total_profit": 571.5, "total_trades": 200, "win_rate": 69.5}, {"meets_target": false, "name": "Advanced Strategy", "risk_level": "Medium", "total_profit": 553, "total_trades": 200, "win_rate": 69}, {"meets_target": false, "name": "Pattern Recognition", "risk_level": "Low", "total_profit": 867.5, "total_trades": 200, "win_rate": 77.5}, {"meets_target": false, "name": "Multi-Timeframe", "risk_level": "Medium", "total_profit": 867.5, "total_trades": 200, "win_rate": 77.5}], "strategies_tested": 12, "timestamp": "2025-05-26T13:41:25"}, {"overall_win_rate": 71.29166666666667, "session_type": "full_strategy_test", "strategies": [{"meets_target": false, "name": "<PERSON><PERSON><PERSON>", "risk_level": "High", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5}, {"meets_target": false, "name": "Anti-Martingale", "risk_level": "Medium", "total_profit": 294, "total_trades": 200, "win_rate": 62}, {"meets_target": false, "name": "<PERSON><PERSON><PERSON><PERSON>", "risk_level": "Medium", "total_profit": 423.5, "total_trades": 200, "win_rate": 65.5}, {"meets_target": false, "name": "Percentage Recovery", "risk_level": "Low", "total_profit": 405, "total_trades": 200, "win_rate": 65}, {"meets_target": false, "name": "Dynamic Recovery", "risk_level": "Medium", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5}, {"meets_target": false, "name": "Trend Following", "risk_level": "Medium", "total_profit": 719.5, "total_trades": 200, "win_rate": 73.5}, {"meets_target": false, "name": "Reversal", "risk_level": "Medium", "total_profit": 701, "total_trades": 200, "win_rate": 73}, {"meets_target": false, "name": "Breakout", "risk_level": "High", "total_profit": 793.5, "total_trades": 200, "win_rate": 75.5}, {"meets_target": false, "name": "Momentum", "risk_level": "Medium", "total_profit": 608.5, "total_trades": 200, "win_rate": 70.5}, {"meets_target": false, "name": "Advanced Strategy", "risk_level": "Medium", "total_profit": 553, "total_trades": 200, "win_rate": 69}, {"meets_target": false, "name": "Pattern Recognition", "risk_level": "Low", "total_profit": 923, "total_trades": 200, "win_rate": 79}, {"meets_target": false, "name": "Multi-Timeframe", "risk_level": "Medium", "total_profit": 867.5, "total_trades": 200, "win_rate": 77.5}], "strategies_tested": 12, "timestamp": "2025-05-27T10:30:19"}, {"overall_win_rate": 69.66666666666667, "session_type": "full_strategy_test", "strategies": [{"meets_target": false, "name": "<PERSON><PERSON><PERSON>", "risk_level": "High", "total_profit": 331, "total_trades": 200, "win_rate": 63}, {"meets_target": false, "name": "Anti-Martingale", "risk_level": "Medium", "total_profit": 238.5, "total_trades": 200, "win_rate": 60.5}, {"meets_target": false, "name": "<PERSON><PERSON><PERSON><PERSON>", "risk_level": "Medium", "total_profit": 516, "total_trades": 200, "win_rate": 68}, {"meets_target": false, "name": "Percentage Recovery", "risk_level": "Low", "total_profit": 497.5, "total_trades": 200, "win_rate": 67.5}, {"meets_target": false, "name": "Dynamic Recovery", "risk_level": "Medium", "total_profit": 590, "total_trades": 200, "win_rate": 70}, {"meets_target": false, "name": "Trend Following", "risk_level": "Medium", "total_profit": 793.5, "total_trades": 200, "win_rate": 75.5}, {"meets_target": false, "name": "Reversal", "risk_level": "Medium", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5}, {"meets_target": false, "name": "Breakout", "risk_level": "High", "total_profit": 719.5, "total_trades": 200, "win_rate": 73.5}, {"meets_target": false, "name": "Momentum", "risk_level": "Medium", "total_profit": 423.5, "total_trades": 200, "win_rate": 65.5}, {"meets_target": false, "name": "Advanced Strategy", "risk_level": "Medium", "total_profit": 534.5, "total_trades": 200, "win_rate": 68.5}, {"meets_target": false, "name": "Pattern Recognition", "risk_level": "Low", "total_profit": 886, "total_trades": 200, "win_rate": 78}, {"meets_target": false, "name": "Multi-Timeframe", "risk_level": "Medium", "total_profit": 719.5, "total_trades": 200, "win_rate": 73.5}], "strategies_tested": 12, "timestamp": "2025-05-27T11:12:59"}, {"overall_win_rate": 68.79166666666667, "session_type": "full_strategy_test", "strategies": [{"meets_target": false, "name": "<PERSON><PERSON><PERSON>", "risk_level": "High", "total_profit": 257, "total_trades": 200, "win_rate": 61}, {"meets_target": false, "name": "Anti-Martingale", "risk_level": "Medium", "total_profit": 331, "total_trades": 200, "win_rate": 63}, {"meets_target": false, "name": "<PERSON><PERSON><PERSON><PERSON>", "risk_level": "Medium", "total_profit": 294, "total_trades": 200, "win_rate": 62}, {"meets_target": false, "name": "Percentage Recovery", "risk_level": "Low", "total_profit": 238.5, "total_trades": 200, "win_rate": 60.5}, {"meets_target": false, "name": "Dynamic Recovery", "risk_level": "Medium", "total_profit": 793.5, "total_trades": 200, "win_rate": 75.5}, {"meets_target": false, "name": "Trend Following", "risk_level": "Medium", "total_profit": 627, "total_trades": 200, "win_rate": 71}, {"meets_target": false, "name": "Reversal", "risk_level": "Medium", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5}, {"meets_target": false, "name": "Breakout", "risk_level": "High", "total_profit": 590, "total_trades": 200, "win_rate": 70}, {"meets_target": false, "name": "Momentum", "risk_level": "Medium", "total_profit": 553, "total_trades": 200, "win_rate": 69}, {"meets_target": false, "name": "Advanced Strategy", "risk_level": "Medium", "total_profit": 516, "total_trades": 200, "win_rate": 68}, {"meets_target": false, "name": "Pattern Recognition", "risk_level": "Low", "total_profit": 830.5, "total_trades": 200, "win_rate": 76.5}, {"meets_target": false, "name": "Multi-Timeframe", "risk_level": "Medium", "total_profit": 830.5, "total_trades": 200, "win_rate": 76.5}], "strategies_tested": 12, "timestamp": "2025-05-27T11:25:35"}, {"overall_win_rate": 69.91666666666667, "session_type": "full_strategy_test", "strategies": [{"meets_target": false, "name": "<PERSON><PERSON><PERSON>", "risk_level": "High", "total_profit": 516, "total_trades": 200, "win_rate": 68}, {"meets_target": false, "name": "Anti-Martingale", "risk_level": "Medium", "total_profit": 349.5, "total_trades": 200, "win_rate": 63.5}, {"meets_target": false, "name": "<PERSON><PERSON><PERSON><PERSON>", "risk_level": "Medium", "total_profit": 238.5, "total_trades": 200, "win_rate": 60.5}, {"meets_target": false, "name": "Percentage Recovery", "risk_level": "Low", "total_profit": 294, "total_trades": 200, "win_rate": 62}, {"meets_target": false, "name": "Dynamic Recovery", "risk_level": "Medium", "total_profit": 738, "total_trades": 200, "win_rate": 74}, {"meets_target": false, "name": "Trend Following", "risk_level": "Medium", "total_profit": 904.5, "total_trades": 200, "win_rate": 78.5}, {"meets_target": false, "name": "Reversal", "risk_level": "Medium", "total_profit": 590, "total_trades": 200, "win_rate": 70}, {"meets_target": false, "name": "Breakout", "risk_level": "High", "total_profit": 460.5, "total_trades": 200, "win_rate": 66.5}, {"meets_target": false, "name": "Momentum", "risk_level": "Medium", "total_profit": 590, "total_trades": 200, "win_rate": 70}, {"meets_target": false, "name": "Advanced Strategy", "risk_level": "Medium", "total_profit": 627, "total_trades": 200, "win_rate": 71}, {"meets_target": false, "name": "Pattern Recognition", "risk_level": "Low", "total_profit": 923, "total_trades": 200, "win_rate": 79}, {"meets_target": false, "name": "Multi-Timeframe", "risk_level": "Medium", "total_profit": 812, "total_trades": 200, "win_rate": 76}], "strategies_tested": 12, "timestamp": "2025-05-27T12:00:31"}, {"overall_win_rate": 70.125, "session_type": "full_strategy_test", "strategies": [{"meets_target": false, "name": "<PERSON><PERSON><PERSON>", "risk_level": "High", "total_profit": 719.5, "total_trades": 200, "win_rate": 73.5}, {"meets_target": false, "name": "Anti-Martingale", "risk_level": "Medium", "total_profit": 257, "total_trades": 200, "win_rate": 61}, {"meets_target": false, "name": "<PERSON><PERSON><PERSON><PERSON>", "risk_level": "Medium", "total_profit": 201.5, "total_trades": 200, "win_rate": 59.5}, {"meets_target": false, "name": "Percentage Recovery", "risk_level": "Low", "total_profit": 442, "total_trades": 200, "win_rate": 66}, {"meets_target": false, "name": "Dynamic Recovery", "risk_level": "Medium", "total_profit": 719.5, "total_trades": 200, "win_rate": 73.5}, {"meets_target": false, "name": "Trend Following", "risk_level": "Medium", "total_profit": 756.5, "total_trades": 200, "win_rate": 74.5}, {"meets_target": false, "name": "Reversal", "risk_level": "Medium", "total_profit": 349.5, "total_trades": 200, "win_rate": 63.5}, {"meets_target": false, "name": "Breakout", "risk_level": "High", "total_profit": 534.5, "total_trades": 200, "win_rate": 68.5}, {"meets_target": false, "name": "Momentum", "risk_level": "Medium", "total_profit": 664, "total_trades": 200, "win_rate": 72}, {"meets_target": false, "name": "Advanced Strategy", "risk_level": "Medium", "total_profit": 534.5, "total_trades": 200, "win_rate": 68.5}, {"meets_target": true, "name": "Pattern Recognition", "risk_level": "Low", "total_profit": 997, "total_trades": 200, "win_rate": 81}, {"meets_target": true, "name": "Multi-Timeframe", "risk_level": "Medium", "total_profit": 960, "total_trades": 200, "win_rate": 80}], "strategies_tested": 12, "timestamp": "2025-05-27T12:39:04"}, {"overall_win_rate": 69.58333333333333, "session_type": "full_strategy_test", "strategies": [{"meets_target": false, "name": "<PERSON><PERSON><PERSON>", "risk_level": "High", "total_profit": 590, "total_trades": 200, "win_rate": 70}, {"meets_target": false, "name": "Anti-Martingale", "risk_level": "Medium", "total_profit": 275.5, "total_trades": 200, "win_rate": 61.5}, {"meets_target": false, "name": "<PERSON><PERSON><PERSON><PERSON>", "risk_level": "Medium", "total_profit": 238.5, "total_trades": 200, "win_rate": 60.5}, {"meets_target": false, "name": "Percentage Recovery", "risk_level": "Low", "total_profit": 331, "total_trades": 200, "win_rate": 63}, {"meets_target": false, "name": "Dynamic Recovery", "risk_level": "Medium", "total_profit": 534.5, "total_trades": 200, "win_rate": 68.5}, {"meets_target": false, "name": "Trend Following", "risk_level": "Medium", "total_profit": 849, "total_trades": 200, "win_rate": 77}, {"meets_target": false, "name": "Reversal", "risk_level": "Medium", "total_profit": 571.5, "total_trades": 200, "win_rate": 69.5}, {"meets_target": false, "name": "Breakout", "risk_level": "High", "total_profit": 553, "total_trades": 200, "win_rate": 69}, {"meets_target": false, "name": "Momentum", "risk_level": "Medium", "total_profit": 682.5, "total_trades": 200, "win_rate": 72.5}, {"meets_target": false, "name": "Advanced Strategy", "risk_level": "Medium", "total_profit": 701, "total_trades": 200, "win_rate": 73}, {"meets_target": false, "name": "Pattern Recognition", "risk_level": "Low", "total_profit": 756.5, "total_trades": 200, "win_rate": 74.5}, {"meets_target": false, "name": "Multi-Timeframe", "risk_level": "Medium", "total_profit": 812, "total_trades": 200, "win_rate": 76}], "strategies_tested": 12, "timestamp": "2025-05-27T12:41:02"}, {"overall_win_rate": 70.04166666666667, "session_type": "full_strategy_test", "strategies": [{"meets_target": false, "name": "<PERSON><PERSON><PERSON>", "risk_level": "High", "total_profit": 405, "total_trades": 200, "win_rate": 65}, {"meets_target": false, "name": "Anti-Martingale", "risk_level": "Medium", "total_profit": 201.5, "total_trades": 200, "win_rate": 59.5}, {"meets_target": false, "name": "<PERSON><PERSON><PERSON><PERSON>", "risk_level": "Medium", "total_profit": 386.5, "total_trades": 200, "win_rate": 64.5}, {"meets_target": false, "name": "Percentage Recovery", "risk_level": "Low", "total_profit": 331, "total_trades": 200, "win_rate": 63}, {"meets_target": false, "name": "Dynamic Recovery", "risk_level": "Medium", "total_profit": 738, "total_trades": 200, "win_rate": 74}, {"meets_target": false, "name": "Trend Following", "risk_level": "Medium", "total_profit": 830.5, "total_trades": 200, "win_rate": 76.5}, {"meets_target": false, "name": "Reversal", "risk_level": "Medium", "total_profit": 645.5, "total_trades": 200, "win_rate": 71.5}, {"meets_target": false, "name": "Breakout", "risk_level": "High", "total_profit": 775, "total_trades": 200, "win_rate": 75}, {"meets_target": false, "name": "Momentum", "risk_level": "Medium", "total_profit": 423.5, "total_trades": 200, "win_rate": 65.5}, {"meets_target": false, "name": "Advanced Strategy", "risk_level": "Medium", "total_profit": 590, "total_trades": 200, "win_rate": 70}, {"meets_target": false, "name": "Pattern Recognition", "risk_level": "Low", "total_profit": 756.5, "total_trades": 200, "win_rate": 74.5}, {"meets_target": true, "name": "Multi-Timeframe", "risk_level": "Medium", "total_profit": 1015.5, "total_trades": 200, "win_rate": 81.5}], "strategies_tested": 12, "timestamp": "2025-05-29T15:58:21"}, {"overall_win_rate": 71.58333333333333, "session_type": "full_strategy_test", "strategies": [{"meets_target": false, "name": "<PERSON><PERSON><PERSON>", "risk_level": "High", "total_profit": 719.5, "total_trades": 200, "win_rate": 73.5}, {"meets_target": false, "name": "Anti-Martingale", "risk_level": "Medium", "total_profit": 553, "total_trades": 200, "win_rate": 69}, {"meets_target": false, "name": "<PERSON><PERSON><PERSON><PERSON>", "risk_level": "Medium", "total_profit": 423.5, "total_trades": 200, "win_rate": 65.5}, {"meets_target": false, "name": "Percentage Recovery", "risk_level": "Low", "total_profit": 312.5, "total_trades": 200, "win_rate": 62.5}, {"meets_target": false, "name": "Dynamic Recovery", "risk_level": "Medium", "total_profit": 793.5, "total_trades": 200, "win_rate": 75.5}, {"meets_target": false, "name": "Trend Following", "risk_level": "Medium", "total_profit": 830.5, "total_trades": 200, "win_rate": 76.5}, {"meets_target": false, "name": "Reversal", "risk_level": "Medium", "total_profit": 627, "total_trades": 200, "win_rate": 71}, {"meets_target": false, "name": "Breakout", "risk_level": "High", "total_profit": 645.5, "total_trades": 200, "win_rate": 71.5}, {"meets_target": false, "name": "Momentum", "risk_level": "Medium", "total_profit": 497.5, "total_trades": 200, "win_rate": 67.5}, {"meets_target": false, "name": "Advanced Strategy", "risk_level": "Medium", "total_profit": 627, "total_trades": 200, "win_rate": 71}, {"meets_target": true, "name": "Pattern Recognition", "risk_level": "Low", "total_profit": 978.5, "total_trades": 200, "win_rate": 80.5}, {"meets_target": false, "name": "Multi-Timeframe", "risk_level": "Medium", "total_profit": 775, "total_trades": 200, "win_rate": 75}], "strategies_tested": 12, "timestamp": "2025-05-29T15:58:58"}]
import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

// News Signal Strength Overlay Component
Rectangle {
    id: newsOverlay

    // Properties
    property bool isVisible: qmlBridge.hasActiveNews
    property string headline: qmlBridge.topNewsHeadline
    property double sentimentScore: qmlBridge.newsSentimentScore
    property string sentimentText: qmlBridge.newsSentimentText
    property string sentimentColor: qmlBridge.newsSentimentColor
    property double confidenceLevel: qmlBridge.newsConfidenceLevel
    property string newsSource: qmlBridge.newsSource

    // Positioning and sizing
    width: parent.width * 0.35  // 35% of parent width
    height: isVisible ? 120 : 0
    anchors.top: parent.top
    anchors.right: parent.right
    anchors.topMargin: 80  // Below the top bar
    anchors.rightMargin: 20

    // Glassmorphism styling
    color: "transparent"
    radius: 12
    border.width: 1
    border.color: Qt.rgba(1, 1, 1, 0.2)

    // Smooth show/hide animation
    Behavior on height {
        NumberAnimation {
            duration: 300
            easing.type: Easing.OutCubic
        }
    }

    Behavior on opacity {
        NumberAnimation {
            duration: 300
            easing.type: Easing.OutCubic
        }
    }

    opacity: isVisible ? 1.0 : 0.0
    visible: height > 0

    // Background glassmorphism effect (without blur dependency)
    Rectangle {
        id: backgroundBlur
        anchors.fill: parent
        color: Qt.rgba(0.1, 0.1, 0.15, 0.85)  // Dark semi-transparent
        radius: parent.radius

        // Glassmorphism gradient
        gradient: Gradient {
            GradientStop { position: 0.0; color: Qt.rgba(0.2, 0.2, 0.3, 0.9) }
            GradientStop { position: 1.0; color: Qt.rgba(0.1, 0.1, 0.2, 0.7) }
        }

        // Subtle inner glow effect for glassmorphism
        Rectangle {
            anchors.fill: parent
            anchors.margins: 1
            radius: parent.radius - 1
            color: "transparent"
            border.width: 1
            border.color: Qt.rgba(1, 1, 1, 0.1)
        }
    }

    // Content layout
    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 16
        spacing: 8

        // Header row with title and source
        RowLayout {
            Layout.fillWidth: true
            spacing: 8

            // News signal icon
            Text {
                text: "📰"
                font.pixelSize: 16
                color: "#ffffff"
            }

            // Title
            Text {
                text: "News Signal Strength"
                font.family: "SF Pro Display"
                font.pixelSize: 14
                font.weight: Font.DemiBold
                color: "#ffffff"
                Layout.fillWidth: true
            }

            // News source
            Text {
                text: newsSource
                font.family: "SF Pro Display"
                font.pixelSize: 10
                color: Qt.rgba(1, 1, 1, 0.7)
                horizontalAlignment: Text.AlignRight
            }
        }

        // Headline text (scrolling if too long)
        ScrollView {
            Layout.fillWidth: true
            Layout.preferredHeight: 32
            clip: true

            ScrollBar.horizontal.policy: ScrollBar.AlwaysOff
            ScrollBar.vertical.policy: ScrollBar.AlwaysOff

            Text {
                text: headline
                font.family: "SF Pro Display"
                font.pixelSize: 12
                color: "#ffffff"
                wrapMode: Text.WordWrap
                width: newsOverlay.width - 32
                maximumLineCount: 2
                elide: Text.ElideRight
            }
        }

        // Sentiment and confidence row
        RowLayout {
            Layout.fillWidth: true
            spacing: 12

            // Sentiment indicator
            Rectangle {
                Layout.preferredWidth: 120
                Layout.preferredHeight: 24
                color: sentimentColor
                radius: 12
                border.width: 1
                border.color: Qt.rgba(1, 1, 1, 0.3)

                Text {
                    anchors.centerIn: parent
                    text: sentimentText
                    font.family: "SF Pro Display"
                    font.pixelSize: 10
                    font.weight: Font.Medium
                    color: "#ffffff"
                }
            }

            // Confidence indicator
            ColumnLayout {
                Layout.fillWidth: true
                spacing: 2

                Text {
                    text: "Confidence"
                    font.family: "SF Pro Display"
                    font.pixelSize: 9
                    color: Qt.rgba(1, 1, 1, 0.7)
                }

                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 6
                    color: Qt.rgba(1, 1, 1, 0.2)
                    radius: 3

                    Rectangle {
                        width: parent.width * confidenceLevel
                        height: parent.height
                        color: {
                            if (confidenceLevel > 0.8) return "#00cc66"      // High confidence - green
                            else if (confidenceLevel > 0.6) return "#66cc00" // Medium-high - light green
                            else if (confidenceLevel > 0.4) return "#ffcc00" // Medium - yellow
                            else if (confidenceLevel > 0.2) return "#cc6600" // Low-medium - orange
                            else return "#cc3333"                            // Low - red
                        }
                        radius: parent.radius

                        Behavior on width {
                            NumberAnimation {
                                duration: 500
                                easing.type: Easing.OutCubic
                            }
                        }
                    }
                }

                Text {
                    text: Math.round(confidenceLevel * 100) + "%"
                    font.family: "SF Pro Display"
                    font.pixelSize: 9
                    font.weight: Font.Medium
                    color: "#ffffff"
                    Layout.alignment: Qt.AlignRight
                }
            }
        }
    }

    // Pulse animation for high-impact news
    SequentialAnimation {
        id: pulseAnimation
        running: isVisible && Math.abs(sentimentScore) > 0.5
        loops: Animation.Infinite

        PropertyAnimation {
            target: newsOverlay
            property: "scale"
            from: 1.0
            to: 1.02
            duration: 1000
            easing.type: Easing.InOutSine
        }

        PropertyAnimation {
            target: newsOverlay
            property: "scale"
            from: 1.02
            to: 1.0
            duration: 1000
            easing.type: Easing.InOutSine
        }
    }

    // Click handler for expanding news details
    MouseArea {
        anchors.fill: parent
        hoverEnabled: true
        cursorShape: Qt.PointingHandCursor

        onClicked: {
            // Emit signal to show detailed news panel
            console.log("News overlay clicked - showing detailed news")
            // You can add a signal here to show a detailed news panel
        }

        onEntered: {
            newsOverlay.opacity = 0.9
        }

        onExited: {
            newsOverlay.opacity = 1.0
        }
    }

    // Simple shadow effect using Rectangle (no external dependencies)
    Rectangle {
        anchors.fill: parent
        anchors.topMargin: 4
        anchors.leftMargin: 2
        anchors.rightMargin: -2
        anchors.bottomMargin: -2
        radius: parent.radius
        color: Qt.rgba(0, 0, 0, 0.2)
        z: -1  // Behind the main overlay
    }
}

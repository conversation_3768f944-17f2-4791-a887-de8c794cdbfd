import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import Terminal 1.0

Rectangle {
    id: mainPanel
    color: "#20242f"

    // Properties
    property string currentView: "dashboard"

    // Signals
    signal addIndicator()
    signal changeTimeframe(string timeframe)

    // Stack layout to switch between views
    StackLayout {
        anchors.fill: parent
        currentIndex: {
            switch (currentView) {
                case "dashboard": return 0;
                case "markets": return 1;
                case "strategies": return 2;
                case "history": return 3;
                default: return 0;
            }
        }

        // Dashboard View
        Item {
            GridLayout {
                anchors.fill: parent
                anchors.margins: 20
                rowSpacing: 20
                columnSpacing: 20
                rows: 5
                columns: 2

                // Pattern Feed Panel (replacing Price Chart)
                PatternFeedPanel {
                    id: patternFeedPanel
                    Layout.row: 0
                    Layout.column: 0
                    Layout.columnSpan: 2
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    Layout.preferredHeight: parent.height * 0.5
                }

                // Sentiment Panel
                SentimentPanel {
                    id: sentimentPanel
                    Layout.row: 2
                    Layout.column: 0
                    Layout.fillWidth: true
                    Layout.preferredHeight: 120
                    Layout.preferredWidth: parent.width * 0.45
                }

                // News Signal Strength Panel (directly under sentiment)
                NewsSignalOverlay {
                    id: newsSignalPanel
                    Layout.row: 3
                    Layout.column: 0
                    Layout.fillWidth: true
                    Layout.preferredHeight: 120
                    Layout.preferredWidth: parent.width * 0.45
                }

                // Phase Selection Panel
                Rectangle {
                    id: phasePanel
                    Layout.row: 2
                    Layout.column: 1
                    Layout.rowSpan: 2  // Span across both sentiment and news rows
                    Layout.fillWidth: true
                    Layout.preferredHeight: 180
                    Layout.preferredWidth: parent.width * 0.48
                    color: "#292f3d"
                    radius: 10

                    ColumnLayout {
                        anchors.fill: parent
                        anchors.margins: 10
                        spacing: 8

                        // Title and controls
                        RowLayout {
                            Layout.fillWidth: true
                            spacing: 10

                            Text {
                                text: "Trading Phase"
                                color: "white"
                                font.pixelSize: 14
                                font.bold: true
                            }

                            Item { Layout.fillWidth: true }

                            // Auto Phase Mode switch
                            Row {
                                spacing: 5

                                Text {
                                    text: "Auto:"
                                    color: "#cccccc"
                                    font.pixelSize: 12
                                    anchors.verticalCenter: parent.verticalCenter
                                }

                                Switch {
                                    id: autoPhaseSwitch
                                    checked: QmlBridge.autoPhaseMode

                                    indicator: Rectangle {
                                        implicitWidth: 36
                                        implicitHeight: 18
                                        radius: 9
                                        color: autoPhaseSwitch.checked ? "#4a6dff" : "#3a4358"
                                        border.color: "#40000000"
                                        border.width: 1

                                        Rectangle {
                                            x: autoPhaseSwitch.checked ? parent.width - width - 2 : 2
                                            y: 2
                                            width: 14
                                            height: 14
                                            radius: 7
                                            color: "white"

                                            Behavior on x {
                                                NumberAnimation { duration: 150 }
                                            }
                                        }
                                    }

                                    onToggled: {
                                        QmlBridge.autoPhaseMode = checked
                                    }
                                }
                            }

                            // Repetitions ComboBox
                            Row {
                                spacing: 5

                                Text {
                                    text: "Reps:"
                                    color: "#cccccc"
                                    font.pixelSize: 12
                                    anchors.verticalCenter: parent.verticalCenter
                                }

                                ComboBox {
                                    id: repetitionsCombo
                                    model: [1, 2, 3, 5, 10]
                                    currentIndex: {
                                        for (var i = 0; i < model.length; i++) {
                                            if (model[i] === QmlBridge.phaseRepetitions) {
                                                return i;
                                            }
                                        }
                                        return 2; // Default to 3 repetitions (index 2)
                                    }

                                    background: Rectangle {
                                        color: "#303748"
                                        radius: 3
                                        border.color: "#66aaff"
                                        border.width: 1
                                        implicitWidth: 40
                                        implicitHeight: 20
                                    }

                                    contentItem: Text {
                                        text: repetitionsCombo.displayText
                                        color: "white"
                                        font.pixelSize: 12
                                        verticalAlignment: Text.AlignVCenter
                                        horizontalAlignment: Text.AlignHCenter
                                    }

                                    delegate: ItemDelegate {
                                        width: repetitionsCombo.width
                                        contentItem: Text {
                                            text: modelData
                                            color: "white"
                                            font.pixelSize: 12
                                            verticalAlignment: Text.AlignVCenter
                                        }
                                        background: Rectangle {
                                            color: repetitionsCombo.highlightedIndex === index ? "#3a4358" : "#303748"
                                        }
                                    }

                                    popup: Popup {
                                        y: repetitionsCombo.height
                                        width: repetitionsCombo.width
                                        implicitHeight: contentItem.implicitHeight
                                        padding: 1

                                        contentItem: ListView {
                                            clip: true
                                            implicitHeight: contentHeight
                                            model: repetitionsCombo.popup.visible ? repetitionsCombo.delegateModel : null
                                            currentIndex: repetitionsCombo.highlightedIndex
                                        }

                                        background: Rectangle {
                                            color: "#303748"
                                            border.color: "#66aaff"
                                            border.width: 1
                                            radius: 3
                                        }
                                    }

                                    onActivated: function(index) {
                                        QmlBridge.phaseRepetitions = model[index]
                                    }
                                }
                            }
                        }

                        // Phase Buttons Grid
                        GridLayout {
                            Layout.fillWidth: true
                            columns: 4
                            rowSpacing: 6
                            columnSpacing: 6

                            Repeater {
                                model: 8

                                Rectangle {
                                    id: phaseBtn
                                    Layout.fillWidth: true
                                    Layout.preferredHeight: 24
                                    color: QmlBridge.currentPhase === (index + 1) ? "#4a6dff" : "#3a4358"
                                    radius: 5

                                    Rectangle {
                                        anchors.fill: parent
                                        radius: 5
                                        color: "#ffffff"
                                        opacity: phaseMouseArea.containsMouse ? 0.05 : 0.02
                                    }

                                    border.color: "#40000000"
                                    border.width: 1

                                    Text {
                                        anchors.centerIn: parent
                                        text: (index + 1).toString()
                                        color: "white"
                                        font.pixelSize: 12
                                        font.weight: Font.Medium
                                    }

                                    ToolTip {
                                        visible: phaseMouseArea.containsMouse
                                        text: {
                                            for (var i = 0; i < QmlBridge.phaseDescriptions.length; i++) {
                                                if (QmlBridge.phaseDescriptions[i].phase === (index + 1)) {
                                                    return QmlBridge.phaseDescriptions[i].description;
                                                }
                                            }
                                            return "Phase " + (index + 1);
                                        }
                                        delay: 500
                                    }

                                    MouseArea {
                                        id: phaseMouseArea
                                        anchors.fill: parent
                                        hoverEnabled: true
                                        cursorShape: Qt.PointingHandCursor

                                        onPressed: {
                                            phaseBtn.opacity = 0.8
                                            phaseBtn.scale = 0.95
                                        }
                                        onReleased: {
                                            phaseBtn.opacity = 1.0
                                            phaseBtn.scale = 1.0
                                        }
                                        onClicked: {
                                            QmlBridge.currentPhase = index + 1
                                        }
                                    }

                                    Behavior on opacity { NumberAnimation { duration: 100 } }
                                    Behavior on scale { NumberAnimation { duration: 100; easing.type: Easing.OutQuad } }
                                }
                            }
                        }

                        // Description box
                        Rectangle {
                            Layout.fillWidth: true
                            Layout.preferredHeight: Math.min(60, phaseDescText.contentHeight + 14)
                            color: "#3a4358"
                            radius: 6
                            border.color: "#40000000"
                            border.width: 1

                            Text {
                                id: phaseDescText
                                anchors.fill: parent
                                anchors.margins: 6
                                text: {
                                    for (var i = 0; i < QmlBridge.phaseDescriptions.length; i++) {
                                        if (QmlBridge.phaseDescriptions[i].phase === QmlBridge.currentPhase) {
                                            return QmlBridge.phaseDescriptions[i].description;
                                        }
                                    }
                                    return "Select a phase";
                                }
                                color: "#cccccc"
                                font.pixelSize: 12
                                wrapMode: Text.WordWrap
                                elide: Text.ElideRight
                                maximumLineCount: 3
                            }
                        }

                        // Progress bar section
                        Rectangle {
                            Layout.fillWidth: true
                            Layout.preferredHeight: 32
                            color: "#3a4358"
                            radius: 6
                            border.color: "#40000000"
                            border.width: 1

                            ColumnLayout {
                                anchors.fill: parent
                                anchors.margins: 6
                                spacing: 2

                                RowLayout {
                                    Layout.fillWidth: true

                                    Text {
                                        text: QmlBridge.autoPhaseMode ?
                                              "Progress (Rep " + QmlBridge.currentPhaseRepetition + "/" + QmlBridge.phaseRepetitions + ")" :
                                              "Progress"
                                        color: "#cccccc"
                                        font.pixelSize: 12
                                    }

                                    Item { Layout.fillWidth: true }

                                    Text {
                                        visible: QmlBridge.completedPhases > 0
                                        text: "Completed: " + QmlBridge.completedPhases
                                        color: "#66aaff"
                                        font.pixelSize: 12
                                    }

                                    Text {
                                        id: progressText
                                        property real progressValue: QmlBridge.phaseProgress

                                        text: progressValue.toFixed(1) + "%"
                                        color: "white"
                                        font.pixelSize: 12
                                    }
                                }

                                Rectangle {
                                    Layout.fillWidth: true
                                    Layout.preferredHeight: 6
                                    radius: 3
                                    color: "#1e2a3a"

                                    Rectangle {
                                        anchors.left: parent.left
                                        anchors.top: parent.top
                                        anchors.bottom: parent.bottom
                                        width: parent.width * (QmlBridge.phaseProgress / 100)
                                        radius: 3
                                        gradient: Gradient {
                                            GradientStop { position: 0.0; color: "#4a6dff" }
                                            GradientStop { position: 1.0; color: "#6a8dff" }
                                        }

                                        Rectangle {
                                            anchors.fill: parent
                                            color: "#ffffff"
                                            opacity: 0.08
                                            radius: 3
                                        }

                                        Behavior on width {
                                            NumberAnimation { duration: 300; easing.type: Easing.OutQuad }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                // Indicators Panel
                IndicatorsPanel {
                    id: indicatorsPanel
                    Layout.row: 4
                    Layout.column: 0
                    Layout.columnSpan: 2
                    Layout.fillWidth: true
                    Layout.fillHeight: true

                    onAddIndicatorClicked: function() {
                        addIndicator();
                    }

                    onRemoveIndicator: function(name) {
                        QmlBridge.removeIndicator(name);
                    }
                }

                // Loading indicator
                Rectangle {
                    id: loadingIndicator
                    Layout.row: 5
                    Layout.column: 0
                    Layout.fillWidth: true
                    Layout.preferredHeight: 40
                    color: "#292f3d"
                    radius: 10
                    visible: QmlBridge.isLoading

                    RowLayout {
                        anchors.fill: parent
                        anchors.margins: 10
                        spacing: 10

                        Rectangle {
                            width: 20
                            height: 20
                            radius: 10
                            color: "#4a6dff"

                            SequentialAnimation on opacity {
                                running: QmlBridge.isLoading
                                loops: Animation.Infinite
                                NumberAnimation { from: 1.0; to: 0.3; duration: 800; easing.type: Easing.InOutQuad }
                                NumberAnimation { from: 0.3; to: 1.0; duration: 800; easing.type: Easing.InOutQuad }
                            }
                        }

                        Text {
                            text: "Loading..."
                            color: "white"
                            font.pixelSize: 14
                        }
                    }
                }

                // Error message
                Rectangle {
                    id: errorMessage
                    Layout.row: 5
                    Layout.column: 1
                    Layout.fillWidth: true
                    Layout.preferredHeight: 40
                    color: "#ff4d4d"
                    radius: 10
                    visible: QmlBridge.errorMessage !== ""

                    Text {
                        anchors.fill: parent
                        anchors.margins: 10
                        text: QmlBridge.errorMessage
                        color: "white"
                        font.pixelSize: 14
                        wrapMode: Text.WordWrap
                        verticalAlignment: Text.AlignVCenter
                    }
                }
            }
        }

        // Markets View
        Rectangle {
            color: "#20242f"

            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 20
                spacing: 20

                Text {
                    text: "Markets"
                    color: "white"
                    font.pixelSize: 24
                    font.bold: true
                }

                Rectangle {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: "#292f3d"
                    radius: 10

                    ListView {
                        anchors.fill: parent
                        anchors.margins: 20
                        model: ["EUR/USD", "GBP/USD", "USD/JPY", "BTC/USD", "ETH/USD", "XAU/USD", "EUR/GBP", "AUD/USD"]
                        delegate: Rectangle {
                            width: parent.width
                            height: 60
                            color: index % 2 === 0 ? "#303748" : "#353d4e"

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 10
                                spacing: 10

                                Text {
                                    text: modelData
                                    color: "white"
                                    font.pixelSize: 16
                                    font.bold: true
                                }

                                Item { Layout.fillWidth: true }

                                Text {
                                    text: (1.0 + Math.random() * 0.2).toFixed(5)
                                    color: Math.random() > 0.5 ? "#00ff99" : "#ff6666"
                                    font.pixelSize: 16
                                    font.bold: true
                                }

                                Text {
                                    text: (Math.random() > 0.5 ? "+" : "-") + (Math.random() * 0.5).toFixed(2) + "%"
                                    color: text.startsWith("+") ? "#00ff99" : "#ff6666"
                                    font.pixelSize: 14
                                }
                            }
                        }
                    }
                }
            }
        }

        // Strategies View - Strategy Testing System
        StrategyTestingPanel {
            id: strategyTestingPanel
        }

        // History View
        Rectangle {
            color: "#20242f"

            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 20
                spacing: 20

                Text {
                    text: "Trade History"
                    color: "white"
                    font.pixelSize: 24
                    font.bold: true
                }

                Rectangle {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: "#292f3d"
                    radius: 10

                    ListView {
                        anchors.fill: parent
                        anchors.margins: 20
                        model: 20
                        delegate: Rectangle {
                            width: parent.width
                            height: 60
                            color: index % 2 === 0 ? "#303748" : "#353d4e"

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 10
                                spacing: 10

                                Text {
                                    text: {
                                        let date = new Date();
                                        date.setMinutes(date.getMinutes() - index * 30);
                                        return date.toLocaleTimeString();
                                    }
                                    color: "#aaaaaa"
                                    font.pixelSize: 14
                                }

                                Text {
                                    text: ["EUR/USD", "GBP/USD", "USD/JPY", "BTC/USD", "ETH/USD"][index % 5]
                                    color: "white"
                                    font.pixelSize: 16
                                    font.bold: true
                                }

                                Item { Layout.fillWidth: true }

                                Text {
                                    text: ["BUY", "SELL"][index % 2]
                                    color: index % 2 === 0 ? "#00ff99" : "#ff6666"
                                    font.pixelSize: 14
                                    font.bold: true
                                }

                                Text {
                                    text: "$" + (50 + Math.random() * 200).toFixed(2)
                                    color: "white"
                                    font.pixelSize: 14
                                }

                                Text {
                                    text: (Math.random() > 0.6 ? "+" : "-") + "$" + (Math.random() * 50).toFixed(2)
                                    color: text.startsWith("+") ? "#00ff99" : "#ff6666"
                                    font.pixelSize: 14
                                    font.bold: true
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

#pragma once

// Structure to hold AI model prediction results
struct AIModelPrediction {
    double confidence;     // Confidence score of the prediction (0.0 to 1.0)
    double probability;   // Probability of the predicted outcome
    std::string direction; // Predicted market direction
    std::string timestamp; // When the prediction was made
};

// Structure to hold news impact analysis
struct NewsImpactAnalysis {
    double sentiment;     // Overall sentiment score (-1.0 to 1.0)
    double magnitude;     // Magnitude of the news impact
    double reliability;   // Reliability score of the news source
    std::string category; // Category of the news (e.g., "economic", "political")
    std::string timestamp; // When the news was published
};
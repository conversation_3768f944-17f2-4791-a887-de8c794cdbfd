#pragma once
#include <string>
#include <chrono>
#include <vector>

struct NewsData {
    std::string title;
    std::string content;
    std::chrono::system_clock::time_point timestamp;
    double impact;  // -1.0 to 1.0
    std::vector<std::string> affectedAssets;
    std::string category;  // "ECONOMIC", "POLITICAL", "COMPANY", etc.
    double sentiment;  // -1.0 to 1.0
};

class NewsAnalyzer {
public:
    struct NewsAnalysisResult {
        double shortTermImpact;
        double mediumTermImpact;
        double longTermImpact;
        std::vector<std::string> keywords;
        double marketSentiment;
    };

    static NewsAnalysisResult analyzeNewsImpact(const NewsData& news);
    static double calculateNewsSentiment(const std::string& content);
    static bool isHighImpact(const NewsData& news);

private:
    static void processKeywords(const std::string& content);
    static double sentimentAnalysis(const std::string& text);
};
#pragma once
#include <string>
#include <vector>
#include <map>
#include <chrono>
#include "../models/market_regime.h"

struct AINewsImpactAnalysis {
    double impactScore;
    double volatilityImpact;
    double sentimentScore;
    double marketRelevance;
    double processingTime;
    std::vector<std::string> affectedAssets;
    std::chrono::system_clock::time_point timestamp;
};

struct PatternStrengthResult {
    double confidence;
    double processingTime;
    double strength;
    double reliability;
    double historicalAccuracy;
    double currentVolatility;
    std::map<std::string, double> patternStrengths;
    double totalStrength = 0.0;
    double averageStrength = 0.0;
    //std::chrono::steady_clock::time_point processingTime;
    std::vector<std::string> confirmedPatterns;
    bool isValid;
};

struct MarketRegimeResult {
    MarketRegime regime;
    double confidence;
    double volatility;
    double momentum;
    double trendStrength;
    double processingTime;
    std::chrono::system_clock::time_point timestamp;
    std::vector<std::string> supportingIndicators;
};
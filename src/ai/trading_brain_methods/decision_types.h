#pragma once
#include <string>
#include <vector>
#include "../models/market_data.h"
#include "../models/trade_types.h"
#include "../analysis_types.h"
#include "../trading_brain.h"
#include "../models/database_types.h"


struct NewsDecisionImpact {
    double volatilityImpact;
    double sentimentScore;
    double marketImpact;
    double processingTime;
};

struct TradeDecision {
    bool shouldTrade;
    Models::TradeDirection direction;
    double positionSize;
    double stopLoss;
    double takeProfit;
    std::vector<std::string> entryRules;
};
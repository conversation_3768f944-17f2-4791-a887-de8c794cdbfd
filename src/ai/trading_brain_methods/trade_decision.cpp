#include "trade_decision.h"
#include "timeframe_selection.h"
#include "decision_types.h"
#include "../../hardware/hardware_optimization_manager.h"
#include "../ml/advanced_ai_model.h"
#include "../trading_brain_types.h" // Include for RiskLevels
#include "../risk/risk_assessment.h" // Include for RiskAssessment
#include "../../market/market_context.h" // Include for MarketContext
#include "../indicators/concrete_indicators.h" // Include for createIndicator function
#include "../../models/qt_compat.h" // Include for QDateTime compatibility

// Forward declarations for helper functions
TimeframeSelection convertTimeframeSelection(const AI::Timeframe::TimeframeSelection& aiTimeframe);
NewsDecisionImpact convertToNewsImpact(const Models::NewsImpactAnalysis& modelNewsImpact);
double getMaxPositionSize(const RiskAssessment& risk);
std::vector<std::string> convertEntryRulesToStringVector(const EntryRules& rules);
TradeData::MarketContext convertToTradeDataMarketContext(const MarketContext& context);

// Define TradeDecisionPerformanceMetrics struct for internal use
struct TradeDecisionPerformanceMetrics {
    double newsAnalysisTime = 0.0;
    double patternAnalysisTime = 0.0;
    double regimeDetectionTime = 0.0;
    double decisionTime = 0.0;

    // Convert to Models::PerformanceMetrics
    operator Models::PerformanceMetrics() const {
        Models::PerformanceMetrics result;
        // Set processing times as custom metrics
        result.cpuUsage = (newsAnalysisTime + patternAnalysisTime + regimeDetectionTime + decisionTime) * 100.0;
        result.memoryUsage = 0.0; // Set appropriate value if available
        return result;
    }
};

// Implementation of missing function in TradingBrainTradeDecision namespace
namespace TradingBrainTradeDecision {
    // Create a MarketContext from patterns, indicators, and timeframe
    MarketContext createMarketContext(
        const std::vector<AI::Pattern>& patterns,
        const Models::IndicatorSet& indicators,
        const TimeframeSelection& timeframe
    ) {
        MarketContext context;

        // Initialize market state
        context.marketRegime = "unknown";
        context.trendDirection = indicators.trendDirection;
        context.volatilityLevel = indicators.volatility;
        context.liquidityLevel = 0.5; // Default value
        context.price = indicators.currentPrice;
        context.volume = indicators.volume;
        context.volatility = indicators.volatility;
        context.marketDepth = 0.0; // Default value
        context.liquidityIndex = 0.0; // Default value
        context.orderFlowImbalance = 0.0; // Default value
        context.trendStrength = indicators.trendStrength;
        context.isVolatile = indicators.volatility > 0.7;

        // Set pattern confidence if available
        if (!patterns.empty()) {
            context.patternConfidence = patterns[0].confidence;
        } else {
            context.patternConfidence = 0.0;
        }

        // Set market phase based on indicators
        if (indicators.trendStrength > 0.7 && indicators.trendDirection == "up") {
            context.marketPhase = "markup";
        } else if (indicators.trendStrength > 0.7 && indicators.trendDirection == "down") {
            context.marketPhase = "markdown";
        } else if (indicators.volume > 1.2) {
            context.marketPhase = "accumulation";
        } else {
            context.marketPhase = "distribution";
        }

        // Set market conditions
        context.marketConditions.volatility = indicators.volatility;
        context.marketConditions.trendStrength = indicators.trendStrength;
        context.marketConditions.momentum = indicators.momentum;
        context.marketConditions.volume = indicators.volume;
        context.marketConditions.spread = indicators.spread;

        // Set market sentiment based on indicators
        if (indicators.bullishSentiment > 0.6) {
            context.marketConditions.sentiment = "bullish";
        } else if (indicators.bearishSentiment > 0.6) {
            context.marketConditions.sentiment = "bearish";
        } else {
            context.marketConditions.sentiment = "neutral";
        }

        // Set timeframe
        context.timeframe = timeframe;

        // Set timestamp to current time
        context.timestamp = std::chrono::system_clock::now();

        return context;
    }

    // Assess risk based on market context and historical data
    RiskAssessment assessRisk(
        const MarketContext& context,
        const std::vector<Models::MarketData>& historicalData
    ) {
        RiskAssessment risk;

        // Set default risk parameters
        risk.stopLoss = 0.05; // 5% default stop loss
        risk.takeProfit = 0.10; // 10% default take profit
        risk.positionSize = 0.0;
        risk.riskRewardRatio = risk.takeProfit / risk.stopLoss;

        // Calculate volatility adjustment based on historical data
        double volatility = context.volatility;
        risk.volatilityAdjustment = 1.0 - (volatility * 0.5); // Reduce position size in high volatility

        // Calculate maximum risk per trade based on market conditions
        if (context.marketConditions.sentiment == "bullish") {
            risk.maxRiskPerTrade = 0.02; // 2% risk in bullish market
        } else if (context.marketConditions.sentiment == "bearish") {
            risk.maxRiskPerTrade = 0.01; // 1% risk in bearish market
        } else {
            risk.maxRiskPerTrade = 0.015; // 1.5% risk in neutral market
        }

        // Adjust risk based on trend strength
        risk.maxRiskPerTrade *= (1.0 + (context.trendStrength * 0.2));

        // Calculate risk metrics
        risk.volatilityRisk = volatility * 0.5;
        risk.marketRisk = (1.0 - context.trendStrength) * 0.3;
        risk.patternRisk = (1.0 - context.patternConfidence) * 0.2;

        // Set risk metrics
        risk.metrics.volatilityScore = volatility;
        risk.metrics.drawdownRisk = 0.0; // Default value
        risk.metrics.exposureRatio = 0.0; // Default value
        risk.metrics.sharpeRatio = 0.0; // Default value

        return risk;
    }

    RiskLevels calculateDynamicRiskLevels(
        const Models::TradeDirection& direction,
        const Models::NewsImpactAnalysis& newsImpact,
        const MarketRegimeResult& regime,
        const RiskAssessment& risk
    ) {
        RiskLevels levels;

        // Default values
        levels.stopLoss = 0.05;  // 5% default stop loss
        levels.takeProfit = 0.10; // 10% default take profit

        // Adjust based on direction
        if (direction == Models::TradeDirection::CALL) {
            // For CALL trades, adjust based on market conditions
            levels.stopLoss *= (1.0 + newsImpact.volatilityImpact * 0.5);
            levels.takeProfit *= (1.0 + regime.volatility * 0.3);
        } else {
            // For PUT trades, adjust differently
            levels.stopLoss *= (1.0 + newsImpact.volatilityImpact * 0.6);
            levels.takeProfit *= (1.0 + regime.volatility * 0.2);
        }

        // Further adjust based on risk assessment
        // Use maxRiskPerTrade as a proxy for risk tolerance
        if (risk.maxRiskPerTrade > 0.03) { // Higher than 3% is considered high risk tolerance
            // Higher risk tolerance means wider stops and targets
            levels.stopLoss *= 1.2;
            levels.takeProfit *= 1.3;
        } else if (risk.maxRiskPerTrade < 0.01) { // Lower than 1% is considered low risk tolerance
            // Lower risk tolerance means tighter stops and targets
            levels.stopLoss *= 0.8;
            levels.takeProfit *= 0.9;
        }

        return levels;
    }
}

Models::TradeDecision TradingBrain::analyzeAndDecide(
    const std::string& symbol,
    const Models::MarketData& currentData,
    const std::vector<Models::MarketData>& historicalData,
    const std::vector<Models::NewsEvent>& recentNews
) {
    Models::TradeDecision decision;
    decision.shouldTrade = false; // Default to no trade

    try {
        if (AppleSilicon::MSeriesOptimizer* mSeriesOptimizer = hardwareManager_->getOptimizer()) {
            // Optimize for real-time analysis
            mSeriesOptimizer->setWorkloadPriority("RealTimeAnalysis");
            mSeriesOptimizer->balancePerformanceEfficiency();
        }

        // Initialize real-time processor if not already done
        if (!marketProcessor_) {
            initializeRealTimeProcessor();
        }

        // Process latest market data and news with hardware acceleration
        marketProcessor_->processNewTick(currentData);
        for (const auto& news : recentNews) {
            marketProcessor_->processNewsEvent(news);
        }

        // Get candlestick patterns with GPU acceleration
        auto patterns = candlestickAnalyzer_->analyzePatterns(historicalData);

        // Get AI model prediction using Neural Engine
        auto modelPrediction = aiModel_->predict(currentData, historicalData, recentNews);

        // Select optimal timeframe using ML acceleration
        auto timeframeState = timeframeLearner_->extractState(historicalData);
        AI::Timeframe::TimeframeSelection selectedTimeframe = timeframeLearner_->selectTimeframe(
            timeframeState,
            {60, 120, 180, 300}  // Available timeframes
        );

        // Get technical indicators with optimized computation
        auto indicators = calculateIndicators(historicalData, convertTimeframeSelection(selectedTimeframe));

        // Make final decision with all optimized components
        // No need to convert AI::Pattern since we're using it directly
        const std::vector<AI::Pattern>& convertedPatterns = patterns;

        // Convert Models::NewsEvent to NewsData for synthesizeFinalDecision
        std::vector<NewsData> newsDataList;
        for (const auto& news : recentNews) {
            NewsData newsData;
            // Copy relevant fields from Models::NewsEvent to NewsData
            newsData.title = news.title;
            newsData.content = news.description;
            newsData.impact = static_cast<double>(static_cast<int>(news.impact));
            newsData.timestamp = std::chrono::system_clock::from_time_t(news.releaseTime.toSecsSinceEpoch());
            newsDataList.push_back(newsData);
        }

        Models::TradeDecision finalDecision = synthesizeFinalDecision(
            convertedPatterns,
            indicators,
            modelPrediction,
            convertTimeframeSelection(selectedTimeframe),
            newsDataList
        );

        // Copy values from finalDecision to decision
        decision = finalDecision;

        // Update models with hardware acceleration
        TradingBrainTradeDecision::updateModels(decision, currentData, recentNews);

    } catch (const std::exception& e) {
        Logger::getInstance().error("Error in trading decision making: " + std::string(e.what()));
        decision.shouldTrade = false;
    }

    return decision;
}

Models::TradeDecision TradingBrain::makeFinalDecision(
    const std::vector<AI::Pattern>& patterns,
    const Models::IndicatorSet& indicators,
    const PredictionResult& aiPrediction,
    const RiskAssessment& risk,
    const TimeframeSelection& timeframe,
    const std::vector<std::shared_ptr<Indicator>>& selectedIndicators,
    const std::vector<Models::NewsEvent>& news
) {
    Models::TradeDecision decision;
    TradeDecisionPerformanceMetrics metrics = {};

    // Don't assign to currentSymbol_ directly from currentData_ which isn't a parameter
    // Instead, use the symbol from the first historical data point if available
    if (!historicalData_.empty()) {
        currentSymbol_ = historicalData_[0].symbol;
    }

    try {
        // 1. Advanced News Impact Analysis
        auto newsImpact = TradingBrainTradeDecision::analyzeDetailedNewsImpact(news, currentSymbol_);
        auto newsImpactWithTime = convertToNewsImpact(newsImpact);
        metrics.newsAnalysisTime = newsImpactWithTime.processingTime;

        // 2. Pattern Strength Evaluation
        auto patternStrength = TradingBrainTradeDecision::evaluatePatternStrength(patterns, historicalVolatility_);
        metrics.patternAnalysisTime = patternStrength.processingTime;

        // 3. Market Regime Detection
        auto regime = TradingBrainTradeDecision::detectMarketRegime(indicators, historicalData_);
        metrics.regimeDetectionTime = regime.processingTime;

        // 4. Composite Scoring System with Profit Analysis
        DecisionScore score = {
            .technical = TradingBrainTradeDecision::calculateTechnicalScore(indicators, selectedIndicators),
            .fundamental = TradingBrainTradeDecision::calculateFundamentalScore(newsImpact),
            .sentiment = TradingBrainTradeDecision::calculateSentimentScore(aiPrediction, newsImpact),
            .momentum = TradingBrainTradeDecision::calculateMomentumScore(indicators, regime),
            .profitPotential = TradingBrainTradeDecision::calculateProfitPotential(historicalData_.back(), regime)  // Use last historical data point instead of currentData_
        };

        // 5. Dynamic Threshold Adjustment
        auto thresholds = TradingBrainTradeDecision::calculateDynamicThresholds(regime, risk, newsImpact.volatilityImpact);

        // 6. Decision Making with Profit Level Filter
        const double MIN_PROFIT_THRESHOLD = 0.80; // 80% minimum profit requirement

        if (score.profitPotential >= MIN_PROFIT_THRESHOLD) {
            // Use the score, thresholds, and risk version of synthesizeFinalDecision instead
            Models::TradeDecision finalDecision = synthesizeFinalDecision(
                score,
                thresholds,
                risk
            );
            decision = finalDecision;
        } else {
            decision.shouldTrade = false;
            Logger::getInstance().info("Trade rejected: Profit potential " +
                std::to_string(score.profitPotential * 100) + "% below threshold");
        }

        // 7. Position Sizing with News Consideration
        if (decision.shouldTrade) {
            decision.suggestedAmount = TradingBrainTradeDecision::calculateNewsAdjustedPosition(
                getMaxPositionSize(risk),
                newsImpact,
                score.composite()
            );

            // Apply sophisticated entry rules
            // Convert entryRules to appropriate format for Models::TradeDecision
            auto entryRules = TradingBrainTradeDecision::generateSmartEntryRules(
                patterns,
                indicators,
                newsImpact,
                regime
            );
            std::vector<std::string> entryRulesList = convertEntryRulesToStringVector(entryRules);

            // Set dynamic stop loss and take profit
            auto riskLevels = TradingBrainTradeDecision::calculateDynamicRiskLevels(
                decision.direction,
                newsImpact,
                regime,
                risk
            );
            decision.risk.potentialLoss = riskLevels.stopLoss;
            decision.risk.potentialProfit = riskLevels.takeProfit;
        }

        // 8. Performance Monitoring
        metrics.decisionTime = TradingBrainTradeDecision::measureExecutionTime([&]() {
            TradingBrainTradeDecision::monitorDecisionQuality(decision, score, newsImpact);
        });

        // Convert metrics to a format we can use for monitoring
        if (performanceMonitor_) {
            // Update the performance metrics directly
            performanceMonitor_->cpuUsage = (metrics.newsAnalysisTime + metrics.patternAnalysisTime +
                                   metrics.regimeDetectionTime + metrics.decisionTime) * 100.0;
            performanceMonitor_->memoryUsage = 0.0; // Set appropriate value if available
            // No need to call logMetrics as performanceMonitor_ is already a Models::PerformanceMetrics object
        }

    } catch (const std::exception& e) {
        Logger::getInstance().error("Error in final decision making: " + std::string(e.what()));
        decision.shouldTrade = false;
    }

    return decision;
}


Models::TradeDecision TradingBrain::synthesizeFinalDecision(
    const std::vector<AI::Pattern>& patterns,
    const Models::IndicatorSet& indicators,
    const PredictionResult& aiPrediction,
    const TimeframeSelection& timeframe,
    const std::vector<NewsData>& recentNews
) {
    try {
        // Create market context
        MarketContext context = TradingBrainTradeDecision::createMarketContext(patterns, indicators, timeframe);

        // Get risk assessment
        RiskAssessment risk = TradingBrainTradeDecision::assessRisk(context, historicalData_);

        // Select relevant indicators
        std::vector<std::shared_ptr<Indicator>> selectedIndicators;
        if (indicatorSelector_) {
            // Convert MarketContext to TradeData::MarketContext for indicator selection
            TradeData::MarketContext tradeContext = convertToTradeDataMarketContext(context);
            auto rawIndicators = indicatorSelector_->selectIndicators(tradeContext);

            // Convert raw indicators to shared_ptr using the factory function
            for (const auto& indicator : rawIndicators) {
                // Extract the indicator name from the shared_ptr and pass it to createIndicator
                // Use the createIndicator function from concrete_indicators.h
                selectedIndicators.push_back(createIndicator(indicator->getName()));
            }
        }

        // Convert NewsData to NewsEvent for makeFinalDecision
        std::vector<Models::NewsEvent> newsEvents;
        for (const auto& news : recentNews) {
            Models::NewsEvent event;
            // Copy relevant fields from NewsData to Models::NewsEvent
            event.title = news.title;
            event.description = news.content;
            event.impact = static_cast<Models::NewsImpact>(static_cast<int>(news.impact));
            event.releaseTime = compat::QtDateTime::fromSecsSinceEpoch(std::chrono::system_clock::to_time_t(news.timestamp));
            newsEvents.push_back(event);
        }

        // Make final decision
        return makeFinalDecision(
            patterns,
            indicators,
            aiPrediction,
            risk,
            timeframe,
            selectedIndicators,
            newsEvents
        );

    } catch (const std::exception& e) {
        Logger::getInstance().error("Error in final decision synthesis: " + std::string(e.what()));
        Models::TradeDecision emptyDecision;
        emptyDecision.shouldTrade = false;
        return emptyDecision;
    }
}

Models::TradeDecision TradingBrain::synthesizeFinalDecision(
    const DecisionScore& score,
    const ThresholdConfig& thresholds,
    const RiskAssessment& risk
) {
    Models::TradeDecision decision;
    decision.shouldTrade = false; // Default to no trade

    try {
        // Calculate composite score
        double compositeScore = score.composite();

        // Determine trade direction based on technical and sentiment scores
        if (score.technical > thresholds.technical_threshold &&
            score.sentiment > thresholds.sentiment_threshold) {
            decision.direction = Models::TradeDirection::CALL;
        } else if (score.technical < -thresholds.technical_threshold &&
                  score.sentiment < -thresholds.sentiment_threshold) {
            decision.direction = Models::TradeDirection::PUT;
        } else {
            // No clear direction, don't trade
            return decision;
        }

        // Check if profit potential exceeds threshold
        if (score.profitPotential < thresholds.profit_threshold) {
            return decision; // Not enough profit potential
        }

        // Check if risk is acceptable
        double calculatedRiskScore = risk.volatilityRisk + risk.marketRisk + risk.patternRisk;
        if (calculatedRiskScore > thresholds.risk_threshold) {
            return decision; // Too risky
        }

        // All conditions met, should trade
        decision.shouldTrade = true;

        // Set confidence based on composite score
        decision.confidence = std::min(0.95, std::max(0.5, compositeScore));

        // Set suggested amount based on risk assessment
        decision.suggestedAmount = risk.positionSize * decision.confidence;

        // Set risk parameters
        decision.risk.riskScore = calculatedRiskScore; // Use the already calculated risk score
        decision.risk.potentialProfit = score.profitPotential;
        decision.risk.potentialLoss = risk.maxRiskPerTrade;
        decision.risk.winProbability = 0.5 + (compositeScore / 2.0); // Convert -1..1 to 0..1 range

        // Set timestamp
        decision.timestamp = std::chrono::system_clock::now();

    } catch (const std::exception& e) {
        Logger::getInstance().error("Error in score-based decision synthesis: " + std::string(e.what()));
        decision.shouldTrade = false;
    }

    return decision;
}

// Helper function to convert AI::Timeframe::TimeframeSelection to TimeframeSelection
TimeframeSelection convertTimeframeSelection(const AI::Timeframe::TimeframeSelection& aiTimeframe) {
    TimeframeSelection result;
    result.timeframe = aiTimeframe.timeframe;
    result.confidence = aiTimeframe.confidence;
    // Other fields would need to be set if needed
    return result;
}

// Helper function to convert Models::NewsImpactAnalysis to NewsDecisionImpact with processingTime
NewsDecisionImpact convertToNewsImpact(const Models::NewsImpactAnalysis& modelNewsImpact) {
    NewsDecisionImpact result;
    result.volatilityImpact = modelNewsImpact.volatilityImpact;
    result.sentimentScore = modelNewsImpact.sentiment;
    result.marketImpact = modelNewsImpact.marketImpact;
    result.processingTime = 0.0; // Default value since Models::NewsImpactAnalysis doesn't have processingTime
    return result;
}

// Helper function to convert MarketContext to TradeData::MarketContext
TradeData::MarketContext convertToTradeDataMarketContext(const MarketContext& context) {
    TradeData::MarketContext tradeContext;

    // Convert relevant fields
    tradeContext.volatility = context.volatility;
    tradeContext.marketTrend = context.trendStrength;
    tradeContext.volumeProfile = context.volume;
    tradeContext.marketSentiment = 0.0; // Default value

    // Convert market phase
    if (context.marketPhase == "markup") {
        tradeContext.marketPhase = "Uptrend";
    } else if (context.marketPhase == "markdown") {
        tradeContext.marketPhase = "Downtrend";
    } else if (context.marketPhase == "accumulation") {
        tradeContext.marketPhase = "Accumulation";
    } else if (context.marketPhase == "distribution") {
        tradeContext.marketPhase = "Distribution";
    } else {
        tradeContext.marketPhase = "Neutral";
    }

    return tradeContext;
}

// Helper function to safely access RiskAssessment properties
double getMaxPositionSize(const RiskAssessment& risk) {
    // Access the maxPositionSize property from RiskAssessment
    return risk.maxRiskPerTrade * 100.0; // Convert from percentage to absolute value
}

// Helper function to convert EntryRules to std::vector<std::string>
std::vector<std::string> convertEntryRulesToStringVector(const EntryRules& rules) {
    // Start with the required conditions from the EntryRules
    std::vector<std::string> result = rules.required_conditions;

    // Add additional information as strings
    result.push_back("Entry price: " + std::to_string(rules.entry_price));
    result.push_back("Stop loss: " + std::to_string(rules.initial_stop_loss));
    result.push_back("Take profit: " + std::to_string(rules.initial_take_profit));

    if (rules.require_pattern_confirmation) {
        result.push_back("Requires pattern confirmation");
    }

    if (rules.require_indicator_alignment) {
        result.push_back("Requires indicator alignment");
    }

    if (rules.require_volume_confirmation) {
        result.push_back("Requires volume confirmation");
    }

    return result;
}

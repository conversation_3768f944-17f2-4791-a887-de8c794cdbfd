#include "binary_options_bot.h"
#include "trading_engine.h"
#include "trade_executor.h"
#include "../strategies/strategy_manager.h"
#include "risk/risk_manager.h"
#include "../../api/pyquotex_api.h"
#include "../recovery/recovery_manager.h"
#include "../analysis/market_analyzer.h"
#include "../events/event_manager.h"
#include "../hardware/hardware_optimizer.h"
#include "../../terminal/components/trading_interface.h"
#include "../../trading/timeframe.h"
#include "../account/account_manager.h"
#include "../data/data_manager.h"
#include "../trading/concrete_trading_interface.h"
#include "../../autopilot/auto_pilot_manager.h"
#include "../../trading/automated_risk_controller.h"
#include "global_instance.h"

// AI/ML includes
#include "../../ai/ml/advanced_ai_model.h"
#include "../../ai/realtime/market_processor.h"
#include "../../ai/patterns/pattern_recognizer.h"
#include "../../ai/config/ai_config.h"
#include "../../ai/config/market_processor_config.h"
#include "../../utils/logger.h"
#include <nlohmann/json.hpp>

using json = nlohmann::json;
using namespace BinaryOptionComponents;
using namespace Trading;
using namespace Trading::Risk;
using namespace AI::Realtime;

BinaryOptionsBot::BinaryOptionsBot(const Models::TradingConfig& config, std::shared_ptr<TradingAPI> tradingApi)
    : TradingEngine(config)
    , config_(config)
    , currentMode_(TradingMode::DEMO)
    , isRunning_(false)
    , isPaused_(false)
    , currentRiskLevel_(config.baseRiskLevel)
    , totalTrades_(0)
    , isTournamentMode_(false)
    , tradingApi_(tradingApi)
    , api_(tradingApi) { // Set api_ to the same value as tradingApi_ for getTradingApi() method
    Logger::getInstance().log(Logger::LogLevel::INFO,"Initializing Binary Options Bot...");

    try {
        // Initialize core services
        // Note: We no longer need to specify the WebSocket endpoint directly
        // The PyQuotex REST API handles the connection details internally

        std::cout << "[INFO] Using PyQuotex REST API for Quotex API connection" << std::endl;

        // Check if we have a trading API provided
        if (tradingApi_) {
            std::cout << "[INFO] Using provided PyQuotexAPI with REST API implementation" << std::endl;
        } else {
            // No trading API provided - try to create one
            std::cout << "[WARNING] No trading API provided. Attempting to create a PyQuotexAPI instance." << std::endl;
            Logger::getInstance().log(Logger::LogLevel::WARNING, "No trading API provided. Attempting to create a PyQuotexAPI instance.");

            try {
                // Get the PyQuotexAPI singleton instance
                // The PyQuotexAPI singleton will start the REST API server if needed
                // and connect to it automatically
                std::string configPath = "/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/pyquotex-master/settings/config.ini";
                tradingApi_ = PyQuotexAPI::getInstance(configPath);
                api_ = tradingApi_; // Also set api_ for getTradingApi() method

                if (tradingApi_ && tradingApi_->isConnected()) {
                    std::cout << "[INFO] Successfully created PyQuotexAPI instance and connected to REST API" << std::endl;
                    Logger::getInstance().log(Logger::LogLevel::INFO, "Successfully created PyQuotexAPI instance and connected to REST API");
                } else {
                    std::cout << "[ERROR] Failed to create PyQuotexAPI instance or connect to REST API" << std::endl;
                    Logger::getInstance().log(Logger::LogLevel::ERROR, "Failed to create PyQuotexAPI instance or connect to REST API");
                    std::cout << "[INFO] Continuing with limited functionality" << std::endl;
                }
            } catch (const std::exception& e) {
                std::cout << "[ERROR] Failed to create PyQuotexAPI instance: " << e.what() << std::endl;
                Logger::getInstance().log(Logger::LogLevel::ERROR, "Failed to create PyQuotexAPI instance: " + std::string(e.what()));
                std::cout << "[INFO] Continuing with limited functionality" << std::endl;
            }
        }

        // We no longer need separate API callbacks since we're using the TradingAPI interface
        // The callbacks are set up in the TradingAPI section below

        // Set up callbacks for the TradingAPI
        if (tradingApi_) {
            tradingApi_->onMarketDataUpdate([this](const MarketData& data) {
                try {
                    // Check if marketAnalyzer_ is initialized before using it
                    if (marketAnalyzer_) {
                        marketAnalyzer_->processNewData(data);
                    }

                    // Emit the newMarketData signal for the UI
                    if (data.price > 0) {
                        emit newMarketData(data.price, QString::fromStdString(data.symbol));
                    }

                    // Process market regime changes
                    if (marketAnalyzer_) {
                        try {
                            auto regime = marketAnalyzer_->getCurrentMarketRegime();
                            // Convert regime to string manually
                            std::string regimeStr;
                            if (regime.volatility > 0.5) {
                                regimeStr = "High Volatility";
                            } else if (regime.volatility > 0.2) {
                                regimeStr = "Medium Volatility";
                            } else {
                                regimeStr = "Low Volatility";
                            }
                            emit marketRegimeChanged(QString::fromStdString(regimeStr));
                        } catch (const std::exception& e) {
                            Logger::getInstance().log(Logger::LogLevel::ERROR, "Error processing market regime: " + std::string(e.what()));
                        }
                    }

                    // Process pattern detection - real implementation
                    if (patternRecognizer_) {
                        try {
                            // Get actual patterns from the recognizer
                            auto patterns = patternRecognizer_->analyzePatterns(data);

                            // The confidence is already set in the analyzePatterns method

                            // Emit signals for each detected pattern
                            for (const auto& pattern : patterns) {
                                // Extract pattern name and confidence
                                QString patternName = QString::fromStdString(pattern.name);
                                double confidence = pattern.confidence;

                                // Only emit signals for patterns with reasonable confidence
                                if (confidence > 0.5) {
                                    emit patternDetected(patternName, confidence);

                                    // Log the pattern detection
                                    Logger::getInstance().log(Logger::LogLevel::INFO,
                                        "Pattern detected: " + pattern.name +
                                        " (Confidence: " + std::to_string(confidence) + ")");
                                }
                            }

                            // If no patterns were detected with sufficient confidence, emit a generic signal
                            if (patterns.empty()) {
                                // Generate a pattern based on price movement
                                QString patternName;
                                double confidence = 0.6;

                                // Simple price movement pattern detection
                                if (data.candles.size() >= 2) {
                                    double lastPrice = data.candles[data.candles.size() - 1].close;
                                    double prevPrice = data.candles[data.candles.size() - 2].close;

                                    if (lastPrice > prevPrice) {
                                        patternName = "Bullish Movement";
                                        confidence = 0.6 + (lastPrice - prevPrice) / prevPrice;
                                    } else if (lastPrice < prevPrice) {
                                        patternName = "Bearish Movement";
                                        confidence = 0.6 + (prevPrice - lastPrice) / prevPrice;
                                    } else {
                                        patternName = "Sideways Movement";
                                        confidence = 0.6;
                                    }

                                    // Cap confidence at 0.95
                                    confidence = std::min(confidence, 0.95);

                                    emit patternDetected(patternName, confidence);
                                } else if (!data.priceHistory.empty() && data.priceHistory.size() >= 2) {
                                    // Fallback to priceHistory if candles are not available
                                    double lastPrice = data.priceHistory[data.priceHistory.size() - 1];
                                    double prevPrice = data.priceHistory[data.priceHistory.size() - 2];

                                    if (lastPrice > prevPrice) {
                                        patternName = "Bullish Movement";
                                        confidence = 0.6 + (lastPrice - prevPrice) / prevPrice;
                                    } else if (lastPrice < prevPrice) {
                                        patternName = "Bearish Movement";
                                        confidence = 0.6 + (prevPrice - lastPrice) / prevPrice;
                                    } else {
                                        patternName = "Sideways Movement";
                                        confidence = 0.6;
                                    }

                                    // Cap confidence at 0.95
                                    confidence = std::min(confidence, 0.95);

                                    emit patternDetected(patternName, confidence);
                                } else {
                                    // If no historical data is available, emit a generic signal
                                    emit patternDetected(QString::fromStdString("Price Movement"), 0.6);
                                }
                            }
                        } catch (const std::exception& e) {
                            Logger::getInstance().log(Logger::LogLevel::ERROR,
                                "Error in pattern detection: " + std::string(e.what()));

                            // Emit a fallback pattern signal to prevent Qt connection warnings
                            emit patternDetected(QString::fromStdString("Price Movement"), 0.6);
                        }
                    }

                    if (marketDataCallback_) {
                        marketDataCallback_(data);
                    }
                } catch (const std::exception& e) {
                   Logger::getInstance().log(Logger::LogLevel::ERROR, "Error processing market data: " + std::string(e.what()));;
                }
            });

            tradingApi_->onOrderUpdate([this](const Order& order) {
                try {
                    // Handle order update
                    Logger::getInstance().log(Logger::LogLevel::INFO, "Order update received: " + order.orderId);
                } catch (const std::exception& e) {
                    Logger::getInstance().log(Logger::LogLevel::ERROR, "Error processing order update: " + std::string(e.what()));
                }
            });

            tradingApi_->onAccountUpdate([this](const AccountInfo& accountInfo) {
                try {
                    // Handle account update
                    Logger::getInstance().log(Logger::LogLevel::INFO, "Account update received: " + std::to_string(accountInfo.balance));
                } catch (const std::exception& e) {
                    Logger::getInstance().log(Logger::LogLevel::ERROR, "Error processing account update: " + std::string(e.what()));
                }
            });

            tradingApi_->onError([this](const std::string& error) {
                Logger::getInstance().log(Logger::LogLevel::ERROR, "Trading API error: " + error);
            });
        }

        // Initialize database connections
        // Initialize Redis with connection settings from config
        try {
            // Check if we have Redis config in the config file
            std::cout << "[DEBUG] Redis configuration in BinaryOptionsBot constructor:" << std::endl;
            std::cout << "[DEBUG] redisHost: '" << config.redisHost << "'" << std::endl;
            std::cout << "[DEBUG] redisPort: " << config.redisPort << std::endl;
            std::cout << "[DEBUG] redisUsername: '" << config.redisUsername << "'" << std::endl;
            std::cout << "[DEBUG] redisUseSSL: " << (config.redisUseSSL ? "true" : "false") << std::endl;

            // Use the RedisManager singleton instead of creating a new instance
            if (config.redisHost.empty()) {
                std::cout << "[INFO] Redis host not specified in config, using default localhost" << std::endl;
                redisManager_ = RedisManager::getInstance();
            } else {
                std::cout << "[INFO] Connecting to Redis at " << config.redisHost << ":" << config.redisPort << std::endl;
                redisManager_ = RedisManager::getInstance(
                    config.redisHost,
                    config.redisPort,
                    config.redisUsername,
                    config.redisPassword,
                    config.redisUseSSL
                );
            }

            // Try to connect to Redis
            redisManager_->connect();
        } catch (const std::exception& e) {
            std::cerr << "[ERROR] Failed to initialize Redis: " << e.what() << std::endl;
            // Use the RedisManager singleton with default parameters
            redisManager_ = RedisManager::getInstance();
        }

        // Initialize MongoDB with connection string from config
        try {
            // Check if MongoDB is already initialized globally
            if (!g_mongoInitialized.load(std::memory_order_acquire)) {
                // Increment the initialization attempts counter
                g_initializationAttempts.fetch_add(1, std::memory_order_relaxed);

                // If we've tried too many times, just use a mock implementation
                if (g_initializationAttempts.load(std::memory_order_relaxed) > 3) {
                    std::cout << "[WARNING] Too many MongoDB initialization attempts, using mock implementation" << std::endl;
                    mongoManager_ = MongoDBManager::getInstance("");
                    g_mongoInitialized.store(true, std::memory_order_release);
                    return;
                }

                // Check if MongoDB is already running before trying to connect
                std::cout << "[INFO] Checking if MongoDB is already running..." << std::endl;
                std::string checkCommand = "lsof -i :27017 > /dev/null 2>&1";
                int mongoRunning = std::system(checkCommand.c_str());

                if (mongoRunning == 0) {
                    std::cout << "[INFO] MongoDB is already running, using existing instance" << std::endl;
                    // Use the existing MongoDB instance
                    mongoManager_ = MongoDBManager::getInstance("mongodb://localhost:27017");
                } else {
                    std::cout << "[INFO] MongoDB not running, using mock implementation" << std::endl;
                    // Use mock implementation if MongoDB is not running
                    mongoManager_ = MongoDBManager::getInstance("");
                }

                // Mark as initialized globally to prevent multiple attempts
                g_mongoInitialized.store(true, std::memory_order_release);

                // Add a check to see if MongoDB is actually connected
                if (mongoManager_ && !mongoManager_->isConnected()) {
                    std::cout << "[INFO] MongoDB connection not available, using mock implementation" << std::endl;
                }
            } else {
                std::cout << "[INFO] MongoDB already initialized globally, reusing existing instance" << std::endl;
                mongoManager_ = MongoDBManager::getInstance();
            }
        } catch (const std::exception& e) {
            std::cerr << "[ERROR] Failed to initialize MongoDB: " << e.what() << std::endl;
            std::cerr << "[INFO] Continuing with mock implementation" << std::endl;

            // Get the MongoDB singleton instance with default connection string
            // This will create a mock implementation that doesn't try to connect
            mongoManager_ = MongoDBManager::getInstance("");

            // Mark as initialized globally to prevent further attempts
            g_mongoInitialized.store(true, std::memory_order_release);
        }

        // ✅ FIXED: Don't call ensureComponentsInitialized() in constructor to prevent recursion
        // Components will be initialized later via lazy initialization when needed
        Logger::getInstance().log(Logger::LogLevel::INFO, "BinaryOptionsBot constructor completed, components will be initialized later");

        // Configure hardware optimization for bot operation
        optimizeHardwareUsage();

        // Connect to Quotex
        Logger::getInstance().log(Logger::LogLevel::INFO, "Connecting to Quotex via PyQuotex REST API...");

        // Connect to Quotex API
        if (tradingApi_) {
            // Connect to Quotex API using PyQuotexAPI
            Logger::getInstance().log(Logger::LogLevel::INFO, "Connecting to Quotex API using PyQuotexAPI...");
            Logger::getInstance().log(Logger::LogLevel::INFO, "Current trading mode: " + std::string(currentMode_ == TradingMode::DEMO ? "DEMO" : "REAL"));

            // Add retry loop for connection
            bool connected = false;
            int maxRetries = 5;
            int retryCount = 0;

            while (!connected && retryCount < maxRetries) {
                // The PyQuotexAPI class handles the connection to the REST API
                // We just need to pass the trading mode (demo or real)
                // The REST API server handles the actual connection to Quotex
                tradingApi_->connect(currentMode_ == TradingMode::DEMO ? "demo" : "real");
                Logger::getInstance().log(Logger::LogLevel::INFO, "Connection attempt " + std::to_string(retryCount + 1) + " completed");

                // Check if connected
                connected = tradingApi_->isConnected();

                if (connected) {
                    Logger::getInstance().log(Logger::LogLevel::INFO, "Successfully connected to Quotex API via REST API");
                } else {
                    Logger::getInstance().log(Logger::LogLevel::WARNING, "Connection attempt " + std::to_string(retryCount + 1) + " failed, retrying...");
                    retryCount++;
                    // Wait before retrying
                    std::this_thread::sleep_for(std::chrono::seconds(2));
                }
            }

            if (!connected) {
                Logger::getInstance().log(Logger::LogLevel::ERROR, "Failed to connect to Quotex API after " + std::to_string(maxRetries) + " attempts");
            }
        } else {
            Logger::getInstance().log(Logger::LogLevel::ERROR, "Trading API not available");
        }

        // We already tried to connect above, so we just check the connection status here
        try {
            bool connected = false;

            // Check if we're already connected
            if (tradingApi_) {
                connected = tradingApi_->isConnected();
                if (connected) {
                    Logger::getInstance().log(Logger::LogLevel::INFO, "Successfully connected to Quotex using PyQuotex REST API");
                } else {
                    Logger::getInstance().log(Logger::LogLevel::WARNING, "Failed to connect to Quotex using PyQuotex REST API");
                }
            }

            // If TradingAPI connection failed, log the error
            if (!connected) {
                Logger::getInstance().log(Logger::LogLevel::WARNING, "Failed to connect to Quotex");
                std::cout << "[WARNING] Connection to Quotex failed, but continuing with limited functionality" << std::endl;
                std::cout << "[INFO] You can still use the application's UI and other features" << std::endl;
                // Don't throw, just log the error and continue with limited functionality
            }
        } catch (const std::exception& e) {
            Logger::getInstance().log(Logger::LogLevel::ERROR, "Connection error: " + std::string(e.what()));
            std::cout << "[WARNING] Connection to Quotex failed: " << e.what() << std::endl;
            std::cout << "[INFO] You can still use the application's UI and other features" << std::endl;
            // Don't throw, just log the error and continue with limited functionality
        }

    } catch (const std::exception& e) {
        Logger::getInstance().log(Logger::LogLevel::ERROR, "Failed to initialize bot: " + std::string(e.what()));
        // Don't rethrow, just log the error and continue with limited functionality
        // This prevents segmentation faults when initialization fails
    }

    Logger::getInstance().log(Logger::LogLevel::INFO, "Binary Options Bot initialized successfully");

    // Only initialize Auto Pilot if we have the required components
    if (tradingApi_ && tradingApi_->isConnected() && aiModel_) {
        try {
            // Initialize Auto Pilot after other components
            // Convert unique_ptr to shared_ptr for parameters that require shared_ptr
            std::shared_ptr<Trading::AutomatedRiskController> sharedRiskController = std::make_shared<Trading::AutomatedRiskController>();

            // Check if aiModel_ is valid before using it
            if (aiModel_) {
                std::shared_ptr<AI::ML::AdvancedAIModel> sharedAiModel = std::shared_ptr<AI::ML::AdvancedAIModel>(aiModel_.get(), [](AI::ML::AdvancedAIModel*){});

                autoPilot_ = std::make_unique<AutoPilotManager>(
                    shared_from_this(),
                    sharedRiskController,
                    sharedAiModel
                );

                Logger::getInstance().log(Logger::LogLevel::INFO, "Auto Pilot initialized successfully");
            } else {
                Logger::getInstance().log(Logger::LogLevel::WARNING, "Auto Pilot initialization skipped: AI model not available");
            }
        } catch (const std::exception& e) {
            Logger::getInstance().log(Logger::LogLevel::ERROR, "Failed to initialize Auto Pilot: " + std::string(e.what()));
            // Continue without Auto Pilot
        }
    } else {
        Logger::getInstance().log(Logger::LogLevel::WARNING, "Auto Pilot initialization skipped: required components not available");
    }
}

// Remove incorrect private declaration
int currentPhase = 0;
double accumulatedReserves_ = 0.0;
std::map<int, int> phaseRepetitions_;

// Implementation moved to binary_options_bot_impl.cpp
// void BinaryOptionsBot::setTradingMode(TradingMode mode) {
//     currentMode_ = mode;
//     std::string modeStr = (mode == TradingMode::DEMO ? "Demo" : "Live");
//     Logger::getInstance().log(Logger::LogLevel::INFO, std::string("Trading mode set to: ") + modeStr);
//
//     // Create proper RiskParameters object
//     Trading::Risk::RiskParameters params;
//     if (mode == TradingMode::DEMO) {
//         params.volatilityLevel = 0.1; // 10% max risk for demo
//     } else {
//         params.volatilityLevel = config_.maxRiskPerTrade;
//     }
//     params.currentBalance = config_.initialBalance;
//     params.regime = Trading::MarketRegime(); // Create a proper MarketRegime object
//
//     riskManager_->updateRiskParameters(params);
// }

void BinaryOptionsBot::startDemoTrading(double initialBalance) {
    if (isRunning_) {
        Logger::getInstance().log(Logger::LogLevel::WARNING, "Trading session already active");
        return;
    }

    if (initialBalance <= 0) {
        Logger::getInstance().log(Logger::LogLevel::ERROR, "Invalid initial balance for demo trading");
        return;
    }

    try {
        // Ensure components are initialized before starting trading
        ensureComponentsInitialized();

        setTradingMode(TradingMode::DEMO);
        config_.initialBalance = initialBalance;

        if (!tradingApi_->isConnected()) {
            // Connect to the REST API in demo mode
            tradingApi_->connect("demo");

            // Wait a moment for the connection to establish
            std::this_thread::sleep_for(std::chrono::seconds(2));

            if (!tradingApi_->isConnected()) {
                throw std::runtime_error("Failed to connect to REST API");
            }
        }

        isRunning_ = true;
        isPaused_ = false;
        currentMode_ = TradingMode::DEMO;

        startTradingSession(initialBalance, config_.numTrades, config_.timeframeSeconds);

        Logger::getInstance().log(Logger::LogLevel::INFO,
            "Started demo trading with initial balance: " + std::to_string(initialBalance));

    } catch (const std::exception& e) {
        Logger::getInstance().log(Logger::LogLevel::ERROR,
            std::string("Failed to start demo trading: ") + e.what());
    }
}

TradingMode BinaryOptionsBot::getTradingMode() const {
    return currentMode_;
}

// isConnected() implementation moved to binary_options_bot_impl.cpp

void BinaryOptionsBot::startRealTrading(double initialAmount) {
    if (isRunning_) {
        Logger::getInstance().log(Logger::LogLevel::WARNING, "Cannot change trading mode while bot is running");
        return;
    }
    if (initialAmount <= 0) {
        Logger::getInstance().log(Logger::LogLevel::ERROR, "Invalid initial amount for real trading");
        return;
    }
    if (!tradingApi_ || !tradingApi_->isConnected()) {
        Logger::getInstance().log(Logger::LogLevel::ERROR, "Cannot start real trading without active API connection");
        return;
    }

    // Ensure components are initialized before starting trading
    ensureComponentsInitialized();

    currentMode_ = TradingMode::REAL;
    Logger::getInstance().log(Logger::LogLevel::INFO, "Starting Real trading with initial amount: " + std::to_string(initialAmount));
    startTradingSession(initialAmount, config_.numTrades, config_.timeframeSeconds);
}

void BinaryOptionsBot::startTournamentTrading(double initialAmount) {
    if (isRunning_) {
        Logger::getInstance().log(Logger::LogLevel::WARNING, "Cannot change trading mode while bot is running");
        return;
    }
    if (initialAmount <= 0) {
        Logger::getInstance().log(Logger::LogLevel::ERROR, "Invalid initial amount for tournament trading");
        return;
    }
    if (!tradingApi_ || !tradingApi_->isConnected()) {
        Logger::getInstance().log(Logger::LogLevel::ERROR, "Cannot start tournament trading without active API connection");
        return;
    }

    // Ensure components are initialized before starting trading
    ensureComponentsInitialized();

    currentMode_ = TradingMode::TOURNAMENT;
    Logger::getInstance().log(Logger::LogLevel::INFO, "Starting Tournament trading with initial amount: " + std::to_string(initialAmount));
    startTradingSession(initialAmount, config_.numTrades, config_.timeframeSeconds);
}

void BinaryOptionsBot::initializeComponents() {
    // ✅ PROTECTION: Prevent recursive initialization
    static thread_local bool alreadyInitializing = false;
    if (alreadyInitializing) {
        Logger::getInstance().log(Logger::LogLevel::WARNING, "❗ Re-entrant BinaryOptionsBot::initializeComponents() blocked to prevent infinite recursion");
        return;
    }
    alreadyInitializing = true;

    try {
        // Use TradingAPI if available
        if (tradingApi_) {
            // Initialize core components with TradingAPI
            tradingBrain_ = std::make_unique<TradingBrain>(tradingApi_);

            // Use the BinaryOptionsTradeExecutorSingleton instead of creating a new instance
            // This prevents circular dependencies and segmentation faults
            tradeExecutor_ = BinaryOptionsTradeExecutorSingleton::getInstance();

            // Set the trading API for the trade executor if needed
            if (tradeExecutor_ && tradingApi_) {
                tradeExecutor_->setTradingAPI(tradingApi_.get());
            }
        } else {
            // Initialize core components without API
            tradingBrain_ = std::make_unique<TradingBrain>();

            // Still get the singleton instance, but don't set the API
            tradeExecutor_ = BinaryOptionsTradeExecutorSingleton::getInstance();

            Logger::getInstance().log(Logger::LogLevel::ERROR, "No trading API available for trade executor");
        }

        strategyManager_ = std::make_unique<StrategyManager>(this);
        riskManager_ = std::make_unique<Trading::Risk::RiskManager>();
        recoveryManager_ = std::make_unique<RecoveryManager>();
        marketAnalyzer_ = std::make_unique<MarketAnalyzer>();
        eventManager_ = std::make_unique<EventManager>();
        hardwareOptimizer_ = std::make_unique<HardwareOptimizer>();
        tradingInterface_ = std::make_unique<BinaryOptionComponents::ConcreteTradingInterface>();

        // Initialize NewsService with multi-API support
        newsService_ = std::make_unique<NewsService>();
        newsService_->initialize("default"); // Will use the hardcoded API keys

        // Set up news callback to update recentNews_
        newsService_->setNewsCallback([this](const Models::NewsEvent& event) {
            updateRecentNews(event);
        });

        // Start news monitoring
        newsService_->startNewsMonitoring();

        // Explicitly connect to Redis if we have a Redis manager
        if (redisManager_) {
            std::cout << "[INFO] Explicitly connecting to Redis from initializeComponents()" << std::endl;
            redisManager_->connect();

            // Check if we're connected
            if (redisManager_->isConnected()) {
                std::cout << "[INFO] Successfully connected to Redis from initializeComponents()" << std::endl;
            } else {
                std::cout << "[WARNING] Failed to connect to Redis from initializeComponents()" << std::endl;
            }
        }

        // Initialize AI components
        try {
            // Initialize pattern recognizer
            patternRecognizer_ = std::make_unique<AI::Patterns::PatternRecognizer>();

            // Subscribe to market data for common symbols
            if (tradingApi_) {
                // Subscribe to common currency pairs
                tradingApi_->subscribeToMarketData("EURUSD");
                tradingApi_->subscribeToMarketData("GBPUSD");
                tradingApi_->subscribeToMarketData("USDJPY");
                tradingApi_->subscribeToMarketData("AUDUSD");

                // Note: We don't need to call startMarketDataPolling() explicitly
                // The PyQuotexAPI REST implementation handles this internally when subscribeToMarketData is called

                Logger::getInstance().log(Logger::LogLevel::INFO, "Subscribed to market data for common symbols");
            } else {
                Logger::getInstance().log(Logger::LogLevel::WARNING, "Cannot subscribe to market data: Trading API not available");
            }
        } catch (const std::exception& e) {
            Logger::getInstance().log(Logger::LogLevel::ERROR, "Failed to initialize AI components: " + std::string(e.what()));
        }

        // Mark components as initialized
        componentsInitialized_ = true;
        Logger::getInstance().log(Logger::LogLevel::INFO, "BinaryOptionsBot components initialization completed");

    } catch (const std::exception& e) {
        Logger::getInstance().log(Logger::LogLevel::ERROR, "Failed to initialize BinaryOptionsBot components: " + std::string(e.what()));
    }

    // ✅ RESET: Reset the initialization flag
    alreadyInitializing = false;
}

void BinaryOptionsBot::ensureComponentsInitialized() {
    if (!componentsInitialized_) {
        Logger::getInstance().log(Logger::LogLevel::INFO, "Initializing BinaryOptionsBot components on demand");
        initializeComponents();
    }
}

// Move function implementations outside of initializeAIComponents
void BinaryOptionsBot::updateAIModels(const MarketData& currentData) {
    try {
        if (aiModel_) {
            aiModel_->updateModel(currentData);  // Instead of update
        }
    } catch (const std::exception& e) {
        Logger::getInstance().log(Logger::LogLevel::ERROR,"Failed to update AI models: " + std::string(e.what()));
    }
}

void BinaryOptionsBot::initializeTournamentMode(const TournamentConfig& config) {
    if (isRunning_) {
        Logger::getInstance().log(Logger::LogLevel::WARNING, "Cannot initialize tournament mode while trading is active");
        return;
    }

    isTournamentMode_ = true;
    tournamentConfig_ = config;

    tournamentRanking_.clear();
    tournamentMetrics_ = TournamentMetrics{};

    Logger::getInstance().log(Logger::LogLevel::INFO, "Tournament mode initialized with " +
                std::to_string(config.maxParticipants) + " max participants");
}

void BinaryOptionsBot::updateTournamentRanking() {
    if (!isTournamentMode_) return;

    try {
        tournamentMetrics_.rank = getTournamentRank();
        tournamentMetrics_.score = getTournamentScore();

        if (mongoManager_) {
            mongoManager_->updateRanking(tournamentMetrics_);
        }

        emit tournamentRankingUpdated(tournamentMetrics_);

        Logger::getInstance().log(Logger::LogLevel::DEBUG, "Tournament ranking updated: rank=" + std::to_string(tournamentMetrics_.rank) +
                      ", score=" + std::to_string(tournamentMetrics_.score));
    } catch (const std::exception& e) {
        Logger::getInstance().log(Logger::LogLevel::ERROR, "Failed to update tournament ranking: " + std::string(e.what()));
    }
}

void BinaryOptionsBot::analyzeMicrostructure(const MarketData& data) {
    try {
        auto microstructure = std::make_unique<BinaryOptionComponents::MarketMicrostructure>();

        if (data.orderFlow) {
            microstructure->processOrderFlow(data.orderFlow.value());  // Changed from processFlow
        }
        if (data.marketDepth) {
            microstructure->processMarketDepth(data.marketDepth.value());  // Changed from processDepth
        }
        if (data.volumeProfile) {
            microstructure->processVolumeData(data.volumeProfile.value());  // Changed from processVolume
        }

        marketState_.updateMicrostructure(std::move(microstructure));

        if (strategyManager_) {
            strategyManager_->onMicrostructureUpdate(marketState_);
        }
    } catch (const std::exception& e) {
        Logger::getInstance().log(Logger::LogLevel::ERROR, "Microstructure analysis failed: " + std::string(e.what()));
    }
}

// Implementation moved to binary_options_bot_impl.cpp
// void BinaryOptionsBot::implementDynamicRiskStrategy() {
//     try {
//         auto currentMarketRegime = marketAnalyzer_->getCurrentMarketRegime();
//         auto volatilityLevel = marketAnalyzer_->getVolatilityLevel();
//
//         // Adjust risk parameters based on market conditions
//         double baseRisk = config_.baseRiskLevel;
//
//         // Update risk manager with new parameters
//         Trading::Risk::RiskParameters riskParams;
//         riskParams.volatilityLevel = volatilityLevel;
//         riskParams.regime = Trading::MarketRegime(); // Create a proper MarketRegime object
//         riskParams.currentBalance = getCurrentBalance();
//
//         riskManager_->updateRiskParameters(riskParams);
//
//         // Rest of your implementation...
//     } catch (const std::exception& e) {
//         Logger::getInstance().log(Logger::LogLevel::ERROR, "Failed to implement dynamic risk strategy: " + std::string(e.what()));
//     }
// }

bool BinaryOptionsBot::validateAISignal(const MarketData& data) {
    try {
        // Get AI prediction
        auto prediction = aiModel_->predict(data, getHistoricalData(), getRecentNews());  // Include recent news data

        // Validate prediction confidence
        if (prediction.confidenceScore < config_.minAIConfidence) {
            Logger::getInstance().log(Logger::LogLevel::DEBUG, "AI signal rejected: confidence too low");
            return false;
        }

        // Check pattern recognition
        auto patterns = marketAnalyzer_->analyzePatterns();
        if (!patterns.empty()) {
            double patternConfidence = marketAnalyzer_->validatePatternQuality(patterns.front());
            if (patternConfidence < config_.minPatternConfidence) {
                Logger::getInstance().log(Logger::LogLevel::DEBUG, "AI signal rejected: pattern confidence too low");
                return false;
            }
        }

        // Validate market conditions
        bool validMarketConditions = marketAnalyzer_->validateTradingConditions(marketAnalyzer_->getCurrentMarketRegime());
        if (!validMarketConditions) {
            Logger::getInstance().log(Logger::LogLevel::DEBUG, "AI signal rejected: unsuitable market conditions");
            return false;
        }

        return true;
    } catch (const std::exception& e) {
        Logger::getInstance().log(Logger::LogLevel::ERROR, "AI signal validation failed: " + std::string(e.what()));
        return false;
    }
}

double BinaryOptionsBot::calculateConfidenceScore(const Models::AIPrediction& prediction) {
    try {
        double baseConfidence = prediction.confidenceScore;
        double marketContextScore = marketAnalyzer_->getTrendStrength();
        double patternConfidence = 0.0;
        auto patterns = marketAnalyzer_->analyzePatterns();
        if (!patterns.empty()) {
            patternConfidence = marketAnalyzer_->validatePatternQuality(patterns.front());
        }

        // Weighted average of confidence factors
        const double PATTERN_WEIGHT = 0.4;
        const double MARKET_WEIGHT = 0.3;
        const double AI_WEIGHT = 0.3;

        return (baseConfidence * PATTERN_WEIGHT) +
               (marketContextScore * MARKET_WEIGHT) +
               (patternConfidence * AI_WEIGHT);
    } catch (const std::exception& e) {
        Logger::getInstance().log(Logger::LogLevel::ERROR,
            "Failed to calculate AI confidence score: " + std::string(e.what()));
        return 0.0;
    }
}

// Implementation moved to binary_options_bot_impl.cpp
// void BinaryOptionsBot::handleAIPrediction(const Models::AIPrediction& prediction) {
//     try {
//         // Update strategy manager with new prediction
//         // Update strategy based on prediction
//         if (prediction.confidenceScore > config_.highConfidenceThreshold) {
//             Trading::Risk::RiskParameters params;
//             params.volatilityLevel = marketAnalyzer_->getVolatilityLevel();
//             params.regime = marketAnalyzer_->getCurrentMarketRegime();
//             params.currentBalance = getCurrentBalance();
//             riskManager_->updateRiskParameters(params);
//         }
//
//         // Notify UI of new prediction
//         emit aiPredictionReady(QString::fromStdString(prediction.direction + " (" + std::to_string(prediction.confidenceScore) + ")"));
//
//         // Log prediction details
//         Logger::getInstance().log(Logger::LogLevel::DEBUG, "New AI prediction: Direction=" + prediction.direction + ", Confidence=" + std::to_string(prediction.confidenceScore));
//     } catch (const std::exception& e) {
//         Logger::getInstance().log(Logger::LogLevel::ERROR, "Failed to handle AI prediction: " + std::string(e.what()));
//     }
// }

// Implementation moved to binary_options_bot_impl.cpp
// void BinaryOptionsBot::updateConfig(const Models::TradingConfig& config) {
//     // Update base class config
//     TradingEngine::updateConfig(config);
//
//     // Update risk management settings
//     Trading::Risk::RiskParameters riskParams;
//     riskParams.volatilityLevel = marketAnalyzer_->getVolatilityLevel();
//     riskParams.regime = marketAnalyzer_->getCurrentMarketRegime();
//     riskParams.currentBalance = getCurrentBalance();
//     riskManager_->updateRiskParameters(riskParams);
//
//     // Update trading interface settings
//     if (tradingInterface_) {
//         tradingInterface_->updateParameters(config.minTradeAmount, config.maxTradeAmount);
//     }
//
//     // Log configuration update
//     Logger::getInstance().log(Logger::LogLevel::INFO, "Trading configuration updated successfully");
// }

bool BinaryOptionsBot::isHealthy() const {
    try {
        // Check core components health - allow some components to be missing
        bool componentsHealthy = true;

        // Log which components are missing
        if (!tradingInterface_) {
            Logger::getInstance().log(Logger::LogLevel::WARNING, "Trading interface not initialized");
            // Not critical, can continue
        }

        if (!marketAnalyzer_) {
            Logger::getInstance().log(Logger::LogLevel::WARNING, "Market analyzer not initialized");
            // Not critical, can continue
        }

        if (!riskManager_) {
            Logger::getInstance().log(Logger::LogLevel::WARNING, "Risk manager not initialized");
            // Not critical, can continue
        }

        if (!recoveryManager_) {
            Logger::getInstance().log(Logger::LogLevel::WARNING, "Recovery manager not initialized");
            // Not critical, can continue
        }

        if (!strategyManager_) {
            Logger::getInstance().log(Logger::LogLevel::WARNING, "Strategy manager not initialized");
            // Not critical, can continue
        }

        // Check trading state
        bool stateHealthy = !isPaused_ && (!isRunning_ || isRunning_);

        // Check external services - allow some services to be missing
        bool apiHealthy = tradingApi_ != nullptr;
        bool apiConnected = apiHealthy && tradingApi_->isConnected();

        if (!apiConnected) {
            Logger::getInstance().log(Logger::LogLevel::WARNING, "API not connected - running in limited functionality mode");
            // This is important but we can still run in limited functionality mode
        }

        bool redisHealthy = redisManager_ != nullptr;

        // Only check Redis connection if we haven't already logged a warning
        static bool redisWarningLogged = false;
        bool redisConnected = redisHealthy && redisManager_->isConnected();

        if (!redisConnected && !redisWarningLogged) {
            Logger::getInstance().log(Logger::LogLevel::WARNING, "Redis not connected - running without Redis functionality");
            redisWarningLogged = true;
            // Not critical, can continue
        } else if (redisConnected && redisWarningLogged) {
            // If we're now connected but previously logged a warning, log that we're connected
            Logger::getInstance().log(Logger::LogLevel::INFO, "Redis connection restored");
            redisWarningLogged = false;
        }

        bool mongoHealthy = mongoManager_ != nullptr;
        bool mongoConnected = mongoHealthy && mongoManager_->isConnected();

        if (!mongoConnected) {
            Logger::getInstance().log(Logger::LogLevel::INFO, "MongoDB not connected - using mock implementation");
            // Not critical, can continue with mock implementation
        }

        // We can be "healthy enough" even without all services
        // Only require API to exist, not necessarily connected
        // The application can run in a degraded mode without a WebSocket connection
        return stateHealthy && apiHealthy;
    } catch (const std::exception& e) {
        Logger::getInstance().log(Logger::LogLevel::ERROR, "Error checking health status: " + std::string(e.what()));
        return false;
    } catch (...) {
        Logger::getInstance().log(Logger::LogLevel::ERROR, "Unknown error checking health status");
        return false;
    }
}

void BinaryOptionsBot::printStatus() const {
    std::cout << "\n=== Binary Options Bot Status ===\n";
    std::cout << "Trading Active: " << (isRunning_ ? "Yes" : "No") << std::endl;
    std::cout << "Trading Mode: " << (currentMode_ == TradingMode::DEMO ? "Demo" :
                                    currentMode_ == TradingMode::REAL ? "Real" : "Tournament") << std::endl;
    std::cout << "Paused: " << (isPaused_ ? "Yes" : "No") << std::endl;
    std::cout << "Tournament Mode: " << (isTournamentMode_ ? "Yes" : "No") << std::endl;
    std::cout << "Risk Level: " << currentRiskLevel_ << std::endl;
    std::cout << "Total Trades: " << totalTrades_ << std::endl;
    std::cout << "Trade Multiplier: " << tradeMultiplier_ << std::endl;
    std::cout << "System Health: " << (isHealthy() ? "Healthy" : "Unhealthy") << std::endl;
}

void BinaryOptionsBot::printBalance() const {
    if (currentSession_.status != "active") {
        std::cout << "No active trading session" << std::endl;
        return;
    }

    std::cout << "\n=== Account Balance Information ===\n";
    std::cout << std::fixed << std::setprecision(2);
    std::cout << "Initial Balance: $" << currentSession_.initialBalance << std::endl;
    std::cout << "Current Balance: $" << currentSession_.currentBalance << std::endl;
    std::cout << "Total Profit/Loss: $" << (currentSession_.currentBalance - currentSession_.initialBalance) << std::endl;
    std::cout << "Win Rate: " << (currentSession_.metrics.winRate * 100) << "%" << std::endl;
}

void BinaryOptionsBot::printCurrentPatterns() const {
    if (!patternRecognizer_) {
        std::cout << "Pattern recognizer not initialized" << std::endl;
        return;
    }

    std::cout << "\n=== Current Market Patterns ===\n";

    // Check if we're using the AI::Patterns::PatternRecognizer
    auto aiPatternRecognizer = dynamic_cast<AI::Patterns::PatternRecognizer*>(patternRecognizer_.get());
    if (aiPatternRecognizer) {
        const auto& pattern = aiPatternRecognizer->getCurrentPattern();
        if (pattern.empty()) {
            std::cout << "No active patterns detected" << std::endl;
            return;
        }

        std::cout << "Pattern: " << pattern << std::endl;
        std::cout << "Strength: " << aiPatternRecognizer->getPatternStrength() << std::endl;
        std::cout << "Reliability: " << aiPatternRecognizer->getPatternReliability() << std::endl;
    }
    // Check if we're using the Trading::PatternRecognizer
    else {
        std::cout << "Using Trading::PatternRecognizer" << std::endl;
        std::cout << "Pattern details not available in this view" << std::endl;
    }

    std::cout << "------------------------" << std::endl;
}

double BinaryOptionsBot::getCurrentBalance() const {
    // Ensure components are initialized before accessing trading API
    const_cast<BinaryOptionsBot*>(this)->ensureComponentsInitialized();

    if (!tradingApi_) {
        Logger::getInstance().log(Logger::LogLevel::ERROR, "Trading API not initialized");
        return 0.0;
    }

    try {
        // Get the account balance based on the current trading mode
        if (currentMode_ == TradingMode::DEMO) {
            return tradingApi_->getDemoAccountBalance();
        } else {
            return tradingApi_->getAccountBalance();
        }
    } catch (const std::exception& e) {
        Logger::getInstance().log(Logger::LogLevel::ERROR, "Error getting account balance: " + std::string(e.what()));
        return 0.0;
    }
}

BinaryOptionsBot::~BinaryOptionsBot() {
    try {
        Logger::getInstance().log(Logger::LogLevel::INFO, "Destroying Binary Options Bot...");

        // Stop any active trading
        if (isRunning_) {
            try {
                stopTradingSession();
            } catch (const std::exception& e) {
                Logger::getInstance().log(Logger::LogLevel::ERROR, "Error stopping trading session: " + std::string(e.what()));
            }
        }

        // Clean up resources in reverse order of initialization
        // Use try-catch blocks to handle any exceptions during cleanup
        try {
            autoPilot_.reset();
            patternRecognizer_.reset();
            marketProcessor_.reset();
            aiModel_.reset();
        } catch (const std::exception& e) {
            Logger::getInstance().log(Logger::LogLevel::ERROR, "Error cleaning up AI components: " + std::string(e.what()));
        }

        try {
            dataManager_.reset();
            accountManager_.reset();
            mongoManager_.reset();
            redisManager_.reset();
            api_.reset();
        } catch (const std::exception& e) {
            Logger::getInstance().log(Logger::LogLevel::ERROR, "Error cleaning up data components: " + std::string(e.what()));
        }

        try {
            microstructure_.reset();
            tradingInterface_.reset();
            hardwareOptimizer_.reset();
            eventManager_.reset();
            marketAnalyzer_.reset();
        } catch (const std::exception& e) {
            Logger::getInstance().log(Logger::LogLevel::ERROR, "Error cleaning up trading components: " + std::string(e.what()));
        }

        try {
            // These components might be causing the crash, so handle them separately
            if (recoveryManager_) recoveryManager_.reset();
            if (riskManager_) riskManager_.reset();
            if (strategyManager_) strategyManager_.reset();

            // Don't reset the tradeExecutor_ shared_ptr since it's a singleton
            // Just set it to nullptr to release our reference
            tradeExecutor_ = nullptr;

            if (tradingBrain_) tradingBrain_.reset();
        } catch (const std::exception& e) {
            Logger::getInstance().log(Logger::LogLevel::ERROR, "Error cleaning up strategy components: " + std::string(e.what()));
        }

        Logger::getInstance().log(Logger::LogLevel::INFO, "Binary Options Bot destroyed successfully");
    } catch (const std::exception& e) {
        Logger::getInstance().log(Logger::LogLevel::ERROR, "Error in BinaryOptionsBot destructor: " + std::string(e.what()));
    } catch (...) {
        Logger::getInstance().log(Logger::LogLevel::ERROR, "Unknown error in BinaryOptionsBot destructor");
    }
}

Models::MarketData BinaryOptionsBot::getCurrentMarketData() const {
    if (!tradingApi_) {
        Logger::getInstance().log(Logger::LogLevel::ERROR, "Trading API not initialized");
        return Models::MarketData();
    }

    try {
        // Get the market data for the default symbol (EURUSD)
        return tradingApi_->getMarketData("EURUSD");
    } catch (const std::exception& e) {
        Logger::getInstance().log(Logger::LogLevel::ERROR, "Error getting market data: " + std::string(e.what()));
        return Models::MarketData();
    }
}

std::vector<MarketData> BinaryOptionsBot::getHistoricalData() const {
    if (!dataManager_) {
        Logger::getInstance().log(Logger::LogLevel::ERROR, "Data manager not initialized");
        return std::vector<MarketData>();
    }
    return dataManager_->getHistoricalData();
}

bool BinaryOptionsBot::isPhaseCompleted(int phase) const {
    if (phaseRepetitions_.find(phase) == phaseRepetitions_.end()) {
        return false;
    }
    return currentSession_.metrics.totalTrades >= phaseRepetitions_.at(phase);
}

void BinaryOptionsBot::setPhaseRepetitions(int phaseNum, int repetitions) {
    phaseRepetitions_[phaseNum] = repetitions;
}

void BinaryOptionsBot::updatePhaseProgress(double profitAmount) {
    if (profitAmount > 0) {
        tradeMultiplier_ *= 1.1; // Increase multiplier on success
    } else {
        tradeMultiplier_ *= 0.9; // Decrease multiplier on failure
    }

    // Ensure multiplier stays within reasonable bounds
    tradeMultiplier_ = std::max(0.5, std::min(tradeMultiplier_, 2.0));
}

bool BinaryOptionsBot::validateWinRateThreshold(double winRate) const {
    return winRate >= config_.winRate; // Using winRate from TradingConfig
}

void BinaryOptionsBot::printPhaseStatus() const {
    Logger::info("Current Phase: " + std::to_string(currentPhase));

    if (phaseRepetitions_.find(currentPhase) != phaseRepetitions_.end()) {
        Logger::info("Trades Required: " + std::to_string(phaseRepetitions_.at(currentPhase)));
    }
}

void BinaryOptionsBot::printAccumulatedReserves() const {
    Logger::info("Initial Balance: " + std::to_string(currentSession_.initialBalance));
    Logger::info("Current Balance: " + std::to_string(currentSession_.currentBalance));
    Logger::info("Total Profit: " + std::to_string(currentSession_.metrics.totalProfit));
    Logger::info("Accumulated Reserves: " + std::to_string(accumulatedReserves_));
}

double BinaryOptionsBot::getTradeMultiplier() const {
    return tradeMultiplier_;
}

std::vector<Models::NewsEvent> BinaryOptionsBot::getRecentNews() const {
    try {
        // Return the stored recent news events
        return recentNews_;
    } catch (const std::exception& e) {
        Logger::getInstance().log(Logger::LogLevel::ERROR, "Failed to get recent news: " + std::string(e.what()));
        return std::vector<Models::NewsEvent>();
    }
}

void BinaryOptionsBot::updateRecentNews(const Models::NewsEvent& event) {
    try {
        // Add new event to the beginning of the list
        recentNews_.insert(recentNews_.begin(), event);

        // Keep only the most recent 50 news items to prevent memory bloat
        if (recentNews_.size() > 50) {
            recentNews_.resize(50);
        }

        // Log high-impact news
        if (event.impact == Models::NewsImpact::HIGH) {
            Logger::getInstance().log(Logger::LogLevel::INFO,
                "High-impact news received: " + event.title);
        }

        // Notify AI system about news update if needed
        if (tradingBrain_) {
            // The AI system will get the updated news through getRecentNews()
            Logger::getInstance().log(Logger::LogLevel::DEBUG,
                "News updated for AI system: " + event.title);
        }

    } catch (const std::exception& e) {
        Logger::getInstance().log(Logger::LogLevel::ERROR,
            "Failed to update recent news: " + std::string(e.what()));
    }
}

// Add these new Auto Pilot methods after the constructor
void BinaryOptionsBot::startAutoPilot() {
    if (!autoPilot_) {
        auto riskController = std::make_shared<Trading::AutomatedRiskController>();

        // Convert unique_ptr to shared_ptr with custom deleter that does nothing
        // This prevents double deletion since aiModel_ is still owned by the unique_ptr
        std::shared_ptr<AI::ML::AdvancedAIModel> sharedAiModel =
            std::shared_ptr<AI::ML::AdvancedAIModel>(aiModel_.get(), [](AI::ML::AdvancedAIModel*){});

        autoPilot_ = std::make_unique<AutoPilotManager>(
            shared_from_this(),  // Now this will work because of enable_shared_from_this
            riskController,
            sharedAiModel
        );
    }
    autoPilot_->start();
}

void BinaryOptionsBot::stopAutoPilot() {
    if (autoPilot_) {
        autoPilot_->stop();
    }
}

void BinaryOptionsBot::pauseAutoPilot() {
    if (autoPilot_) {
        autoPilot_->pause();
    }
}

void BinaryOptionsBot::resumeAutoPilot() {
    if (autoPilot_) {
        autoPilot_->resume();
    }
}

bool BinaryOptionsBot::isAutoPilotActive() const {
    return autoPilot_ && autoPilot_->isActive();
}

void BinaryOptionsBot::configureAutoPilot(const AutoPilotConfig& config) {
    if (autoPilot_) {
        autoPilot_->configure(config);
    }
}

std::shared_ptr<AI::Patterns::PatternRecognizer> BinaryOptionsBot::getPatternAnalyzer() const {
    // Return a shared_ptr to the pattern recognizer
    // Use a custom deleter that does nothing to avoid double deletion
    if (patternRecognizer_) {
        return std::shared_ptr<AI::Patterns::PatternRecognizer>(
            patternRecognizer_.get(),
            [](AI::Patterns::PatternRecognizer*) {}
        );
    }
    return nullptr;
}

std::shared_ptr<MarketAnalyzer> BinaryOptionsBot::getMarketAnalyzer() const {
    // Return a shared_ptr to the market analyzer
    // Use a custom deleter that does nothing to avoid double deletion
    if (marketAnalyzer_) {
        return std::shared_ptr<MarketAnalyzer>(
            marketAnalyzer_.get(),
            [](MarketAnalyzer*) {}
        );
    }
    return nullptr;
}
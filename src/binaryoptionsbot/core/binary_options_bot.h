#pragma once

#include <memory>
#include <vector>
#include <map>
#include <atomic>
#include <thread>
#include "../../models/qobject_compat.h"
#include "../../models/trading_config.h"
#include "../../ai/trading_brain.h"
/*
#include "../../ai/trading_brain_types.h"
#include "../../ai/trading_brain_methods/trade_decision.h"
#include "../../ai/trading_brain_methods/market_analysis.h"
#include "../../ai/trading_brain_methods/model_management.h"
#include "../../ai/trading_brain_methods/risk_management.h"
*/
// Forward declarations for components
namespace BinaryOptionComponents {
    class HardwareOptimizer;
    class TradingInterface;
    class MarketMicrostructure;
}

namespace AppleSilicon {
    class TradingEngine : public QObject {
        Q_OBJECT
    public:
        explicit TradingEngine(const Models::TradingConfig& config) : QObject(nullptr) {}
        virtual ~TradingEngine() = default;
        virtual void startTradingSession(double initialAmount, int numTrades, int timeframeSeconds) = 0;
        virtual void stopTradingSession() = 0;
        virtual void pauseTradingSession() = 0;
        virtual void resumeTradingSession() = 0;
        virtual double getCurrentBalance() const = 0;
        virtual bool isPhaseCompleted(int phase) const = 0;
        virtual void setPhaseRepetitions(int phaseNum, int repetitions) = 0;
        virtual void updatePhaseProgress(double profitAmount) = 0;
        virtual bool validateWinRateThreshold(double winRate) const = 0;
        virtual void printPhaseStatus() const = 0;
        virtual void printAccumulatedReserves() const = 0;
        virtual double getTradeMultiplier() const = 0;
        virtual void updateConfig(const Models::TradingConfig& config);
    protected:
        virtual void initializeComponents() = 0;
    };
}

#include "trading_engine.h"

#include "../events/event_manager.h"
#include "../recovery/recovery_manager.h"
#include "../../recovery/fibonacci_recovery.h"
#include "../analysis/market_analyzer.h"
#include "../../database/redis_manager.h"
#include "../../database/mongodb_manager.h"
#include "../../api/trading_api.h"
#include "../../trading/trading_data.h"
#include "../../models/market_regime.h"
#include "../../models/market_state.h"
#include "../../models/market_data.h"
#include "../../models/trade_result.h"
#include "../../models/tournament_config.h"
#include "../../models/trade_parameters.h"
#include "../../models/order_flow_data.h"
#include "../../models/market_depth_data.h"
#include "../../models/volume_profile_data.h"
#include "../../models/trading_session.h"
#include "../../models/ai_prediction.h"
#include "../../models/position.h"
#include "../../utils/logger.h"
#include "../../terminal/utils/tts_manager.h"
#include "../../ai/ml/advanced_ai_model.h"
#include "../../ai/realtime/market_processor.h"
#include "../../ai/patterns/pattern_recognizer.h"
#include "../../validation/multi_timeframe_validation.h"
#include "../../hardware/apple_silicon/m_series_optimizer.h"
#include "../../hardware/hardware_optimization_manager.h"
#include "trade_executor.h"
#include "trade_executor_singleton.h" // Added BinaryOptionsTradeExecutorSingleton include
#include "../core/trading_engine.h"
#include "../strategies/strategy_manager.h"
#include "../risk/risk_manager.h"
#include "../recovery/recovery_manager.h"
#include "../../recovery/fibonacci_recovery.h"
#include "../analysis/market_analyzer.h"
#include "../trading/market_microstructure.h"
#include "../events/event_manager.h"
#include "../hardware/hardware_optimizer.h"
#include "../trading/trading_interface.h"
#include "../../trading/risk_metrics.h"

#include "../account/account_manager.h"  // Add this include
#include "../data/data_manager.h"        // Add this include
#include "../trading/concrete_trading_interface.h"
#include "../../services/news_service.h"

#include "../../autopilot/auto_pilot_config.h"
#include "../../autopilot/auto_pilot_manager.h"

// Use namespaces
using namespace Models;
using AI::ML::AdvancedAIModel;
using AppleSilicon::MSeriesOptimizer;
using Trading::Risk::RiskManager;
using Trading::RiskMetrics;

enum class TradingMode {
    DEMO,
    REAL,
    TOURNAMENT
};

#include "../../ai/ml/ensemble_strategy.h"

class BinaryOptionsBot : public TradingEngine, public std::enable_shared_from_this<BinaryOptionsBot>{
    Q_OBJECT
    friend class StrategyManager;
public:
    explicit BinaryOptionsBot(const Models::TradingConfig& config, std::shared_ptr<TradingAPI> tradingApi = nullptr);
    ~BinaryOptionsBot() override;

    // Auto Pilot control
    void startAutoPilot();
    void stopAutoPilot();
    void pauseAutoPilot();
    void resumeAutoPilot();
    bool isAutoPilotActive() const;
    void configureAutoPilot(const AutoPilotConfig& config);
private:
    std::vector<Models::NewsEvent> recentNews_;

     // Add Auto Pilot manager to private members
    std::unique_ptr<AutoPilotManager> autoPilot_;

public:
    // Market data methods
    Models::MarketData getMarketData() const;
    Models::MarketData getCurrentMarketData() const;

    // Trading methods
    Models::TradeResult executeTrade(double amount, Models::TradeDirection direction, int expirySeconds);

    // Risk assessment methods
    Trading::RiskMetrics::RiskAnalysis assessRisk(const Models::MarketData& data) const;

    friend class StrategyManager;

signals:
    // Existing signals
    void phaseChanged(int newPhase);
    void sessionStatusChanged(const QString& status);
    void riskLevelChanged(double level);
    void tradeExecuted(const Models::TradeResult& result);
    void tournamentRankingUpdated(const TournamentMetrics& metrics);
    void tradingStarted(const QString& mode);
    void tradingStopped(const QString& reason);
    void tradePaused();
    void tradeResumed();
    void phaseCompleted(int phase, double winRate);
    void tournamentUpdated(const QString& ranking);
    void terminalUpdateReady(const QString& data);
    void marketAnalysisReady(const Models::MarketData& data);
    void performanceMetricsUpdated(const QString& metrics);
    void aiPredictionReady(const QString& prediction);
    void microstructureUpdated(const QString& analysis);

    // Add missing signals that the UI is trying to connect to
    void newMarketData(double price, QString symbol);
    void patternDetected(QString pattern, double confidence);
    void marketRegimeChanged(QString regime);

public:
    using MarketDataCallback = std::function<void(const Models::MarketData&)>;

    // Trading mode methods
    void setTradingMode(TradingMode mode);
    TradingMode getTradingMode() const;
    double getTradeMultiplier() const override;

    // Mode-specific start methods
    void startDemoTrading(double initialAmount);
    void startRealTrading(double initialAmount);
    void startTournamentTrading(double initialAmount);

    // Status and monitoring methods
    void startTradingSession(double initialAmount, int numTrades, int timeframeSeconds) override;
    bool isHealthy() const;
    bool isConnected() const; // Check if the bot is connected to the trading API
    void printStatus() const;
    void printBalance() const;
    void printCurrentPatterns() const;
    void printTournamentRanking() const;

    // Configuration management
    void updateConfig(const Models::TradingConfig& config) override;

    // Core trading functions
    void stopTradingSession() override;
    void pauseTradingSession() override;
    void resumeTradingSession() override;

    double getCurrentBalance() const override;
    std::vector<Models::MarketData> getHistoricalData() const;

    // Get recent news data for AI predictions
    std::vector<Models::NewsEvent> getRecentNews() const;

    // Update recent news data
    void updateRecentNews(const Models::NewsEvent& event);

    // Get AI components
    std::shared_ptr<AI::Patterns::PatternRecognizer> getPatternAnalyzer() const;
    std::shared_ptr<MarketAnalyzer> getMarketAnalyzer() const;

private:
    TradingMode currentMode_;

    // Phase system functions
    bool isPhaseCompleted(int phase) const override;
    void setPhaseRepetitions(int phaseNum, int repetitions) override;
    void updatePhaseProgress(double profitAmount) override;
    bool validateWinRateThreshold(double winRate) const override;
    void printPhaseStatus() const override;
    void printAccumulatedReserves() const override;

    // Tournament mode functions
    void enableTournamentMode(bool enable);
    double getTournamentScore() const;
    void updateTournamentMetrics(const Models::TradeResult& result);

    // Terminal integration
    void connectToTerminal();
    void updateTerminalDisplay(const QString& message);
    void sendTradeNotification(const Models::TradeResult& result);
    void displayMarketAnalysis(const Models::MarketData& data);

    // Advanced analysis
    void enhanceMicrostructureAnalysis(const Models::MarketData& data);
    void implementDynamicRiskStrategy();
    void adjustPositionSizeBasedOnConfidence();
    bool validateAISignal(const Models::MarketData& data);
    double calculateEnhancedPositionSize(const Models::MarketData& data);

    // Advanced Pattern Validation Methods
    bool validateVolumeProfile(const Models::PatternAnalysis& pattern);
    MultiTimeframeValidation validateMultiTimeframe(const Models::PatternAnalysis& pattern);
    double validatePriceAction(const Models::PatternAnalysis& pattern);
    double validateMomentumAlignment(const Models::PatternAnalysis& pattern);
    double validateVolatilityContext(const Models::PatternAnalysis& pattern);

    // Tournament Mode Components
    void initializeTournamentMode(const Models::TournamentConfig& config);
    void updateTournamentRanking();
    void printTournamentStatus() const;
    bool validateTournamentRules(const Models::TradeParameters& params) const;
    void handleTournamentRewards();

    // Advanced Market Microstructure Analysis
    void analyzeMicrostructure(const Models::MarketData& data);
    void processOrderFlow(const Models::OrderFlowData& data);
    void analyzeMarketDepth(const Models::MarketDepthData& data);
    void processVolumeProfile(const Models::VolumeProfileData& data);

    // Execute single trade
    Models::TradeResult executeSingleTrade(double amount);

// Add TradingBrain member to private section
private:
    // Core components
    std::unique_ptr<TradingBrain> tradingBrain_;
    std::shared_ptr<BinaryOptionsTradeExecutorSingleton> tradeExecutor_; // Changed to shared_ptr and BinaryOptionsTradeExecutorSingleton
    std::unique_ptr<StrategyManager> strategyManager_;
    std::unique_ptr<RiskManager> riskManager_;
    std::unique_ptr<RecoveryManager> recoveryManager_;
    std::unique_ptr<MarketAnalyzer> marketAnalyzer_;
    std::unique_ptr<EventManager> eventManager_;
    std::unique_ptr<BinaryOptionComponents::HardwareOptimizer> hardwareOptimizer_;
    std::unique_ptr<BinaryOptionComponents::TradingInterface> tradingInterface_;
    std::unique_ptr<BinaryOptionComponents::MarketMicrostructure> microstructure_;

    // Callback
    MarketDataCallback marketDataCallback_;

    // External services
    std::shared_ptr<TradingAPI> api_; // This is the same as tradingApi_
    std::shared_ptr<TradingAPI> tradingApi_;
    std::shared_ptr<RedisManager> redisManager_;
    std::shared_ptr<MongoDBManager> mongoManager_;
    std::unique_ptr<BotAccountManager> accountManager_;
    std::unique_ptr<DataManager> dataManager_;
    std::unique_ptr<NewsService> newsService_;

    // AI/ML components
    std::unique_ptr<AI::ML::AdvancedAIModel> aiModel_;
    std::unique_ptr<AI::Realtime::IMarketProcessor> marketProcessor_;
    std::unique_ptr<AI::Patterns::PatternRecognizer> patternRecognizer_;
    std::unique_ptr<MultiTimeframeValidation> multiTimeframeValidator_;

    // Hardware optimization
    std::unique_ptr<AppleSilicon::MSeriesOptimizer> mSeriesOptimizer_;
    std::unique_ptr<Hardware::HardwareOptimizationManager> hardwareManager_;

    // Trading state
    std::atomic<bool> isRunning_;
    std::atomic<bool> isPaused_;
    std::atomic<bool> isTournamentMode_;
    Trading::TradingSession currentSession_;
    std::map<int, int> phaseRepetitions_;
    double currentRiskLevel_;
    int totalTrades_;
    double tradeMultiplier_{1.0};
    TournamentConfig tournamentConfig_;
    TournamentMetrics tournamentMetrics_;
    std::vector<TournamentRanking> tournamentRanking_;

    // Initialization state
    bool componentsInitialized_{false};

    // Configuration
    Models::TradingConfig config_;

    // Market state members
    MarketState marketState_;

public:
    // Performance and position methods
    Models::PerformanceMetrics getPerformanceMetrics() const;
    std::vector<Models::Position> getOpenPositions() const;

    // API access
    std::shared_ptr<TradingAPI> getTradingApi() const { return api_; }

    // Database access
    bool isRedisConnected() const;
    bool isMongoDBConnected() const;

private:
    // Helper functions
    void initializeComponents() override;
    void ensureComponentsInitialized();
    void setupTerminalConnections();
    void validateTradingConditions();
    void updatePerformanceMetrics();
    void processAIPredictions();
    void optimizeHardwareUsage();
    void handleRiskUpdate(double riskLevel);
    void updateTradeStatistics(bool isWin, double amount, double profit);
    void handleDrawdownRecovery();
    double getAIConfidenceScore(const Models::PatternAnalysis& pattern);

    // AI methods
    void initializeAIComponents();
    void updateAIModels();
    void handleAIPrediction(const AIPrediction& prediction);

    // Tournament ranking methods
    double getTournamentRank() const;
    void updateRanking(const TournamentMetrics& metrics);

    void updateAIModels(const Models::MarketData& data);
    double calculateConfidenceScore(const Models::AIPrediction& prediction);
};

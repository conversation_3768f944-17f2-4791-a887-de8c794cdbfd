#pragma once
#include "../models/news_data.h"
#include "../api/news/news_api_client.h"
#include <memory>
#include <functional>

// Use Models namespace types
using Models::NewsEvent;
using Models::NewsAnalysis;
using Models::NewsImpact;

class NewsService {
public:
    NewsService();

    void initialize(const std::string& apiKey);
    void startNewsMonitoring();
    void stopNewsMonitoring();

    void subscribeToNews(const std::vector<std::string>& symbols);
    void setNewsCallback(std::function<void(const NewsEvent&)> callback);

    NewsAnalysis analyzeNewsImpact(const NewsEvent& event);
    std::vector<NewsEvent> getPendingNews(const std::string& symbol);
    bool shouldAvoidTrading(const std::string& symbol);

private:
    double analyzeSentiment(const std::string& text);
    double estimateVolatilityImpact(const NewsEvent& event);
    std::vector<std::string> extractKeywords(const std::string& text);

    void fetchNews();
    void processNewsEvent(const NewsEvent& event);
    void updateTradingRestrictions();
    double calculateNewsImpact(const NewsEvent& event);

    std::unique_ptr<NewsAPIClient> apiClient_;
    std::vector<NewsEvent> pendingNews_;
    std::function<void(const NewsEvent&)> newsCallback_;
    bool isMonitoring_;
};
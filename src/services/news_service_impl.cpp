#include "news_service.h"
#include "../utils/logger.h"
#include <thread>
#include <chrono>
#include <algorithm>
#include <random>
#include <fstream>
#include <filesystem>
#include <nlohmann/json.hpp>

// Use Models namespace types
using Models::NewsEvent;
using Models::NewsAnalysis;
using Models::NewsImpact;

// Helper function to load API keys from config file
struct NewsAPIKeys {
    std::string marketaux;
    std::string finnhub;
    std::string newsdata;
};

NewsAPIKeys loadNewsAPIKeys() {
    NewsAPIKeys keys;

    try {
        // Try to load from config file
        std::string configPath = "config/news_api_config.json";

        // Check if file exists
        if (!std::filesystem::exists(configPath)) {
            Logger::getInstance().error("News API config file not found at: " + configPath);
            throw std::runtime_error("News API configuration file not found");
        }

        // Load and parse config file
        std::ifstream configFile(configPath);
        if (!configFile.is_open()) {
            throw std::runtime_error("Could not open config file: " + configPath);
        }

        nlohmann::json config;
        configFile >> config;

        // Extract API keys
        if (config.contains("news_apis")) {
            auto apis = config["news_apis"];

            if (apis.contains("marketaux") && apis["marketaux"].contains("api_key")) {
                keys.marketaux = apis["marketaux"]["api_key"];
            }

            if (apis.contains("finnhub") && apis["finnhub"].contains("api_key")) {
                keys.finnhub = apis["finnhub"]["api_key"];
            }

            if (apis.contains("newsdata") && apis["newsdata"].contains("api_key")) {
                keys.newsdata = apis["newsdata"]["api_key"];
            }
        }

        Logger::getInstance().info("Successfully loaded API keys from config file");

    } catch (const std::exception& e) {
        Logger::getInstance().error("Error loading API keys from config: " + std::string(e.what()));
        throw; // Re-throw the exception
    }

    return keys;
}

NewsService::NewsService() : isMonitoring_(false) {
    // Initialize with default constructor - will be properly initialized in initialize() method
    apiClient_ = std::make_unique<NewsAPIClient>();
}

void NewsService::initialize(const std::string& apiKey) {
    try {
        Logger::info("Initializing NewsService with multi-API support");

        // Create the new multi-API client
        apiClient_ = std::make_unique<NewsAPIClient>();
        apiClient_->setTimeout(30); // 30 seconds timeout for multiple APIs

        // Load API keys from config file
        NewsAPIKeys keys = loadNewsAPIKeys();

        std::string marketauxKey = keys.marketaux;
        std::string finnhubKey = keys.finnhub;
        std::string newsdataKey = keys.newsdata;

        // If a specific API key is provided, try to determine which one it is
        if (!apiKey.empty() && apiKey != "default") {
            if (apiKey.length() > 30) {
                marketauxKey = apiKey; // Marketaux keys are longer
            } else if (apiKey.length() > 20) {
                finnhubKey = apiKey; // Finnhub keys are medium length
            } else {
                newsdataKey = apiKey; // NewsData keys are shorter
            }
        }

        apiClient_->initialize(marketauxKey, finnhubKey, newsdataKey);

        Logger::info("NewsService initialized successfully with multi-API support");
        Logger::info("- Marketaux: " + std::string(marketauxKey.empty() ? "Not configured" : "Configured"));
        Logger::info("- Finnhub: " + std::string(finnhubKey.empty() ? "Not configured" : "Configured"));
        Logger::info("- NewsData: " + std::string(newsdataKey.empty() ? "Not configured" : "Configured"));

    } catch (const std::exception& e) {
        Logger::error("Failed to initialize NewsService: " + std::string(e.what()));
        throw;
    }
}

void NewsService::stopNewsMonitoring() {
    Logger::info("Stopping news monitoring");
    isMonitoring_ = false;
}

void NewsService::subscribeToNews(const std::vector<std::string>& symbols) {
    try {
        Logger::info("Subscribing to news for symbols");

        // Log the symbols we're subscribing to
        std::string symbolList;
        for (const auto& symbol : symbols) {
            if (!symbolList.empty()) symbolList += ", ";
            symbolList += symbol;
        }

        Logger::info("Subscribed to news for: " + symbolList);
    } catch (const std::exception& e) {
        Logger::error("Failed to subscribe to news: " + std::string(e.what()));
    }
}

void NewsService::setNewsCallback(std::function<void(const NewsEvent&)> callback) {
    try {
        Logger::info("Setting news callback");
        newsCallback_ = callback;
    } catch (const std::exception& e) {
        Logger::error("Failed to set news callback: " + std::string(e.what()));
    }
}

void NewsService::fetchNews() {
    try {
        Logger::info("Fetching latest news");

        // Fetch news from API
        auto newsEvents = apiClient_->fetchLatestNews();

        // Process each news event
        for (const auto& event : newsEvents) {
            processNewsEvent(event);
        }

        // Also fetch economic calendar
        auto economicEvents = apiClient_->fetchEconomicCalendar();

        // Process each economic event
        for (const auto& event : economicEvents) {
            processNewsEvent(event);
        }

        // Update trading restrictions based on news
        updateTradingRestrictions();

        Logger::info("News fetched and processed successfully");
    } catch (const std::exception& e) {
        Logger::error("Failed to fetch news: " + std::string(e.what()));
    }
}

void NewsService::processNewsEvent(const NewsEvent& event) {
    try {
        Logger::info("Processing news event: " + event.title);

        // Add to pending news
        pendingNews_.push_back(event);

        // Call the callback if set
        if (newsCallback_) {
            newsCallback_(event);
        }

        // Analyze the news impact
        auto analysis = analyzeNewsImpact(event);

        // Log the analysis
        Logger::info("News analysis: Sentiment=" + std::to_string(analysis.sentimentScore) +
                     ", Impact=" + std::to_string(analysis.marketImpact) +
                     ", Volatility=" + std::to_string(analysis.volatilityExpectation));
    } catch (const std::exception& e) {
        Logger::error("Failed to process news event: " + std::string(e.what()));
    }
}

void NewsService::updateTradingRestrictions() {
    try {
        Logger::info("Updating trading restrictions based on news");

        // Remove old news events
        auto now = std::chrono::system_clock::now();
        auto it = pendingNews_.begin();

        while (it != pendingNews_.end()) {
            auto eventTime = std::chrono::system_clock::from_time_t(it->releaseTime.toSecsSinceEpoch());
            auto timeDiff = std::chrono::duration_cast<std::chrono::hours>(now - eventTime).count();

            // Remove news older than 24 hours
            if (timeDiff > 24) {
                it = pendingNews_.erase(it);
            } else {
                ++it;
            }
        }

        Logger::info("Trading restrictions updated");
    } catch (const std::exception& e) {
        Logger::error("Failed to update trading restrictions: " + std::string(e.what()));
    }
}

double NewsService::calculateNewsImpact(const NewsEvent& event) {
    try {
        Logger::info("Calculating impact for news: " + event.title);

        double impact = 0.0;

        // Base impact from news type
        switch (event.impact) {
            case NewsImpact::HIGH:
                impact = 0.8;
                break;
            case NewsImpact::MEDIUM:
                impact = 0.5;
                break;
            case NewsImpact::LOW:
                impact = 0.2;
                break;
            default:
                impact = 0.1;
        }

        // Adjust based on sentiment
        double sentiment = analyzeSentiment(event.description);
        impact *= (1.0 + std::abs(sentiment) * 0.5);

        // Adjust based on affected symbols
        impact *= (0.5 + 0.1 * event.relatedSymbols.size());

        // Cap at 1.0
        impact = std::min(impact, 1.0);

        Logger::info("Calculated news impact: " + std::to_string(impact));
        return impact;
    } catch (const std::exception& e) {
        Logger::error("Failed to calculate news impact: " + std::string(e.what()));
        return 0.0;
    }
}

std::vector<NewsEvent> NewsService::getPendingNews(const std::string& symbol) {
    try {
        Logger::info("Getting pending news for symbol: " + symbol);

        std::vector<NewsEvent> relevantNews;

        // Filter news relevant to the symbol
        for (const auto& news : pendingNews_) {
            // Check if this is the primary symbol for the news
            if (news.symbol == symbol) {
                relevantNews.push_back(news);
                continue;
            }

            // Check related symbols
            auto it = std::find(news.relatedSymbols.begin(), news.relatedSymbols.end(), symbol);
            if (it != news.relatedSymbols.end()) {
                relevantNews.push_back(news);
            }
        }

        Logger::info("Found " + std::to_string(relevantNews.size()) + " pending news items for " + symbol);
        return relevantNews;
    } catch (const std::exception& e) {
        Logger::error("Failed to get pending news: " + std::string(e.what()));
        return {};
    }
}

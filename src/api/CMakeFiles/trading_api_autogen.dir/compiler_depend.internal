# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

/Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/trading_api_autogen/timestamp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/CMakeLists.txt
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/account_manager.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/account_manager.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/market_data_handler.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/market_data_handler.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/order_manager.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/order_manager.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/pyquotex_rest_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/pyquotex_rest_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/quotex_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/quotex_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/trading_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/trading_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/websocket_channel.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/websocket_channel.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/ws_channel.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/ws_channel.h
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeature.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeatureCommon.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAndroidHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAppleHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicGitHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers_v2.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomQtEntityHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTargetHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTestHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicToolHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWindowsHelpers.cmake
 /opt/homebrew/bin/cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Config.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigExtras.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersion.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Dependencies.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Targets.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtInstallPaths.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake
 /opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake
 /opt/homebrew/share/cmake/Modules/CheckCXXCompilerFlag.cmake
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/CMakeLists.txt
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/account_manager.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/account_manager.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/market_data_handler.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/market_data_handler.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/order_manager.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/order_manager.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/pyquotex_rest_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/pyquotex_rest_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/quotex_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/quotex_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/trading_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/trading_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/websocket_channel.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/websocket_channel.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/ws_channel.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/ws_channel.h
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeature.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeatureCommon.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAndroidHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAppleHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicGitHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers_v2.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomQtEntityHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTargetHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTestHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicToolHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWindowsHelpers.cmake
 /opt/homebrew/bin/cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Config.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigExtras.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersion.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Dependencies.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Targets.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtInstallPaths.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake
 /opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake
 /opt/homebrew/share/cmake/Modules/CheckCXXCompilerFlag.cmake
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/CMakeLists.txt
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/account_manager.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/account_manager.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/market_data_handler.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/market_data_handler.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/order_manager.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/order_manager.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/pyquotex_rest_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/pyquotex_rest_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/quotex_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/quotex_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/trading_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/trading_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/websocket_channel.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/websocket_channel.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/ws_channel.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/ws_channel.h
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeature.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeatureCommon.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAndroidHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAppleHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicGitHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers_v2.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomQtEntityHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTargetHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTestHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicToolHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWindowsHelpers.cmake
 /opt/homebrew/bin/cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Config.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigExtras.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersion.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Dependencies.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Targets.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtInstallPaths.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake
 /opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake
 /opt/homebrew/share/cmake/Modules/CheckCXXCompilerFlag.cmake
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/CMakeLists.txt
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/account_manager.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/account_manager.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/market_data_handler.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/market_data_handler.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/order_manager.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/order_manager.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/pyquotex_rest_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/pyquotex_rest_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/quotex_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/quotex_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/trading_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/trading_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/websocket_channel.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/websocket_channel.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/ws_channel.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/ws_channel.h
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeature.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeatureCommon.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAndroidHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAppleHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicGitHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers_v2.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomQtEntityHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTargetHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTestHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicToolHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWindowsHelpers.cmake
 /opt/homebrew/bin/cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Config.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigExtras.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersion.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Dependencies.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Targets.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtInstallPaths.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake
 /opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake
 /opt/homebrew/share/cmake/Modules/CheckCXXCompilerFlag.cmake
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/CMakeLists.txt
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/account_manager.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/account_manager.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/market_data_handler.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/market_data_handler.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/order_manager.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/order_manager.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/pyquotex_rest_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/pyquotex_rest_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/quotex_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/quotex_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/trading_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/trading_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/websocket_channel.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/websocket_channel.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/ws_channel.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/ws_channel.h
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeature.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeatureCommon.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAndroidHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAppleHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicGitHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers_v2.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomQtEntityHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTargetHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTestHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicToolHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWindowsHelpers.cmake
 /opt/homebrew/bin/cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Config.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigExtras.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersion.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Dependencies.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Targets.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtInstallPaths.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake
 /opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake
 /opt/homebrew/share/cmake/Modules/CheckCXXCompilerFlag.cmake
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/CMakeLists.txt
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/account_manager.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/account_manager.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/market_data_handler.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/market_data_handler.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/order_manager.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/order_manager.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/pyquotex_rest_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/pyquotex_rest_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/quotex_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/quotex_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/trading_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/trading_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/websocket_channel.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/websocket_channel.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/ws_channel.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/ws_channel.h
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeature.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeatureCommon.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAndroidHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAppleHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicGitHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers_v2.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomQtEntityHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTargetHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTestHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicToolHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWindowsHelpers.cmake
 /opt/homebrew/bin/cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Config.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigExtras.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersion.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Dependencies.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Targets.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtInstallPaths.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake
 /opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake
 /opt/homebrew/share/cmake/Modules/CheckCXXCompilerFlag.cmake
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/CMakeLists.txt
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/account_manager.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/account_manager.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/market_data_handler.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/market_data_handler.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/order_manager.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/order_manager.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/pyquotex_rest_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/pyquotex_rest_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/quotex_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/quotex_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/trading_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/trading_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/websocket_channel.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/websocket_channel.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/ws_channel.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/ws_channel.h
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeature.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeatureCommon.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAndroidHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAppleHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicGitHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers_v2.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomQtEntityHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTargetHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTestHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicToolHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWindowsHelpers.cmake
 /opt/homebrew/bin/cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Config.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigExtras.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersion.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Dependencies.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Targets.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtInstallPaths.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake
 /opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake
 /opt/homebrew/share/cmake/Modules/CheckCXXCompilerFlag.cmake
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/CMakeLists.txt
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/account_manager.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/account_manager.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/market_data_handler.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/market_data_handler.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/order_manager.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/order_manager.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/pyquotex_rest_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/pyquotex_rest_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/quotex_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/quotex_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/trading_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/trading_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/websocket_channel.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/websocket_channel.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/ws_channel.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/ws_channel.h
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeature.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeatureCommon.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAndroidHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAppleHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicGitHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers_v2.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomQtEntityHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTargetHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTestHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicToolHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWindowsHelpers.cmake
 /opt/homebrew/bin/cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Config.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigExtras.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersion.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Dependencies.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Targets.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtInstallPaths.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake
 /opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake
 /opt/homebrew/share/cmake/Modules/CheckCXXCompilerFlag.cmake
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/CMakeLists.txt
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/account_manager.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/account_manager.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/market_data_handler.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/market_data_handler.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/order_manager.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/order_manager.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/pyquotex_rest_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/pyquotex_rest_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/quotex_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/quotex_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/trading_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/trading_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/websocket_channel.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/websocket_channel.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/ws_channel.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/ws_channel.h
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeature.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeatureCommon.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAndroidHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAppleHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicGitHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers_v2.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomQtEntityHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTargetHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTestHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicToolHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWindowsHelpers.cmake
 /opt/homebrew/bin/cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Config.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigExtras.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersion.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Dependencies.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Targets.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtInstallPaths.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake
 /opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake
 /opt/homebrew/share/cmake/Modules/CheckCXXCompilerFlag.cmake
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/CMakeLists.txt
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/account_manager.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/account_manager.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/market_data_handler.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/market_data_handler.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/order_manager.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/order_manager.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/pyquotex_rest_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/pyquotex_rest_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/quotex_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/quotex_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/trading_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/trading_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/websocket_channel.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/websocket_channel.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/ws_channel.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/ws_channel.h
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeature.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeatureCommon.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAndroidHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAppleHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicGitHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers_v2.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomQtEntityHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTargetHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTestHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicToolHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWindowsHelpers.cmake
 /opt/homebrew/bin/cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Config.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigExtras.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersion.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Dependencies.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Targets.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtInstallPaths.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake
 /opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake
 /opt/homebrew/share/cmake/Modules/CheckCXXCompilerFlag.cmake
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/CMakeLists.txt
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/account_manager.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/account_manager.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/market_data_handler.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/market_data_handler.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/order_manager.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/order_manager.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/pyquotex_rest_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/pyquotex_rest_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/quotex_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/quotex_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/trading_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/trading_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/websocket_channel.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/websocket_channel.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/ws_channel.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/ws_channel.h
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeature.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeatureCommon.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAndroidHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAppleHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicGitHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers_v2.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomQtEntityHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTargetHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTestHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicToolHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWindowsHelpers.cmake
 /opt/homebrew/bin/cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Config.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigExtras.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersion.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Dependencies.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Targets.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtInstallPaths.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake
 /opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake
 /opt/homebrew/share/cmake/Modules/CheckCXXCompilerFlag.cmake
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/CMakeLists.txt
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/account_manager.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/account_manager.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/market_data_handler.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/market_data_handler.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/order_manager.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/order_manager.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/pyquotex_rest_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/pyquotex_rest_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/quotex_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/quotex_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/trading_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/trading_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/websocket_channel.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/websocket_channel.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/ws_channel.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/ws_channel.h
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeature.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeatureCommon.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAndroidHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAppleHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicGitHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers_v2.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomQtEntityHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTargetHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTestHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicToolHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWindowsHelpers.cmake
 /opt/homebrew/bin/cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Config.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigExtras.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersion.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Dependencies.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Targets.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtInstallPaths.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake
 /opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake
 /opt/homebrew/share/cmake/Modules/CheckCXXCompilerFlag.cmake
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/CMakeLists.txt
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/account_manager.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/account_manager.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/market_data_handler.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/market_data_handler.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/order_manager.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/order_manager.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/pyquotex_rest_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/pyquotex_rest_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/quotex_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/quotex_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/trading_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/trading_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/websocket_channel.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/websocket_channel.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/ws_channel.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/ws_channel.h
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeature.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeatureCommon.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAndroidHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAppleHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicGitHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers_v2.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomQtEntityHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTargetHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTestHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicToolHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWindowsHelpers.cmake
 /opt/homebrew/bin/cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Config.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigExtras.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersion.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Dependencies.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Targets.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtInstallPaths.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake
 /opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake
 /opt/homebrew/share/cmake/Modules/CheckCXXCompilerFlag.cmake
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/CMakeLists.txt
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/account_manager.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/account_manager.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/market_data_handler.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/market_data_handler.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/order_manager.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/order_manager.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/pyquotex_rest_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/pyquotex_rest_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/quotex_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/quotex_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/trading_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/trading_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/websocket_channel.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/websocket_channel.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/ws_channel.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/ws_channel.h
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeature.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeatureCommon.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAndroidHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAppleHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicGitHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers_v2.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomQtEntityHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTargetHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTestHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicToolHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWindowsHelpers.cmake
 /opt/homebrew/bin/cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Config.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigExtras.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersion.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Dependencies.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Targets.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtInstallPaths.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake
 /opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake
 /opt/homebrew/share/cmake/Modules/CheckCXXCompilerFlag.cmake
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/CMakeLists.txt
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/account_manager.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/account_manager.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/market_data_handler.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/market_data_handler.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/order_manager.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/order_manager.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/pyquotex_rest_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/pyquotex_rest_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/quotex_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/quotex_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/trading_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/trading_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/websocket_channel.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/websocket_channel.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/ws_channel.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/ws_channel.h
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeature.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeatureCommon.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAndroidHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAppleHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicGitHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers_v2.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomQtEntityHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTargetHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTestHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicToolHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWindowsHelpers.cmake
 /opt/homebrew/bin/cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Config.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigExtras.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersion.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Dependencies.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Targets.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtInstallPaths.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake
 /opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake
 /opt/homebrew/share/cmake/Modules/CheckCXXCompilerFlag.cmake
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/CMakeLists.txt
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/account_manager.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/account_manager.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/market_data_handler.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/market_data_handler.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/order_manager.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/order_manager.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/pyquotex_rest_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/pyquotex_rest_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/quotex_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/quotex_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/trading_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/trading_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/websocket_channel.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/websocket_channel.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/ws_channel.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/ws_channel.h
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeature.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeatureCommon.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAndroidHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAppleHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicGitHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers_v2.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomQtEntityHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTargetHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTestHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicToolHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWindowsHelpers.cmake
 /opt/homebrew/bin/cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Config.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigExtras.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersion.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Dependencies.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Targets.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtInstallPaths.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake
 /opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake
 /opt/homebrew/share/cmake/Modules/CheckCXXCompilerFlag.cmake
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/CMakeLists.txt
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/account_manager.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/account_manager.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/market_data_handler.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/market_data_handler.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/order_manager.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/order_manager.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/pyquotex_rest_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/pyquotex_rest_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/quotex_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/quotex_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/trading_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/trading_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/websocket_channel.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/websocket_channel.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/ws_channel.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/ws_channel.h
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeature.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeatureCommon.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAndroidHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAppleHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicGitHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers_v2.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomQtEntityHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTargetHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTestHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicToolHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWindowsHelpers.cmake
 /opt/homebrew/bin/cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Config.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigExtras.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersion.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Dependencies.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Targets.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtInstallPaths.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake
 /opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake
 /opt/homebrew/share/cmake/Modules/CheckCXXCompilerFlag.cmake
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/CMakeLists.txt
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/account_manager.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/account_manager.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/market_data_handler.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/market_data_handler.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/order_manager.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/order_manager.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/pyquotex_rest_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/pyquotex_rest_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/quotex_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/quotex_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/trading_api.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/trading_api.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/websocket_channel.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/websocket_channel.h
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/ws_channel.cpp
 /Users/<USER>/DevProjects/RicaXbulan-Binaray-Options-trading-Bot.1-2.0/src/api/ws_channel.h
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeature.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeatureCommon.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAndroidHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAppleHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicGitHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers_v2.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomQtEntityHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTargetHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTestHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicToolHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake
 /opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWindowsHelpers.cmake
 /opt/homebrew/bin/cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Config.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigExtras.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersion.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Dependencies.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6Targets.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtInstallPaths.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake
 /opt/homebrew/opt/qt@6/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake
 /opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake
 /opt/homebrew/share/cmake/Modules/CheckCXXCompilerFlag.cmake


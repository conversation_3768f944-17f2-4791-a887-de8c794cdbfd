#include "news_api_client.h"
#include "../../utils/logger.h"
#include "../../models/qdatetime_compat.h"
#include <curl/curl.h>
#include <nlohmann/json.hpp>
#include <sstream>
#include <chrono>
#include <thread>
#include <algorithm>
#include <regex>
#include <iomanip>

// Callback function for CURL to write response data
static size_t WriteCallback(void* contents, size_t size, size_t nmemb, std::string* userp) {
    userp->append((char*)contents, size * nmemb);
    return size * nmemb;
}

NewsAPIClient::NewsAPIClient()
    : timeout_(30)
    , lastFetchTime_(0)
{
    // Initialize CURL
    curl_global_init(CURL_GLOBAL_DEFAULT);

    // Set default headers
    headers_.push_back("Content-Type: application/json");
    headers_.push_back("User-Agent: RicaXBulan-BinaryOptionsBot/2.0");
    headers_.push_back("Accept: application/json");

    // Initialize Redis connection
    redisManager_ = RedisManager::getInstance();

    Logger::getInstance().info("Multi-API NewsAPIClient initialized");
}

NewsAPIClient::~NewsAPIClient() {
    // Clean up CURL
    curl_global_cleanup();
    Logger::getInstance().info("NewsAPIClient destroyed");
}

void NewsAPIClient::initialize(const std::string& marketauxKey,
                              const std::string& finnhubKey,
                              const std::string& newsdataKey) {
    std::lock_guard<std::mutex> lock(apiMutex_);

    // Initialize Marketaux
    apiCredentials_[NewsAPIProvider::MARKETAUX] = {
        marketauxKey,
        "https://api.marketaux.com/v1",
        1000, // 1000 calls per hour
        1000, // Start with full quota
        std::chrono::system_clock::now() + std::chrono::hours(1),
        true,
        0.0,
        0
    };

    // Initialize Finnhub
    apiCredentials_[NewsAPIProvider::FINNHUB] = {
        finnhubKey,
        "https://finnhub.io/api/v1",
        60, // 60 calls per minute for free tier
        60,
        std::chrono::system_clock::now() + std::chrono::minutes(1),
        true,
        0.0,
        0
    };

    // Initialize NewsData.io
    apiCredentials_[NewsAPIProvider::NEWSDATA] = {
        newsdataKey,
        "https://newsdata.io/api/1",
        200, // 200 calls per day for free tier
        200,
        std::chrono::system_clock::now() + std::chrono::hours(24),
        true,
        0.0,
        0
    };

    Logger::getInstance().info("NewsAPIClient initialized with " +
                              std::to_string(apiCredentials_.size()) + " API providers");
}

void NewsAPIClient::setTimeout(int seconds) {
    timeout_ = seconds;
    Logger::getInstance().info("NewsAPIClient timeout set to " + std::to_string(timeout_) + " seconds");
}

std::vector<Models::NewsEvent> NewsAPIClient::fetchLatestNews(const NewsFilter& filter) {
    // Check cache first
    std::string cacheKey = getCacheKey("latest_news", filter);
    auto cachedNews = getCachedNews(cacheKey);
    if (!cachedNews.empty()) {
        Logger::getInstance().info("Returning " + std::to_string(cachedNews.size()) + " cached news items");
        return cachedNews;
    }

    std::vector<Models::NewsEvent> allNews;

    // Try APIs in order of preference based on remaining quota and health
    NewsAPIProvider selectedProvider = selectBestProvider();

    try {
        switch (selectedProvider) {
            case NewsAPIProvider::MARKETAUX:
                allNews = fetchFromMarketaux(filter);
                break;
            case NewsAPIProvider::FINNHUB:
                allNews = fetchFromFinnhub(filter);
                break;
            case NewsAPIProvider::NEWSDATA:
                allNews = fetchFromNewsdata(filter);
                break;
            case NewsAPIProvider::FALLBACK_CACHE:
                // Return any cached data we have, even if expired
                allNews = getCachedNews(cacheKey);
                Logger::warn("All APIs unavailable, using fallback cache");
                break;
        }

        // Cache the results if we got fresh data
        if (!allNews.empty() && selectedProvider != NewsAPIProvider::FALLBACK_CACHE) {
            cacheNews(cacheKey, allNews, NEWS_CACHE_TTL);
        }

        // Sort by release time (newest first) and impact
        std::sort(allNews.begin(), allNews.end(), [](const Models::NewsEvent& a, const Models::NewsEvent& b) {
            if (a.impact != b.impact) {
                return static_cast<int>(a.impact) > static_cast<int>(b.impact);
            }
            return a.releaseTime.toSecsSinceEpoch() > b.releaseTime.toSecsSinceEpoch();
        });

        Logger::getInstance().info("Fetched " + std::to_string(allNews.size()) +
                                  " news items from " + providerToString(selectedProvider));

    } catch (const std::exception& e) {
        Logger::getInstance().error("Error fetching news: " + std::string(e.what()));
        handleAPIError(selectedProvider, e.what());

        // Try fallback cache
        allNews = getCachedNews(cacheKey);
    }

    return allNews;
}

std::vector<Models::NewsEvent> NewsAPIClient::fetchNewsBySymbols(const std::vector<std::string>& symbols) {
    NewsFilter filter;
    filter.symbols = symbols;
    filter.minImpact = Models::NewsImpact::MEDIUM; // Focus on medium+ impact for symbol-specific news

    return fetchLatestNews(filter);
}

Models::NewsAnalysis NewsAPIClient::fetchSentimentAnalysis(const std::string& symbol) {
    // Check cache first
    auto cachedSentiment = getCachedSentiment(symbol);
    if (cachedSentiment.sentimentScore != 0.0) { // Assuming 0.0 means no cached data
        Logger::getInstance().info("Returning cached sentiment for " + symbol);
        return cachedSentiment;
    }

    Models::NewsAnalysis analysis;

    try {
        // Finnhub has the best sentiment analysis API
        if (hasRemainingQuota(NewsAPIProvider::FINNHUB)) {
            analysis = fetchSentimentFromFinnhub(symbol);
            cacheSentiment(symbol, analysis);
        } else {
            Logger::warn("Finnhub quota exhausted, using fallback sentiment calculation");

            // Fallback: analyze recent news for the symbol
            NewsFilter filter;
            filter.symbols = {symbol};
            auto recentNews = fetchLatestNews(filter);

            if (!recentNews.empty()) {
                double totalSentiment = 0.0;
                int count = 0;

                for (const auto& news : recentNews) {
                    double sentiment = calculateSentimentScore(news.title, news.description);
                    totalSentiment += sentiment;
                    count++;
                }

                analysis.sentimentScore = count > 0 ? totalSentiment / count : 0.0;
                analysis.marketImpact = 0.5; // Default moderate impact
                analysis.volatilityExpectation = std::abs(analysis.sentimentScore) * 0.8;
                analysis.isTradingRecommended = std::abs(analysis.sentimentScore) > 0.3;
            }
        }
    } catch (const std::exception& e) {
        Logger::getInstance().error("Error fetching sentiment for " + symbol + ": " + e.what());
    }

    return analysis;
}

NewsAPIProvider NewsAPIClient::selectBestProvider() const {
    std::lock_guard<std::mutex> lock(apiMutex_);

    // Priority order: Marketaux (most comprehensive) -> Finnhub (good sentiment) -> NewsData (backup)
    std::vector<NewsAPIProvider> providers = {
        NewsAPIProvider::MARKETAUX,
        NewsAPIProvider::FINNHUB,
        NewsAPIProvider::NEWSDATA
    };

    for (auto provider : providers) {
        auto it = apiCredentials_.find(provider);
        if (it != apiCredentials_.end() &&
            it->second.isActive &&
            hasRemainingQuota(provider) &&
            it->second.errorCount < 5) { // Skip if too many recent errors
            return provider;
        }
    }

    Logger::warn("No healthy API providers available, using fallback cache");
    return NewsAPIProvider::FALLBACK_CACHE;
}

bool NewsAPIClient::hasRemainingQuota(NewsAPIProvider provider) const {
    auto it = apiCredentials_.find(provider);
    if (it == apiCredentials_.end()) return false;

    const auto& creds = it->second;
    auto now = std::chrono::system_clock::now();

    // Check if quota has reset
    if (now >= creds.resetTime) {
        // Reset quota (this is a const method, so we can't modify here)
        // The actual reset will happen in updateAPIUsage
        return true;
    }

    return creds.remainingCalls > 0;
}

std::vector<Models::NewsEvent> NewsAPIClient::fetchFromMarketaux(const NewsFilter& filter) {
    std::string url = buildRequestUrl(NewsAPIProvider::MARKETAUX, filter);

    auto startTime = std::chrono::high_resolution_clock::now();
    std::string response = makeRequest(NewsAPIProvider::MARKETAUX, url);
    auto endTime = std::chrono::high_resolution_clock::now();

    double responseTime = std::chrono::duration<double, std::milli>(endTime - startTime).count();
    updateAPIUsage(NewsAPIProvider::MARKETAUX, !response.empty(), responseTime);

    if (response.empty()) {
        throw std::runtime_error("Empty response from Marketaux API");
    }

    return normalizeNewsData(response, NewsAPIProvider::MARKETAUX);
}

std::vector<Models::NewsEvent> NewsAPIClient::fetchFromFinnhub(const NewsFilter& filter) {
    std::string url = buildRequestUrl(NewsAPIProvider::FINNHUB, filter);

    auto startTime = std::chrono::high_resolution_clock::now();
    std::string response = makeRequest(NewsAPIProvider::FINNHUB, url);
    auto endTime = std::chrono::high_resolution_clock::now();

    double responseTime = std::chrono::duration<double, std::milli>(endTime - startTime).count();
    updateAPIUsage(NewsAPIProvider::FINNHUB, !response.empty(), responseTime);

    if (response.empty()) {
        throw std::runtime_error("Empty response from Finnhub API");
    }

    return normalizeNewsData(response, NewsAPIProvider::FINNHUB);
}

std::vector<Models::NewsEvent> NewsAPIClient::fetchFromNewsdata(const NewsFilter& filter) {
    std::string url = buildRequestUrl(NewsAPIProvider::NEWSDATA, filter);

    auto startTime = std::chrono::high_resolution_clock::now();
    std::string response = makeRequest(NewsAPIProvider::NEWSDATA, url);
    auto endTime = std::chrono::high_resolution_clock::now();

    double responseTime = std::chrono::duration<double, std::milli>(endTime - startTime).count();
    updateAPIUsage(NewsAPIProvider::NEWSDATA, !response.empty(), responseTime);

    if (response.empty()) {
        throw std::runtime_error("Empty response from NewsData API");
    }

    return normalizeNewsData(response, NewsAPIProvider::NEWSDATA);
}

std::string NewsAPIClient::buildRequestUrl(NewsAPIProvider provider, const NewsFilter& filter) {
    auto it = apiCredentials_.find(provider);
    if (it == apiCredentials_.end()) {
        throw std::runtime_error("API credentials not found for provider");
    }

    const auto& creds = it->second;
    std::string url = creds.baseUrl;

    switch (provider) {
        case NewsAPIProvider::MARKETAUX: {
            url += "/news/all?";
            url += "api_token=" + creds.apiKey;
            url += "&language=en";
            url += "&filter_entities=true";
            url += "&limit=50";

            if (!filter.symbols.empty()) {
                url += "&symbols=";
                for (size_t i = 0; i < filter.symbols.size(); ++i) {
                    if (i > 0) url += ",";
                    url += filter.symbols[i];
                }
            }
            break;
        }

        case NewsAPIProvider::FINNHUB: {
            if (!filter.symbols.empty()) {
                // Company news endpoint
                url += "/company-news?";
                url += "token=" + creds.apiKey;
                url += "&symbol=" + filter.symbols[0]; // Finnhub takes one symbol at a time

                // Add date range
                auto now = std::chrono::system_clock::now();
                auto yesterday = now - std::chrono::hours(24);
                auto time_t_now = std::chrono::system_clock::to_time_t(now);
                auto time_t_yesterday = std::chrono::system_clock::to_time_t(yesterday);

                std::tm* tm_now = std::gmtime(&time_t_now);
                std::tm* tm_yesterday = std::gmtime(&time_t_yesterday);

                char dateBuffer[11];
                std::strftime(dateBuffer, sizeof(dateBuffer), "%Y-%m-%d", tm_yesterday);
                url += "&from=" + std::string(dateBuffer);

                std::strftime(dateBuffer, sizeof(dateBuffer), "%Y-%m-%d", tm_now);
                url += "&to=" + std::string(dateBuffer);
            } else {
                // General news endpoint
                url += "/news?";
                url += "token=" + creds.apiKey;
                url += "&category=general";
            }
            break;
        }

        case NewsAPIProvider::NEWSDATA: {
            url += "/news?";
            url += "apikey=" + creds.apiKey;
            url += "&language=en";
            url += "&category=business";
            url += "&size=50";

            if (!filter.symbols.empty() || !filter.keywords.empty()) {
                url += "&q=";
                std::string query;

                for (const auto& symbol : filter.symbols) {
                    if (!query.empty()) query += " OR ";
                    query += symbol;
                }

                for (const auto& keyword : filter.keywords) {
                    if (!query.empty()) query += " OR ";
                    query += keyword;
                }

                url += query;
            }
            break;
        }

        default:
            throw std::runtime_error("Unknown API provider");
    }

    return url;
}

std::vector<Models::NewsEvent> NewsAPIClient::fetchEconomicCalendar() {
    // Check cache first
    std::string cacheKey = getCacheKey("economic_calendar");
    auto cachedEvents = getCachedNews(cacheKey);
    if (!cachedEvents.empty()) {
        Logger::getInstance().info("Returning " + std::to_string(cachedEvents.size()) + " cached economic events");
        return cachedEvents;
    }

    std::vector<Models::NewsEvent> events;
    NewsAPIProvider selectedProvider = selectBestProvider();

    try {
        // Marketaux has the best economic calendar
        if (selectedProvider == NewsAPIProvider::MARKETAUX ||
            (selectedProvider == NewsAPIProvider::FALLBACK_CACHE && hasRemainingQuota(NewsAPIProvider::MARKETAUX))) {

            auto it = apiCredentials_.find(NewsAPIProvider::MARKETAUX);
            if (it != apiCredentials_.end()) {
                std::string url = it->second.baseUrl + "/calendar?api_token=" + it->second.apiKey + "&language=en";

                auto startTime = std::chrono::high_resolution_clock::now();
                std::string response = makeRequest(NewsAPIProvider::MARKETAUX, url);
                auto endTime = std::chrono::high_resolution_clock::now();

                double responseTime = std::chrono::duration<double, std::milli>(endTime - startTime).count();
                updateAPIUsage(NewsAPIProvider::MARKETAUX, !response.empty(), responseTime);

                if (!response.empty()) {
                    events = normalizeNewsData(response, NewsAPIProvider::MARKETAUX);
                    cacheNews(cacheKey, events, ECONOMIC_CACHE_TTL);
                }
            }
        }

        if (events.empty()) {
            // Fallback to cached data
            events = getCachedNews(cacheKey);
            Logger::warn("Economic calendar API unavailable, using cached data");
        }

        Logger::getInstance().info("Fetched " + std::to_string(events.size()) + " economic events");

    } catch (const std::exception& e) {
        Logger::getInstance().error("Error fetching economic calendar: " + std::string(e.what()));
        events = getCachedNews(cacheKey); // Try cached data as fallback
    }

    return events;
}

// Implementation of data normalization methods
std::vector<Models::NewsEvent> NewsAPIClient::normalizeNewsData(const std::string& jsonResponse, NewsAPIProvider provider) {
    std::vector<Models::NewsEvent> events;

    try {
        nlohmann::json json = nlohmann::json::parse(jsonResponse);

        switch (provider) {
            case NewsAPIProvider::MARKETAUX: {
                if (json.contains("data") && json["data"].is_array()) {
                    for (const auto& item : json["data"]) {
                        Models::NewsEvent event;

                        event.title = item.value("title", "");
                        event.description = item.value("description", "");
                        event.source = item.value("source", "Marketaux");
                        event.symbol = "";

                        // Extract symbols from entities
                        if (item.contains("entities") && item["entities"].is_array()) {
                            for (const auto& entity : item["entities"]) {
                                if (entity.contains("symbol")) {
                                    if (event.symbol.empty()) {
                                        event.symbol = entity["symbol"];
                                    }
                                    event.relatedSymbols.push_back(entity["symbol"]);
                                }
                            }
                        }

                        // Parse date
                        if (item.contains("published_at")) {
                            std::string dateStr = item["published_at"];
                            event.releaseTime = parseISO8601Date(dateStr);
                        } else {
                            event.releaseTime = DATETIME_TYPE::currentDateTime();
                        }

                        // Set impact based on relevance score
                        double relevanceScore = item.value("relevance_score", 0.0);
                        if (relevanceScore > 80) {
                            event.impact = Models::NewsImpact::HIGH;
                        } else if (relevanceScore > 50) {
                            event.impact = Models::NewsImpact::MEDIUM;
                        } else {
                            event.impact = Models::NewsImpact::LOW;
                        }

                        event.isProcessed = false;
                        event.forecast = 0.0;
                        event.previous = 0.0;
                        event.actual = 0.0;

                        events.push_back(event);
                    }
                }
                break;
            }

            case NewsAPIProvider::FINNHUB: {
                if (json.is_array()) {
                    for (const auto& item : json) {
                        Models::NewsEvent event;

                        event.title = item.value("headline", "");
                        event.description = item.value("summary", "");
                        event.source = item.value("source", "Finnhub");
                        event.symbol = item.value("symbol", "");

                        // Parse Unix timestamp
                        if (item.contains("datetime")) {
                            std::time_t timestamp = item["datetime"];
                            event.releaseTime = DATETIME_TYPE::fromTime_t(timestamp);
                        } else {
                            event.releaseTime = DATETIME_TYPE::currentDateTime();
                        }

                        event.impact = Models::NewsImpact::MEDIUM; // Default for Finnhub
                        event.isProcessed = false;
                        event.forecast = 0.0;
                        event.previous = 0.0;
                        event.actual = 0.0;

                        events.push_back(event);
                    }
                }
                break;
            }

            case NewsAPIProvider::NEWSDATA: {
                if (json.contains("results") && json["results"].is_array()) {
                    for (const auto& item : json["results"]) {
                        Models::NewsEvent event;

                        event.title = item.value("title", "");
                        event.description = item.value("description", "");
                        event.source = item.value("source_id", "NewsData");
                        event.symbol = "";

                        // Parse date
                        if (item.contains("pubDate")) {
                            std::string dateStr = item["pubDate"];
                            event.releaseTime = parseISO8601Date(dateStr);
                        } else {
                            event.releaseTime = DATETIME_TYPE::currentDateTime();
                        }

                        event.impact = Models::NewsImpact::MEDIUM; // Default for NewsData
                        event.isProcessed = false;
                        event.forecast = 0.0;
                        event.previous = 0.0;
                        event.actual = 0.0;

                        events.push_back(event);
                    }
                }
                break;
            }

            default:
                Logger::warn("Unknown provider for data normalization");
                break;
        }

    } catch (const std::exception& e) {
        Logger::getInstance().error("Error normalizing news data: " + std::string(e.what()));
    }

    return events;
}

std::string NewsAPIClient::makeRequest(NewsAPIProvider provider, const std::string& url) {
    enforceRateLimit(provider);

    CURL* curl = curl_easy_init();
    std::string responseData;

    if (curl) {
        // Set URL
        curl_easy_setopt(curl, CURLOPT_URL, url.c_str());

        // Set write callback
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, &responseData);

        // Set timeout
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, timeout_);

        // Set headers
        struct curl_slist* headerList = nullptr;
        for (const auto& header : headers_) {
            headerList = curl_slist_append(headerList, header.c_str());
        }
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headerList);

        // Perform the request
        CURLcode res = curl_easy_perform(curl);

        // Check for errors
        if (res != CURLE_OK) {
            std::string error = "CURL error: " + std::string(curl_easy_strerror(res));
            Logger::getInstance().error(error);
            handleAPIError(provider, error);
        }

        // Clean up
        curl_slist_free_all(headerList);
        curl_easy_cleanup(curl);

        // Update last fetch time
        lastFetchTime_ = std::time(nullptr);
    }

    return responseData;
}

void NewsAPIClient::updateAPIUsage(NewsAPIProvider provider, bool success, double responseTimeMs) {
    std::lock_guard<std::mutex> lock(apiMutex_);

    auto it = apiCredentials_.find(provider);
    if (it == apiCredentials_.end()) return;

    auto& creds = it->second;
    auto now = std::chrono::system_clock::now();

    // Reset quota if time has passed
    if (now >= creds.resetTime) {
        creds.remainingCalls = creds.rateLimitPerHour;

        // Set next reset time based on provider
        switch (provider) {
            case NewsAPIProvider::FINNHUB:
                creds.resetTime = now + std::chrono::minutes(1);
                break;
            case NewsAPIProvider::NEWSDATA:
                creds.resetTime = now + std::chrono::hours(24);
                break;
            default:
                creds.resetTime = now + std::chrono::hours(1);
                break;
        }
    }

    // Update usage
    if (success) {
        creds.remainingCalls = std::max(0, creds.remainingCalls - 1);
        creds.errorCount = std::max(0, creds.errorCount - 1); // Reduce error count on success
    } else {
        creds.errorCount++;
    }

    // Update response time (moving average)
    if (responseTimeMs > 0) {
        creds.responseTimeMs = (creds.responseTimeMs * 0.8) + (responseTimeMs * 0.2);
    }

    Logger::getInstance().info("API usage updated for " + providerToString(provider) +
                              ": " + std::to_string(creds.remainingCalls) + " calls remaining");
}

void NewsAPIClient::enforceRateLimit(NewsAPIProvider provider) {
    std::lock_guard<std::mutex> lock(apiMutex_);

    auto it = apiCredentials_.find(provider);
    if (it == apiCredentials_.end()) return;

    const auto& creds = it->second;

    // Calculate minimum delay between requests based on rate limit
    int delayMs = 0;
    switch (provider) {
        case NewsAPIProvider::FINNHUB:
            delayMs = 1000; // 1 second between requests (60/minute)
            break;
        case NewsAPIProvider::NEWSDATA:
            delayMs = 500; // 0.5 seconds (conservative)
            break;
        case NewsAPIProvider::MARKETAUX:
            delayMs = 100; // 0.1 seconds (1000/hour is generous)
            break;
        default:
            delayMs = 1000;
            break;
    }

    std::time_t currentTime = std::time(nullptr);
    std::time_t timeSinceLastFetch = currentTime - lastFetchTime_;

    if (lastFetchTime_ > 0 && timeSinceLastFetch < delayMs / 1000) {
        int waitTime = delayMs / 1000 - timeSinceLastFetch;
        if (waitTime > 0) {
            Logger::getInstance().info("Rate limiting " + providerToString(provider) +
                                      ": waiting " + std::to_string(waitTime) + " seconds");
            std::this_thread::sleep_for(std::chrono::seconds(waitTime));
        }
    }
}

// Utility and helper methods
std::string NewsAPIClient::providerToString(NewsAPIProvider provider) const {
    switch (provider) {
        case NewsAPIProvider::MARKETAUX: return "Marketaux";
        case NewsAPIProvider::FINNHUB: return "Finnhub";
        case NewsAPIProvider::NEWSDATA: return "NewsData";
        case NewsAPIProvider::FALLBACK_CACHE: return "Cache";
        default: return "Unknown";
    }
}

void NewsAPIClient::handleAPIError(NewsAPIProvider provider, const std::string& error) {
    std::lock_guard<std::mutex> lock(apiMutex_);

    auto it = apiCredentials_.find(provider);
    if (it != apiCredentials_.end()) {
        it->second.errorCount++;

        // Disable provider if too many errors
        if (it->second.errorCount >= 10) {
            it->second.isActive = false;
            Logger::warn("Disabled " + providerToString(provider) +
                                        " due to excessive errors");
        }
    }

    Logger::getInstance().error("API Error [" + providerToString(provider) + "]: " + error);
}

std::string NewsAPIClient::getCacheKey(const std::string& operation, const NewsFilter& filter) {
    std::string key = "news:" + operation;

    if (!filter.symbols.empty()) {
        key += ":symbols:";
        for (size_t i = 0; i < filter.symbols.size(); ++i) {
            if (i > 0) key += ",";
            key += filter.symbols[i];
        }
    }

    if (!filter.keywords.empty()) {
        key += ":keywords:";
        for (size_t i = 0; i < filter.keywords.size(); ++i) {
            if (i > 0) key += ",";
            key += filter.keywords[i];
        }
    }

    key += ":impact:" + std::to_string(static_cast<int>(filter.minImpact));

    return key;
}

std::vector<Models::NewsEvent> NewsAPIClient::getCachedNews(const std::string& cacheKey) {
    std::vector<Models::NewsEvent> events;

    try {
        if (redisManager_) {
            // Try to get cached data from Redis
            // This would need to be implemented based on your Redis serialization method
            // For now, return empty vector
            Logger::getInstance().info("Checking cache for key: " + cacheKey);
        }
    } catch (const std::exception& e) {
        Logger::getInstance().error("Error reading from cache: " + std::string(e.what()));
    }

    return events;
}

void NewsAPIClient::cacheNews(const std::string& cacheKey, const std::vector<Models::NewsEvent>& news, int ttl) {
    try {
        if (redisManager_ && !news.empty()) {
            // Serialize and cache the news data
            // This would need to be implemented based on your Redis serialization method
            Logger::getInstance().info("Caching " + std::to_string(news.size()) +
                                      " news items with key: " + cacheKey +
                                      " (TTL: " + std::to_string(ttl) + "s)");
        }
    } catch (const std::exception& e) {
        Logger::getInstance().error("Error caching news: " + std::string(e.what()));
    }
}

Models::NewsAnalysis NewsAPIClient::getCachedSentiment(const std::string& symbol) {
    Models::NewsAnalysis analysis;

    try {
        if (redisManager_) {
            std::string cacheKey = "sentiment:" + symbol;
            // Try to get cached sentiment from Redis
            Logger::getInstance().info("Checking sentiment cache for: " + symbol);
        }
    } catch (const std::exception& e) {
        Logger::getInstance().error("Error reading sentiment from cache: " + std::string(e.what()));
    }

    return analysis;
}

void NewsAPIClient::cacheSentiment(const std::string& symbol, const Models::NewsAnalysis& analysis) {
    try {
        if (redisManager_) {
            std::string cacheKey = "sentiment:" + symbol;
            // Serialize and cache the sentiment data
            Logger::getInstance().info("Caching sentiment for: " + symbol);
        }
    } catch (const std::exception& e) {
        Logger::getInstance().error("Error caching sentiment: " + std::string(e.what()));
    }
}

double NewsAPIClient::calculateSentimentScore(const std::string& title, const std::string& description) const {
    // Simple sentiment analysis based on keywords
    std::vector<std::string> positiveWords = {
        "gain", "rise", "up", "increase", "growth", "profit", "bull", "surge", "rally", "boost"
    };

    std::vector<std::string> negativeWords = {
        "fall", "drop", "down", "decrease", "loss", "bear", "crash", "decline", "plunge", "sink"
    };

    std::string text = title + " " + description;
    std::transform(text.begin(), text.end(), text.begin(), ::tolower);

    int positiveCount = 0;
    int negativeCount = 0;

    for (const auto& word : positiveWords) {
        if (text.find(word) != std::string::npos) {
            positiveCount++;
        }
    }

    for (const auto& word : negativeWords) {
        if (text.find(word) != std::string::npos) {
            negativeCount++;
        }
    }

    if (positiveCount + negativeCount == 0) return 0.0;

    return (double)(positiveCount - negativeCount) / (positiveCount + negativeCount);
}

DATETIME_TYPE NewsAPIClient::parseISO8601Date(const std::string& dateStr) const {
    try {
        std::tm tm = {};

        // Parse date part (YYYY-MM-DD)
        if (dateStr.length() >= 10) {
            tm.tm_year = std::stoi(dateStr.substr(0, 4)) - 1900;
            tm.tm_mon = std::stoi(dateStr.substr(5, 2)) - 1;
            tm.tm_mday = std::stoi(dateStr.substr(8, 2));

            // Parse time part (THH:MM:SS)
            if (dateStr.length() >= 19) {
                tm.tm_hour = std::stoi(dateStr.substr(11, 2));
                tm.tm_min = std::stoi(dateStr.substr(14, 2));
                tm.tm_sec = std::stoi(dateStr.substr(17, 2));
            }

            // Convert to time_t
            std::time_t eventTime = std::mktime(&tm);
            return DATETIME_TYPE::fromTime_t(eventTime);
        }
    } catch (...) {
        // If parsing fails, use current date/time
    }

    return DATETIME_TYPE::currentDateTime();
}

// Additional missing methods implementation
Models::NewsAnalysis NewsAPIClient::fetchSentimentFromFinnhub(const std::string& symbol) {
    Models::NewsAnalysis analysis;

    try {
        auto it = apiCredentials_.find(NewsAPIProvider::FINNHUB);
        if (it == apiCredentials_.end()) {
            throw std::runtime_error("Finnhub credentials not found");
        }

        std::string url = it->second.baseUrl + "/news-sentiment?symbol=" + symbol + "&token=" + it->second.apiKey;

        auto startTime = std::chrono::high_resolution_clock::now();
        std::string response = makeRequest(NewsAPIProvider::FINNHUB, url);
        auto endTime = std::chrono::high_resolution_clock::now();

        double responseTime = std::chrono::duration<double, std::milli>(endTime - startTime).count();
        updateAPIUsage(NewsAPIProvider::FINNHUB, !response.empty(), responseTime);

        if (!response.empty()) {
            analysis = normalizeSentimentData(response, NewsAPIProvider::FINNHUB);
        }

    } catch (const std::exception& e) {
        Logger::getInstance().error("Error fetching sentiment from Finnhub: " + std::string(e.what()));
    }

    return analysis;
}

Models::NewsAnalysis NewsAPIClient::normalizeSentimentData(const std::string& jsonResponse, NewsAPIProvider provider) {
    Models::NewsAnalysis analysis;

    try {
        nlohmann::json json = nlohmann::json::parse(jsonResponse);

        switch (provider) {
            case NewsAPIProvider::FINNHUB: {
                if (json.contains("sentiment")) {
                    auto sentiment = json["sentiment"];
                    double positive = sentiment.value("positive", 0.0);
                    double negative = sentiment.value("negative", 0.0);
                    double neutral = sentiment.value("neutral", 0.0);

                    // Calculate overall sentiment score (-1 to 1)
                    analysis.sentimentScore = positive - negative;
                    analysis.marketImpact = std::abs(analysis.sentimentScore);
                    analysis.volatilityExpectation = analysis.marketImpact * 0.8;
                    analysis.isTradingRecommended = analysis.marketImpact > 0.3;

                    // Add keywords based on sentiment
                    if (positive > 0.6) analysis.keywords.push_back("bullish");
                    if (negative > 0.6) analysis.keywords.push_back("bearish");
                    if (neutral > 0.6) analysis.keywords.push_back("neutral");
                }
                break;
            }

            default:
                Logger::warn("Unknown provider for sentiment normalization");
                break;
        }

    } catch (const std::exception& e) {
        Logger::getInstance().error("Error normalizing sentiment data: " + std::string(e.what()));
    }

    return analysis;
}

std::map<NewsAPIProvider, APICredentials> NewsAPIClient::getAPIUsageStats() const {
    std::lock_guard<std::mutex> lock(apiMutex_);
    return apiCredentials_;
}

void NewsAPIClient::refreshCache() {
    try {
        if (redisManager_) {
            // Clear all news-related cache keys
            Logger::getInstance().info("Refreshing news cache");
            // Implementation would depend on your Redis setup
        }
    } catch (const std::exception& e) {
        Logger::getInstance().error("Error refreshing cache: " + std::string(e.what()));
    }
}

void NewsAPIClient::setProviderEnabled(NewsAPIProvider provider, bool enabled) {
    std::lock_guard<std::mutex> lock(apiMutex_);

    auto it = apiCredentials_.find(provider);
    if (it != apiCredentials_.end()) {
        it->second.isActive = enabled;
        Logger::getInstance().info("Provider " + providerToString(provider) +
                                  (enabled ? " enabled" : " disabled"));
    }
}

bool NewsAPIClient::isHighPriorityNews(const Models::NewsEvent& event) const {
    // Check for high-impact keywords
    std::vector<std::string> highImpactKeywords = {
        "fed", "federal reserve", "interest rate", "inflation", "gdp", "unemployment",
        "earnings", "merger", "acquisition", "bankruptcy", "ipo", "dividend",
        "oil", "gold", "bitcoin", "crypto", "war", "election", "trade war"
    };

    std::string text = event.title + " " + event.description;
    std::transform(text.begin(), text.end(), text.begin(), ::tolower);

    for (const auto& keyword : highImpactKeywords) {
        if (text.find(keyword) != std::string::npos) {
            return true;
        }
    }

    return event.impact == Models::NewsImpact::HIGH;
}

std::vector<std::string> NewsAPIClient::extractAffectedSymbols(const Models::NewsEvent& event) const {
    std::vector<std::string> symbols;

    // Add primary symbol if available
    if (!event.symbol.empty()) {
        symbols.push_back(event.symbol);
    }

    // Add related symbols
    for (const auto& symbol : event.relatedSymbols) {
        if (std::find(symbols.begin(), symbols.end(), symbol) == symbols.end()) {
            symbols.push_back(symbol);
        }
    }

    // Extract symbols from text using simple pattern matching
    std::string text = event.title + " " + event.description;
    std::regex symbolPattern(R"(\b[A-Z]{2,5}\b)"); // Match 2-5 uppercase letters
    std::sregex_iterator iter(text.begin(), text.end(), symbolPattern);
    std::sregex_iterator end;

    for (; iter != end; ++iter) {
        std::string match = iter->str();
        if (std::find(symbols.begin(), symbols.end(), match) == symbols.end()) {
            symbols.push_back(match);
        }
    }

    return symbols;
}

bool NewsAPIClient::shouldRetryRequest(NewsAPIProvider provider, int errorCode) const {
    // Retry on temporary errors
    return errorCode >= 500 && errorCode < 600; // Server errors
}

void NewsAPIClient::logAPIMetrics() const {
    std::lock_guard<std::mutex> lock(apiMutex_);

    Logger::getInstance().info("=== API Usage Metrics ===");
    for (const auto& [provider, creds] : apiCredentials_) {
        Logger::getInstance().info(providerToString(provider) + ":");
        Logger::getInstance().info("  Active: " + std::string(creds.isActive ? "Yes" : "No"));
        Logger::getInstance().info("  Remaining calls: " + std::to_string(creds.remainingCalls));
        Logger::getInstance().info("  Error count: " + std::to_string(creds.errorCount));
        Logger::getInstance().info("  Avg response time: " + std::to_string(creds.responseTimeMs) + "ms");
    }
}

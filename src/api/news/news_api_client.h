#pragma once

#include <string>
#include <vector>
#include <ctime>
#include <memory>
#include <map>
#include <chrono>
#include <mutex>
#include "../../models/news_data.h"
#include "../../database/redis_manager.h"

// Forward declarations
class RedisManager;

/**
 * @enum NewsAPIProvider
 * @brief Supported news API providers
 */
enum class NewsAPIProvider {
    MARKETAUX,
    FINNHUB,
    NEWSDATA,
    FALLBACK_CACHE
};

/**
 * @struct APICredentials
 * @brief API credentials and configuration for each provider
 */
struct APICredentials {
    std::string apiKey;
    std::string baseUrl;
    int rateLimitPerHour;
    int remainingCalls;
    std::chrono::system_clock::time_point resetTime;
    bool isActive;
    double responseTimeMs;
    int errorCount;

    APICredentials() : rateLimitPerHour(0), remainingCalls(0), isActive(true), responseTimeMs(0.0), errorCount(0) {}
};

/**
 * @struct NewsFilter
 * @brief Filtering criteria for news fetching
 */
struct NewsFilter {
    std::vector<std::string> symbols;
    std::vector<std::string> keywords;
    std::vector<std::string> currencies;
    Models::NewsImpact minImpact;
    std::chrono::system_clock::time_point fromDate;
    std::chrono::system_clock::time_point toDate;
    bool includeEconomicEvents;
    bool includeSentiment;

    NewsFilter() : minImpact(Models::NewsImpact::LOW), includeEconomicEvents(true), includeSentiment(true) {}
};

/**
 * @class NewsAPIClient
 * @brief Multi-provider news client with caching and intelligent rotation
 */
class NewsAPIClient {
public:
    /**
     * @brief Constructor with multiple API credentials
     */
    NewsAPIClient();

    /**
     * @brief Destructor
     */
    ~NewsAPIClient();

    /**
     * @brief Initialize with API credentials
     * @param marketauxKey Marketaux API key
     * @param finnhubKey Finnhub API key
     * @param newsdataKey NewsData.io API key
     */
    void initialize(const std::string& marketauxKey,
                   const std::string& finnhubKey,
                   const std::string& newsdataKey);

    /**
     * @brief Set request timeout for all APIs
     * @param seconds Timeout in seconds
     */
    void setTimeout(int seconds);

    /**
     * @brief Fetch latest financial news with intelligent API rotation
     * @param filter Optional filtering criteria
     * @return Vector of Models::NewsEvent structures
     */
    std::vector<Models::NewsEvent> fetchLatestNews(const NewsFilter& filter = NewsFilter());

    /**
     * @brief Fetch news for specific symbols/assets
     * @param symbols Vector of trading symbols (e.g., "AAPL", "EUR/USD")
     * @return Vector of Models::NewsEvent structures
     */
    std::vector<Models::NewsEvent> fetchNewsBySymbols(const std::vector<std::string>& symbols);

    /**
     * @brief Fetch economic calendar events
     * @return Vector of Models::NewsEvent structures
     */
    std::vector<Models::NewsEvent> fetchEconomicCalendar();

    /**
     * @brief Fetch sentiment analysis for specific symbol
     * @param symbol Trading symbol
     * @return Models::NewsAnalysis with sentiment data
     */
    Models::NewsAnalysis fetchSentimentAnalysis(const std::string& symbol);

    /**
     * @brief Get current API usage statistics
     * @return Map of provider to usage stats
     */
    std::map<NewsAPIProvider, APICredentials> getAPIUsageStats() const;

    /**
     * @brief Force cache refresh for all providers
     */
    void refreshCache();

    /**
     * @brief Enable/disable specific API provider
     * @param provider API provider to toggle
     * @param enabled Enable or disable
     */
    void setProviderEnabled(NewsAPIProvider provider, bool enabled);

private:
    // API Management
    std::map<NewsAPIProvider, APICredentials> apiCredentials_;
    std::shared_ptr<RedisManager> redisManager_;
    mutable std::mutex apiMutex_;

    // Configuration
    int timeout_;
    std::vector<std::string> headers_;
    std::time_t lastFetchTime_;

    // Cache settings
    static constexpr int NEWS_CACHE_TTL = 300; // 5 minutes
    static constexpr int SENTIMENT_CACHE_TTL = 600; // 10 minutes
    static constexpr int ECONOMIC_CACHE_TTL = 3600; // 1 hour

    // Core API methods
    /**
     * @brief Select best available API provider based on usage and health
     * @return Selected provider or FALLBACK_CACHE if none available
     */
    NewsAPIProvider selectBestProvider() const;

    /**
     * @brief Make HTTP request with automatic retry and error handling
     * @param provider API provider to use
     * @param url Request URL
     * @return Response body as string
     */
    std::string makeRequest(NewsAPIProvider provider, const std::string& url);

    /**
     * @brief Update API usage statistics after request
     * @param provider API provider used
     * @param success Whether request was successful
     * @param responseTimeMs Response time in milliseconds
     */
    void updateAPIUsage(NewsAPIProvider provider, bool success, double responseTimeMs);

    /**
     * @brief Check if API provider has remaining quota
     * @param provider API provider to check
     * @return True if provider has remaining calls
     */
    bool hasRemainingQuota(NewsAPIProvider provider) const;

    // Provider-specific implementations
    std::vector<Models::NewsEvent> fetchFromMarketaux(const NewsFilter& filter);
    std::vector<Models::NewsEvent> fetchFromFinnhub(const NewsFilter& filter);
    std::vector<Models::NewsEvent> fetchFromNewsdata(const NewsFilter& filter);
    Models::NewsAnalysis fetchSentimentFromFinnhub(const std::string& symbol);

    // Data processing
    std::vector<Models::NewsEvent> normalizeNewsData(const std::string& jsonResponse, NewsAPIProvider provider);
    Models::NewsAnalysis normalizeSentimentData(const std::string& jsonResponse, NewsAPIProvider provider);
    std::string buildRequestUrl(NewsAPIProvider provider, const NewsFilter& filter);

    // Caching methods
    std::string getCacheKey(const std::string& operation, const NewsFilter& filter = NewsFilter());
    std::vector<Models::NewsEvent> getCachedNews(const std::string& cacheKey);
    void cacheNews(const std::string& cacheKey, const std::vector<Models::NewsEvent>& news, int ttl);
    Models::NewsAnalysis getCachedSentiment(const std::string& symbol);
    void cacheSentiment(const std::string& symbol, const Models::NewsAnalysis& analysis);

    // Utility methods
    void enforceRateLimit(NewsAPIProvider provider);
    std::string providerToString(NewsAPIProvider provider) const;
    bool isHighPriorityNews(const Models::NewsEvent& event) const;
    double calculateSentimentScore(const std::string& title, const std::string& description) const;
    std::vector<std::string> extractAffectedSymbols(const Models::NewsEvent& event) const;

    // Error handling
    void handleAPIError(NewsAPIProvider provider, const std::string& error);
    bool shouldRetryRequest(NewsAPIProvider provider, int errorCode) const;
    void logAPIMetrics() const;

    // Date parsing utility
    DATETIME_TYPE parseISO8601Date(const std::string& dateStr) const;
};

#ifndef QML_BRIDGE_H
#define QML_BRIDGE_H

#include <QObject>
#include <QString>
#include <QVariant>
#include <QList>
#include <QMap>
#include <QQmlEngine>
#include <QJSEngine>
#include <QTimer>
#include <QDateTime>
#include <QRandomGenerator>
#include <QDebug>
#include <memory>
#include <algorithm>
#include "../ai/patterns/pattern.h"

// Forward declarations
class BinaryOptionsBot;
class StrategyManager;
class BacktestingSystem;

namespace Terminal {

/**
 * @brief The QmlBridge class provides a bridge between C++ and QML
 *
 * This class exposes C++ data and functionality to QML, allowing
 * the QML UI to interact with the underlying C++ code.
 */
class QmlBridge : public QObject
{
    Q_OBJECT

    // Properties exposed to QML
    Q_PROPERTY(QString accountBalance READ accountBalance NOTIFY accountBalanceChanged)
    Q_PROPERTY(QString accountEquity READ accountEquity NOTIFY accountEquityChanged)
    Q_PROPERTY(QString profitLoss READ profitLoss NOTIFY profitLossChanged)
    Q_PROPERTY(bool isConnected READ isConnected NOTIFY connectionStatusChanged)
    Q_PROPERTY(QString marketStatus READ marketStatus NOTIFY marketStatusChanged)

    // User Profile Properties
    Q_PROPERTY(QString userNickname READ userNickname NOTIFY userNicknameChanged)
    Q_PROPERTY(QString userProfileId READ userProfileId NOTIFY userProfileIdChanged)
    Q_PROPERTY(QString userAvatar READ userAvatar NOTIFY userAvatarChanged)
    Q_PROPERTY(QString userCountry READ userCountry NOTIFY userCountryChanged)
    Q_PROPERTY(QString userCountryName READ userCountryName NOTIFY userCountryNameChanged)
    Q_PROPERTY(QString userCurrencyCode READ userCurrencyCode NOTIFY userCurrencyCodeChanged)
    Q_PROPERTY(QString userCurrencySymbol READ userCurrencySymbol NOTIFY userCurrencySymbolChanged)
    Q_PROPERTY(QString userDemoBalance READ userDemoBalance NOTIFY userDemoBalanceChanged)
    Q_PROPERTY(QString userLiveBalance READ userLiveBalance NOTIFY userLiveBalanceChanged)
    Q_PROPERTY(QString userTimezone READ userTimezone NOTIFY userTimezoneChanged)
    Q_PROPERTY(bool isAutoPilotActive READ isAutoPilotActive NOTIFY autoPilotStatusChanged)
    Q_PROPERTY(QVariantList indicators READ indicators NOTIFY indicatorsChanged)
    Q_PROPERTY(QVariantMap sentiment READ sentiment NOTIFY sentimentChanged)
    Q_PROPERTY(QVariantList priceData READ priceData NOTIFY priceDataChanged)
    Q_PROPERTY(QVariantList activeOrders READ activeOrders NOTIFY activeOrdersChanged)
    Q_PROPERTY(bool isLoading READ isLoading NOTIFY loadingStatusChanged)
    Q_PROPERTY(QString errorMessage READ errorMessage NOTIFY errorMessageChanged)
    Q_PROPERTY(int currentPhase READ currentPhase WRITE setCurrentPhase NOTIFY currentPhaseChanged)
    Q_PROPERTY(QVariantList availableTradingPairs READ availableTradingPairs NOTIFY availableTradingPairsChanged)
    Q_PROPERTY(QString currentTradingPair READ currentTradingPair WRITE setCurrentTradingPair NOTIFY currentTradingPairChanged)

    // Strategy Testing Properties
    Q_PROPERTY(QVariantList strategyTestResults READ strategyTestResults NOTIFY strategyTestResultsChanged)
    Q_PROPERTY(bool isStrategyTesting READ isStrategyTesting NOTIFY isStrategyTestingChanged)
    Q_PROPERTY(QString currentTestingStrategy READ currentTestingStrategy NOTIFY currentTestingStrategyChanged)
    Q_PROPERTY(double overallWinRate READ overallWinRate NOTIFY overallWinRateChanged)
    Q_PROPERTY(QVariantList phaseDescriptions READ phaseDescriptions NOTIFY phaseDescriptionsChanged)
    Q_PROPERTY(bool autoPhaseMode READ autoPhaseMode WRITE setAutoPhaseMode NOTIFY autoPhaseChanged)
    Q_PROPERTY(int phaseRepetitions READ phaseRepetitions WRITE setPhaseRepetitions NOTIFY phaseRepetitionsChanged)
    Q_PROPERTY(double phaseProgress READ phaseProgress NOTIFY phaseProgressChanged)
    Q_PROPERTY(int completedPhases READ completedPhases NOTIFY completedPhasesChanged)
    Q_PROPERTY(QVariantList detectedPatterns READ detectedPatterns NOTIFY detectedPatternsChanged)
    Q_PROPERTY(double launchTime READ launchTime NOTIFY launchTimeChanged)

    // News Signal Strength Properties
    Q_PROPERTY(QString topNewsHeadline READ topNewsHeadline NOTIFY topNewsHeadlineChanged)
    Q_PROPERTY(double newsSentimentScore READ newsSentimentScore NOTIFY newsSentimentScoreChanged)
    Q_PROPERTY(QString newsSentimentText READ newsSentimentText NOTIFY newsSentimentTextChanged)
    Q_PROPERTY(QString newsSentimentColor READ newsSentimentColor NOTIFY newsSentimentColorChanged)
    Q_PROPERTY(double newsConfidenceLevel READ newsConfidenceLevel NOTIFY newsConfidenceLevelChanged)
    Q_PROPERTY(QString newsSource READ newsSource NOTIFY newsSourceChanged)
    Q_PROPERTY(bool hasActiveNews READ hasActiveNews NOTIFY hasActiveNewsChanged)

public:
    /**
     * @brief Singleton instance accessor
     * @return Reference to the singleton instance
     */
    static QmlBridge& instance();

    /**
     * @brief QML singleton provider
     * @param engine QML engine
     * @param scriptEngine JavaScript engine
     * @return QML singleton instance
     */
    static QObject* qmlSingletonProvider(QQmlEngine* engine, QJSEngine* scriptEngine);

    // Property getters
    QString accountBalance() const;
    QString accountEquity() const;
    QString profitLoss() const;
    bool isConnected() const;
    QString marketStatus() const;

    // User Profile Property getters
    QString userNickname() const;
    QString userProfileId() const;
    QString userAvatar() const;
    QString userCountry() const;
    QString userCountryName() const;
    QString userCurrencyCode() const;
    QString userCurrencySymbol() const;
    QString userDemoBalance() const;
    QString userLiveBalance() const;
    QString userTimezone() const;

    bool isAutoPilotActive() const;
    QVariantList indicators() const;
    QVariantMap sentiment() const;
    QVariantList priceData() const;
    QVariantList activeOrders() const;
    bool isLoading() const;
    QString errorMessage() const;
    int currentPhase() const;
    void setCurrentPhase(int phase);
    QVariantList availableTradingPairs() const;
    QString currentTradingPair() const;
    void setCurrentTradingPair(const QString& pair);

    // Strategy Testing Property getters
    QVariantList strategyTestResults() const;
    bool isStrategyTesting() const;
    QString currentTestingStrategy() const;
    double overallWinRate() const;
    QVariantList phaseDescriptions() const;
    bool autoPhaseMode() const;
    void setAutoPhaseMode(bool enabled);
    int phaseRepetitions() const;
    void setPhaseRepetitions(int repetitions);
    double phaseProgress() const;
    int completedPhases() const;
    QVariantList detectedPatterns() const;
    double launchTime() const;

    // News Signal Strength Property getters
    QString topNewsHeadline() const;
    double newsSentimentScore() const;
    QString newsSentimentText() const;
    QString newsSentimentColor() const;
    double newsConfidenceLevel() const;
    QString newsSource() const;
    bool hasActiveNews() const;

public slots:
    // Actions that can be triggered from QML
    void connect();
    void disconnect();
    void placeTrade(const QString& asset, double amount, const QString& direction, int expiryMinutes);
    void toggleAutoPilot(bool enabled);
    void showSettings();
    void refreshData();
    void addIndicator(const QString& name);
    void removeIndicator(const QString& name);
    void updateChartTimeframe(const QString& timeframe);

    // Bot control methods
    void startBot();
    void pauseBot();
    void stopBot();

    // Trading mode control
    void setTradingMode(const QString& mode);

    // Bot connection
    void setBinaryOptionsBot(std::shared_ptr<BinaryOptionsBot> bot);

    // Safe bot release (call before destroying the bot)
    Q_INVOKABLE void releaseBotReference();

    // PIN code submission for PyQuotex authentication
    Q_INVOKABLE bool submitPinCode(const QString& pinCode);

    // AI Pattern detection
    void detectAIPatterns();
    Q_INVOKABLE void fetchRealCandlestickData(const QString& asset = "EURUSD_otc", int period = 300, int count = 50);
    Q_INVOKABLE void triggerPatternDetection(); // Manual trigger for testing
    Q_INVOKABLE bool connectPyQuotexAPI(); // Manual API connection trigger
    Q_INVOKABLE QString getBestAvailableAsset(); // Get the best currently open asset for trading
    Q_INVOKABLE void updateTradingPairs(); // Manual refresh of trading pairs from API

    // Strategy Testing System
    Q_INVOKABLE void testAllStrategies(); // Test all available strategies
    Q_INVOKABLE void testSpecificStrategy(const QString& strategyName); // Test a specific strategy
    Q_INVOKABLE QVariantList getAvailableStrategies(); // Get list of all available strategies
    Q_INVOKABLE QVariantMap getStrategyPerformance(const QString& strategyName); // Get performance metrics for a strategy
    Q_INVOKABLE void resetStrategyTests(); // Reset all strategy test results

    // AI + Bot Performance Testing System
    Q_INVOKABLE void runPerformanceTest(); // Comprehensive AI + Bot performance test

    // Loading state control
    Q_INVOKABLE void setLoadingState(bool isLoading);

    // Direct property setters for manual updates
    Q_INVOKABLE void setConnected(bool connected);
    Q_INVOKABLE void setAccountBalance(const QString& balance);
    Q_INVOKABLE void setMarketStatus(const QString& status);
    Q_INVOKABLE void setAutoPilotActive(bool active);
    Q_INVOKABLE void setSentiment(const QVariantMap& sentiment);
    Q_INVOKABLE void setPriceData(const QVariantList& priceData);
    Q_INVOKABLE void setLaunchTime(double seconds);

signals:
    // Signals to notify QML of property changes
    void accountBalanceChanged();
    void accountEquityChanged();
    void profitLossChanged();
    void connectionStatusChanged();
    void marketStatusChanged();

    // User Profile Signals
    void userNicknameChanged();
    void userProfileIdChanged();
    void userAvatarChanged();
    void userCountryChanged();
    void userCountryNameChanged();
    void userCurrencyCodeChanged();
    void userCurrencySymbolChanged();
    void userDemoBalanceChanged();
    void userLiveBalanceChanged();
    void userTimezoneChanged();

    void autoPilotStatusChanged();
    void indicatorsChanged();
    void sentimentChanged();
    void priceDataChanged();
    void activeOrdersChanged();
    void loadingStatusChanged();
    void errorMessageChanged();
    void currentPhaseChanged();
    void availableTradingPairsChanged();
    void currentTradingPairChanged();

    // Strategy Testing Signals
    void strategyTestResultsChanged();
    void isStrategyTestingChanged();
    void currentTestingStrategyChanged();
    void overallWinRateChanged();
    void strategyTestCompleted(const QString& strategyName, double winRate, const QVariantMap& metrics);
    void allStrategiesTestCompleted(double overallWinRate);

    // AI + Bot Performance Test Signals
    void performanceTestCompleted(const QVariantMap& results);
    void phaseDescriptionsChanged();
    void autoPhaseChanged();
    void phaseRepetitionsChanged();
    void phaseProgressChanged();
    void completedPhasesChanged();
    void detectedPatternsChanged();
    void launchTimeChanged();

    // News Signal Strength Signals
    void topNewsHeadlineChanged();
    void newsSentimentScoreChanged();
    void newsSentimentTextChanged();
    void newsSentimentColorChanged();
    void newsConfidenceLevelChanged();
    void newsSourceChanged();
    void hasActiveNewsChanged();

    // Signals for notifications
    void notification(const QString& message, const QString& type);
    void tradeResult(bool success, const QString& message);

    // Signal for connection status updates
    void connectionStatusUpdate(const QString& status, bool isError);

private:
    // Private constructor for singleton
    explicit QmlBridge(QObject* parent = nullptr);

    // Destructor
    ~QmlBridge();

    // Prevent copying
    QmlBridge(const QmlBridge&) = delete;
    QmlBridge& operator=(const QmlBridge&) = delete;

    // Internal data
    QString m_accountBalance;
    QString m_accountEquity;
    QString m_profitLoss;
    bool m_isConnected;
    QString m_marketStatus;

    // User Profile Data
    QString m_userNickname;
    QString m_userProfileId;
    QString m_userAvatar;
    QString m_userCountry;
    QString m_userCountryName;
    QString m_userCurrencyCode;
    QString m_userCurrencySymbol;
    QString m_userDemoBalance;
    QString m_userLiveBalance;
    QString m_userTimezone;

    bool m_isAutoPilotActive;
    QVariantList m_indicators;
    QVariantMap m_sentiment;
    QVariantList m_priceData;
    QVariantList m_activeOrders;
    bool m_isLoading;
    QString m_errorMessage;
    int m_currentPhase;
    QVariantList m_availableTradingPairs;
    QString m_currentTradingPair;

    // Strategy Testing
    QVariantList m_strategyTestResults;
    bool m_isStrategyTesting;
    QString m_currentTestingStrategy;
    double m_overallWinRate;

    // Private helper methods
    QVariantMap testStrategy(const QString& strategyName);
    QVariantList getRealAvailableStrategies(); // Get strategies from actual bot
    QVariantMap testRealStrategy(const QString& strategyName); // Test actual strategy implementation

    // Strategy performance logging methods
    void logStrategyTestResult(const QVariantMap& result);
    void logAllStrategiesTest(const QVariantList& results, double overallWinRate);
    QVariantList m_phaseDescriptions;
    bool m_autoPhaseMode;
    int m_phaseRepetitions;
    double m_phaseProgress;
    int m_completedPhases;
    int m_currentPhaseRepetition;
    QTimer* m_phaseTimer;
    QVariantList m_detectedPatterns;
    double m_launchTime;

    // News Signal Strength Data
    QString m_topNewsHeadline;
    double m_newsSentimentScore;
    QString m_newsSentimentText;
    QString m_newsSentimentColor;
    double m_newsConfidenceLevel;
    QString m_newsSource;
    bool m_hasActiveNews;

    // Flag to prevent signal emission during destruction
    bool m_isDestroying;

    // Candle accumulation for pattern detection
    std::vector<Models::CandleData> m_accumulatedCandles;
    static const size_t MIN_CANDLES_FOR_PATTERNS = 5;  // Reduced from 20 to 5 for faster pattern detection
    static const size_t MAX_CANDLES_BUFFER = 100;

    // Current timeframe for pattern detection
    QString m_currentTimeframe;

    // Reference to the BinaryOptionsBot
    std::shared_ptr<BinaryOptionsBot> m_bot;

    // Timer for automatic reconnection
    QTimer* m_reconnectTimer;

    // Timer for automatic AI pattern detection
    QTimer* m_patternDetectionTimer;

    // Internal methods
    void updateAccountInfo();
    void updateUserProfile();
    void updateNewsSignalStrength();
    void updateMarketData();
    void updateSentimentData();
    void updateOrderData();
    void updatePhaseProgress();
    void advanceToNextPhase();
    void generateSyntheticMarketData();
    void generateSyntheticPatterns();
    void processDetectedPatterns(const std::vector<AI::Pattern>& patterns);

    // New methods for real candlestick data processing
    std::vector<Models::MarketData> convertCandlesToMarketData(const std::vector<Models::CandleData>& candleData);
    void detectPatternsFromMarketData(const std::vector<Models::MarketData>& marketData);
    void updatePriceDataFromCandles(const std::vector<Models::CandleData>& candleData);

    // Candle accumulation methods
    void accumulateCandles(const std::vector<Models::CandleData>& newCandles);
    bool hasEnoughCandlesForPatterns() const;

    // Timeframe conversion helper
    int timeframeToPeriod(const QString& timeframe) const;

    // AI + Bot Performance Test Methods
    QVariantMap testAIPatternRecognition();
    QVariantMap testBotExecutionLogic();
    QVariantMap analyzeRealVsExpectedResults();
    QVariantMap testTimingPerformance();
    QVariantMap testStrategyTraceability();
    double calculateOverallHealthScore(const QVariantMap& performanceResults);
    QVariantList generatePerformanceRecommendations(const QVariantMap& performanceResults);
    void logPerformanceTestResults(const QVariantMap& results);

    // Enhanced Pattern Generation Methods
    void generateComprehensiveSyntheticPatterns(const std::vector<Models::MarketData>& marketData);
    std::vector<Models::MarketData> generateSyntheticMarketData(const std::string& symbol, int count);

    // Audit Trail Methods
    void createPatternAuditTrail(const QVariantMap& results, const QString& logDir, const QString& timestamp);
};

} // namespace Terminal

#endif // QML_BRIDGE_H

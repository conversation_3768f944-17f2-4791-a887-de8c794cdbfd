{"ai_pattern_recognition": {"candles_analyzed": 40, "data_source": "comprehensive_synthetic", "execution_time_ms": 9, "pattern_accuracy": 74.1, "patterns_detected": 10, "status": "api_unavailable", "timeframes_analyzed": 1, "trading_pair": "EUR/USD (OTC)"}, "bot_execution": {"avg_confidence": 75, "avg_execution_time_ms": 956.8563333333333, "balance_retrieval": "success", "bot_connectivity": "connected", "bot_health": "healthy", "execution_efficiency": "needs_improvement", "historical_data_size": 0, "market_data_test": "success", "operations_tested": 4, "status": "success", "total_execution_time_ms": 2870}, "overall_health_score": 1518.9583333333333, "real_vs_expected": {"accuracy_percentage": 90.04166666666667, "avg_actual_win_rate": 70.04166666666667, "avg_expected_win_rate": 80, "performance_gap": "minor_underperformance", "status": "success", "strategies_analyzed": 12, "variance": -9.958333333333329}, "recommendations": [{"action": "Profile and optimize strategy calculation algorithms", "category": "Bot Execution", "priority": "medium", "recommendation": "Optimize strategy execution speed - taking over 50ms"}], "strategy_traceability": {"audit_trail": "limited", "decision_context": "limited", "decision_logging": "enabled", "features_with_traceability": 3, "performance_tracking": "enabled", "status": "success", "total_features": 5, "traceability_percentage": 60}, "test_type": "ai_bot_performance_test", "timestamp": "2025-05-29T15:58:49", "timing_performance": {"pattern_detection_time_ms": 0, "status": "success", "strategy_execution_time_ms": 1, "timing_grade": "excellent", "total_response_time_ms": 1}}
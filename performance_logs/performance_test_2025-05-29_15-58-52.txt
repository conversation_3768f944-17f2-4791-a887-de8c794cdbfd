========================================
🧠 AI + Bot Performance Test Report
========================================
Timestamp: 2025-05-29T15:58:49
Overall Health Score: 1519.0%
Test Type: ai_bot_performance_test

🔍 AI Pattern Recognition:
  Status: api_unavailable
  Patterns Detected: 10
  Pattern Accuracy: 74.1%
  Data Source: comprehensive_synthetic
  Trading Pair: EUR/USD (OTC)
  Candles Analyzed: 40
  Timeframes Analyzed: 1
  Execution Time: 9ms

⚡ Bot Execution Logic:
  Status: success
  Operations Tested: 4
  Average Confidence: 75.0%
  Execution Efficiency: needs_improvement
  Total Execution Time: 2870ms

📊 Real vs Expected Analysis:
  Status: success
  Strategies Analyzed: 12
  Average Actual Win Rate: 70.0%
  Average Expected Win Rate: 80.0%
  Variance: -10.0%
  Accuracy: 90.0%
  Performance Gap: minor_underperformance

⏱️ Timing Performance:
  Status: success
  Pattern Detection Time: 0ms
  Strategy Execution Time: 1ms
  Total Response Time: 1ms
  Timing Grade: excellent

🔄 Strategy Decision Traceability:
  Status: success
  Total Features: 5
  Features with Traceability: 3
  Traceability Percentage: 60.0%
  Decision Logging: enabled
  Audit Trail: limited

💡 Recommendations:
  • [MEDIUM] Bot Execution: Optimize strategy execution speed - taking over 50ms
    Action: Profile and optimize strategy calculation algorithms

========================================
